using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Ethos.Workflows.Scheduling;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Controllers;

public sealed record FindSlotsRequestDto
{
    public required Guid StudyId { get; set; }
    public required Guid CareLocationId { get; set; }
    public required DateOnly StartDate { get; set; }
    public required DateOnly EndDate { get; set; }
}

public sealed record AvailableSlotDto
{
    public required DateOnly Date { get; set; }
    public required Guid RoomId { get; set; }
    public required Guid CareLocationShiftId { get; set; }
    public required List<string> SoftConstraintViolations { get; set; }
}

public sealed record FindSlotsResponseDto
{
    public required IReadOnlyList<AvailableSlotDto> AvailableSlots { get; set; }
}

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class SchedulingController : ControllerBase
{
    private readonly AppDbContext _dbContext;
    private readonly IAppointmentScheduler _appointmentScheduler;
    
    public SchedulingController(AppDbContext dbContext, IAppointmentScheduler appointmentScheduler)
    {
        _dbContext = dbContext;
        _appointmentScheduler = appointmentScheduler;
    }
    
    [HttpPost]
    [Route("find-slots")]
    public async Task<ActionResult<FindSlotsResponseDto>> FindSlots([FromBody] FindSlotsRequestDto requestDto)
    {
        var study = _dbContext.Set<StudyDbo>()
            .Include(s => s.Order)
            .ThenInclude(o => o.Patient)
            .FirstOrDefault(s => s.Id == requestDto.StudyId);
        if (study == null) 
        {
            return NotFound($"Study with ID {requestDto.StudyId} does not exist.");
        }
        
        var careLocation = _dbContext.Set<CareLocationDbo>()
            .Include(c => c.Equipment)
            .Include(c => c.Identifiers)
            .Include(c => c.ParentProvider)
            .FirstOrDefault(c => c.Id == requestDto.CareLocationId);
        if (careLocation == null)
        {
            return NotFound($"CareLocation with ID {requestDto.CareLocationId} does not exist.");
        }

        var results = await _appointmentScheduler.FindAvailableSlotsForPatient(study, careLocation, requestDto.StartDate, requestDto.EndDate);
        
        return Ok(new FindSlotsResponseDto {
            AvailableSlots = results.AvailableSlots.Select(slot => new AvailableSlotDto
            {
                Date = slot.Date,
                RoomId = slot.Room.Id,
                CareLocationShiftId = slot.Shift.Id,
                SoftConstraintViolations = results.SoftConstraintViolations
                    .GetValueOrDefault((slot.Date, slot.Room.Id, slot.Shift.Id), [])
            }).ToList(),
        });
    }
}