import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import moment from 'moment';

export function IsDateOnly(
  validationOptions?: ValidationOptions,
): PropertyDecorator {
  return (object: object, propertyName: string | symbol) => { // eslint-disable-line
    registerDecorator({
      propertyName: typeof propertyName === 'symbol' ? propertyName.toString() : propertyName,
      name: 'isDateOnly',
      target: object.constructor,
      constraints: [],
      options: {
        message:
          'Please provide only date like 2020-01-01',
        ...validationOptions,
      },
      validator: {
        validate(value: string, _args: ValidationArguments) {
          return RegExp(/^\d{4}-\d{2}-\d{2}$/).test(value);
        },
      },
    });
  };
}

export function IsIntegerList(
  validationOptions?: ValidationOptions,
): PropertyDecorator {
  return (object: object, propertyName: string | symbol) => { // eslint-disable-line
    registerDecorator({
      propertyName: typeof propertyName === 'symbol' ? propertyName.toString() : propertyName,
      name: 'IsIntegerList',
      target: object.constructor,
      constraints: [],
      options: {
        message:
            'Please provide list of integers without spaces - 1,2,3',
        ...validationOptions,
      },
      validator: {
        validate(value: number[], _args: ValidationArguments) {
          return value?.every((part) => Number.isInteger(part));
        },
      },
    });
  };
}

export function IsDatesList(
  validationOptions?: ValidationOptions,
): PropertyDecorator {
  return (object: object, propertyName: string | symbol) => { // eslint-disable-line
    registerDecorator({
      propertyName: typeof propertyName === 'symbol' ? propertyName.toString() : propertyName,
      name: 'IsDatesList',
      target: object.constructor,
      constraints: [],
      options: {
        message:
            'Please provide list of dates without spaces - 2020-01-01,2021-02-02,2022-03-03',
        ...validationOptions,
      },
      validator: {
        validate(value: string[], _args: ValidationArguments) {
          return value?.every((part) => moment(part).isValid());
        },
      },
    });
  };
}