﻿
namespace Ethos.PlatformManager
{
    public enum LicenseState
    {
        Active,
        Inactive,
    }

    public class LicenseDto
    {
        public Guid? Id { get; set; }
        public string Name { get; set; } = null!;
        public Guid TenantId { get; set; }
        public string? State { get; set; }
        public DateTimeOffset? StartDate { get; set; }
        public DateTimeOffset? EndDate { get; set; }
        public DateTimeOffset Created { get; set; }
        public DateTimeOffset? Updated { get; set; }
    }

    public class ProductDto
    {
        public Guid? Id { get; set; }
        public string Name { get; set; } = null!;
        public ICollection<ProductFeatureDto> Features { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class TenantContactDto
    {
        public Guid? Id { get; set; }
        public string LastName { get; set; } = null!;
        public string FirstName { get; set; } = null!;
        public string? MiddleName { get; set; }
        public string? Title { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string ContactType { get; set; } = null!;
        public string? OrganizationName { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class TenantDto
    {
        public Guid? Id { get; set; }
        public string? Status { get; set; }
        public string OrganizationName { get; set; } = null!;
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? Address3 { get; set; }
        public string? PostalCode { get; set; }
        public string? City { get; set; }
        public string? Region { get; set; }
        public string? Country { get; set; }
        public string? OrganizationPhone { get; set; }
        public string? OrganizationEmail { get; set; }
        public ICollection<TenantContactDto> Contacts { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class FeatureDto
    {
        public Guid? Id { get; set; }
        public string Name { get; set; } = null!;
        public string ScopePrefix { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class ProductFeatureDto
    {
        public Guid FeatureId { get; set; }
        public string? Name { get; set; }
        public bool Enabled { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseFeatureDto
    {
        public Guid FeatureId { get; set; }
        public string? Name { get; set; }
        public bool Enabled { get; set; }
        public DateTimeOffset? StartDate { get; set; }
        public DateTimeOffset? EndDate { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseProductDto
    {
        public Guid? Id { get; set; }
        public Guid LicenseId { get; set; }
        public Guid ProductId { get; set; }
        public string? Name { get; set; }
        public ICollection<LicenseFeatureDto> Features { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseProductResponseDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public ICollection<LicenseFeatureDto> Features { get; set; } = [];
        public ProductDto? SourceProduct { get; set; }
        public LicenseDto License { get; set; } = null!;
    }
}