import { MigrationInterface, QueryRunner } from 'typeorm';

export class addStudyCredentials1690173704405 implements MigrationInterface {
    name = 'addStudyCredentials1690173704405'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('CREATE TABLE "study_credentials" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "state_id" integer, "study_id" integer NOT NULL, "credentials" jsonb NOT NULL DEFAULT \'[]\', CONSTRAINT "PK_9246c30b4ba6de1dfb1ec8650f0" PRIMARY KEY ("id"))');
      await queryRunner.query('ALTER TABLE "study_credentials" ADD CONSTRAINT "FK_a8ebe583291f0227706b98c4fd3" FOREIGN KEY ("state_id") REFERENCES "states"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "study_credentials" ADD CONSTRAINT "FK_0109c5a3361472df1966aa032f7" FOREIGN KEY ("study_id") REFERENCES "studies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "study_credentials" DROP CONSTRAINT "FK_0109c5a3361472df1966aa032f7"');
      await queryRunner.query('ALTER TABLE "study_credentials" DROP CONSTRAINT "FK_a8ebe583291f0227706b98c4fd3"');
      await queryRunner.query('DROP TABLE "study_credentials"');
    }

}
