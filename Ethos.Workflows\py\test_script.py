﻿import aiohttp
from ethos_common import *
from ethos_common_types import *

from login_api_v0 import LoginApi, LoginRequestDto
from provider_api import ProviderA<PERSON>, CreateProviderDto, ProviderDto
from carelocation_api import CareLocationApi, CreateCareLocationDto, CareLocationDto, CareLocationQ
from physician_api import PhysicianApi, CreatePhysicianDto, PhysicianDto, PhysicianQ
from room_api import RoomApi, CreateRoomDto, RoomDto
from equipment_api import EquipmentApi, CreateEquipmentDto, EquipmentDto
from patient_api import PatientApi, CreatePatientInputDto, PatientDto, PatientQ
from order_api import OrderApi, CreateOrderDto, OrderDto
from study_api import StudyApi, CreateStudyDto, StudyDto
from schedulingconstraint_api import SchedulingConstraintApi, CreateSchedulingConstraintDto, SchedulingConstraintDto
from file_api_v0 import FileApi, RequestUploadTokenDto

async def main():
    async with aiohttp.ClientSession(base_url="http://localhost:4000/") as session:
        login_client = LoginApi(session)
        token = await login_client.login(LoginRequestDto(username='test', password='password'))
        providers = ProviderApi(session)
        providers.set_bearer_token(token)
        care_locations = CareLocationApi(session)
        care_locations.set_bearer_token(token)
        room_client = RoomApi(session)
        room_client.set_bearer_token(token)
        equipment_client = EquipmentApi(session)
        equipment_client.set_bearer_token(token)
        physician_client = PhysicianApi(session)
        physician_client.set_bearer_token(token)
        order_client = OrderApi(session)
        order_client.set_bearer_token(token)
        patient_client = PatientApi(session)
        patient_client.set_bearer_token(token)
        scheduling_constraint_client = SchedulingConstraintApi(session)
        scheduling_constraint_client.set_bearer_token(token)
        study_client = StudyApi(session)
        study_client.set_bearer_token(token)

        # Example usage:
        new_provider = CreateProviderDto(
            name='New Provider',
            identifiers=[
                IdentifierDto(
                    value='**********',  # Example identifier value
                    system='EIN'  # Example identifier system
                )
            ],
            parentProviderId=None,
            contactDetail=OrganizationContactDetailDto(
                phoneNumbers=[
                    OrganizationPhoneNumberDto(
                        phoneNumber='************'
                    )
                ],
                emails=[
                    OrganizationEmailDto("<EMAIL>")
                ],
                addresses=[
                    OrganizationAddressDto(
                        address=AddressDto(
                            line1='123 Provider St',
                            line2='Suite 100',
                            city='Anytown',
                            state=1,  # Example state
                            postalCode='12345',
                            country=1  # Example country
                        ),
                        use=1,  # Example address use
                        type=2  # Example address type
                    )
                ],
                contactPersons=[
                    OrganizationContactPersonDto(
                        name=PersonNameDto(
                            prefix=1,  # Example prefix
                            firstName='John',
                            middleName='A',
                            lastName='Doe',
                            suffix=2  # Example suffix
                        ),
                        contactDetail=PersonalContactDetailDto(
                            phoneNumbers=[
                                PersonalPhoneNumberDto(
                                    type=1,  # Example phone type
                                    value='************',
                                    preferredTime=1,
                                    allowsSms=True,
                                    allowsVoice=True,
                                    isPreferred=True
                                )
                            ],
                            emails=[
                                PersonalEmailDto(
                                    use=1,  # Example email use
                                    value="<EMAIL>",
                                    isPreferred=True
                                )
                            ],
                            addresses=[],
                            emergencyContacts=[]
                        )
                    ),
                ]
            ))
        created_provider, etag = await providers.create(new_provider)
        print(f'Created provider: {created_provider}, ETag: {etag}')


        new_care_location = CreateCareLocationDto(
            name='New Care Location',
            parentServiceLocationId=None,
            parentProviderId=created_provider.id,
            contactDetail=OrganizationContactDetailDto(
                phoneNumbers=[], emails=[], addresses=[], contactPersons=[]
            ),
            supportedEncounterTypes= [1, 2, 3],  # Example encounter types
            supportedStudyTypes=[1, 2, 3],  # Example service types
        )
        created_carelocation, etag = await care_locations.create(new_care_location)
        print(f'Created care location: {created_carelocation}, ETag: {etag}')

        new_care_location = CreateCareLocationDto(
            name='New Care Location 2',
            parentServiceLocationId=created_carelocation.id,
            parentProviderId=created_provider.id,
            contactDetail=OrganizationContactDetailDto(
                phoneNumbers=[], emails=[], addresses=[], contactPersons=[]
            ),
            supportedEncounterTypes= [1, 2, 3],  # Example encounter types
            supportedStudyTypes=[1, 2, 3],  # Example service types
        )
        created_carelocation, etag = await care_locations.create(new_care_location)
        print(f'Created care location: {created_carelocation}, ETag: {etag}')

        await care_locations.search(QueryDto.Literal(CareLocationQ.WithId(created_carelocation.id)))
        r = await care_locations.search(QueryDto.Literal(CareLocationQ.WithApproximateName("new")))
        print(f'Searched care location: {created_carelocation} -> {r}')

        new_room = CreateRoomDto(
            name='Room 101',
            careLocationId=created_carelocation.id,
            supportedStudyTypes= [1, 2, 3],  # Example study types
        )
        created_room, etag = await room_client.create(new_room)
        print(f'Created room: {created_room}, ETag: {etag}')

        new_equipment = CreateEquipmentDto(
            roomId=created_room.id,
            careLocationId=created_carelocation.id,
            equipmentTypeId=1,  # Example UUID
            equipmentData={
                'serialNumber': 'SN123456',
                'manufacturer': 'Example Corp',
                'model': 'Model X'
            }
        )
        created_equipment, etag = await equipment_client.create(new_equipment)
        print(f'Created equipment: {created_equipment}, ETag: {etag}')

        new_physician = CreatePhysicianDto(
            names=[PersonNameDto(
                prefix=1,  # Example prefix
                firstName='John',
                middleName='A',
                lastName='Doe',
                suffix=2  # Example suffix
            )],
            identifiers=[
                IdentifierDto(
                    value='**********',  # Example identifier value
                    system='NPI'  # Example identifier system
                )
            ],
            demographics=None,
            contactInformation=PersonalContactDetailDto(
                phoneNumbers=[
                    PersonalPhoneNumberDto(
                        type=1,  # Example phone type
                        value='************',
                        preferredTime=1,
                        allowsSms=True,
                        allowsVoice=True,
                        isPreferred=True
                    )
                ],
                emails=[
                    PersonalEmailDto(
                        use=1,  # Example email use
                        isPreferred=True,
                        value="<EMAIL>")
                ],
                addresses=[
                    PersonalAddressDto(
                        address=AddressDto(
                            line1='123 Physician St',
                            line2='Suite 300',
                            city='Physician City',
                            state=1,  # Example state
                            postalCode='67890',
                            country=1  # Example country
                        ),
                        use=1,  # Example address use
                        type=2  # Example address type
                    )
                ],
                emergencyContacts=[],
            ),
            careLocationIds=[created_carelocation.id]  # Associate with created care location
        )
        created_physician, etag = await physician_client.create(new_physician)
        print(f'Created physician: {created_physician}, ETag: {etag}')

        searched_physicians = await physician_client.search(QueryDto.Literal(PhysicianQ.WithId(created_physician.id)))
        print(f'Searched physician: {searched_physicians}')
        searched_physicians = await physician_client.search(QueryDto.Literal(PhysicianQ.WithApproximateFullName("John")))
        print(f'Searched physician by name: {searched_physicians}')

        test_patient_draft = await patient_client.create_draft(
            data = {
                'contactInformation': {
                    'phoneNumbers': [
                        {
                            'type': 1,
                            'value': '',
                            'preferredTime': 1,
                            'allowsSms': True,
                            'allowsVoice': True,
                            'isPreferred': True
                        }
                    ],
                }
            }
        )

        print(f'Test patient draft: {test_patient_draft}')

        new_patient = CreatePatientInputDto(
            contactInformation=PersonalContactDetailDto(
                phoneNumbers=[
                    PersonalPhoneNumberDto(
                        type=1,
                        value='************',
                        preferredTime=1,
                        allowsSms=True,
                        allowsVoice=True,
                        isPreferred=True
                    )
                ],
                emails=[
                    PersonalEmailDto(
                        use=1,
                        value="<EMAIL>",
                        isPreferred=True
                    )
                ],
                emergencyContacts=[
                    PersonalEmergencyContactDto(
                        prefix=1,  # Example prefix
                        firstName='Jane',
                        middleName='B',
                        lastName='Doe',
                        suffix=2,  # Example suffix
                        relationship=1,  # Example relationship
                        contactInformation="************",
                    )
                ],
                addresses=[
                    PersonalAddressDto(
                        address=AddressDto(
                            line1='123 Insurance St',
                            line2='Suite 200',
                            city='Insurance City',
                            state=1,
                            postalCode='54321',
                            country=1
                        ),
                        use=1,
                        type=2,
                )],
            ),

            identifiers=[],

            insurances=[
                InsuranceDto(
                    insuranceCarrier=1,
                    insuranceId='INS123456',
                    policyId='POL123456',
                    groupNumber='GRP123456',
                    memberId= 'MEM123456',
                    insuranceHolder=InsuranceHolderDto(
                        name="John Doe",
                        dateOfBirth="1990-01-01",
                        relationship=1,  # Example relationship
                    ),
                    phoneNumber=PhoneNumberWithUseDto(
                        use=1,
                        phoneNumber='************',
                        allowsSMS=True,
                        allowsVoice=True,
                        allowsCommunication=True,
                        extension='1234',
                    ),
                    email=EmailWithUseDto(
                        use=1,
                        email="<EMAIL>"
                    ),
                    address=AddressWithUseDto(
                        AddressDto(
                            line1='123 Insurance St',
                            line2='Suite 200',
                            city='Insurance City',
                            state=1,
                            postalCode='54321',
                            country=1
                        ),
                        use=1
                    )
                )
            ],
            patientInformation=PatientInformationDto(
                prefix=1,
                suffix=2,
                firstName='John',
                middleName='A',
                lastName='Doe',
                ssn='***********',
                mrn='MRN123456',
            ),
            guardians=[
                PatientGuardianDto(
                    id=None,
                    names=[PersonNameDto(
                        prefix=1,  # Example prefix
                        firstName='Jane',
                        middleName='B',
                        lastName='Doe',
                        suffix=2  # Example suffix
                    )],
                    identifiers=[
                        IdentifierDto(
                            value='**********',  # Example identifier value
                            system='SSN'  # Example identifier system
                        )
                    ],
                    contactInformation=PersonalContactDetailDto(
                        phoneNumbers=[
                            PersonalPhoneNumberDto(
                                type=1,  # Example phone type
                                value='************',
                                preferredTime=1,
                                allowsSms=True,
                                allowsVoice=True,
                                isPreferred=True
                            )
                        ],
                        emails=[
                            PersonalEmailDto(
                                use=1,  # Example email use
                                isPreferred=True,
                                value="<EMAIL>"
                            )
                        ],
                        emergencyContacts=[],
                        addresses=[]
                    ),
                    demographics=None,
                    relationshipToPatient=1,  # Example relationship
                )
            ],
            clinicalConsiderations=[1, 3, 5],
            schedulingPreferences=SchedulingPreferencesDto(
                technicianPreference=1,
                preferredDayOfWeek=[1, 2, 3],  # Example days of the week
            ),
            additionalPatientNotes="Patient has no known allergies.",
            caregiverInformation="Primary caregiver is spouse.",
            physicalMeasurements=PhysicalMeasurementsDto(
                heightInches= 70,
                weightPounds=180,
                neckSize= 15,
                bmi=24.5,
            ),
            demographics=DemographicsDto(
                birthSex=1,
                gender=1,
                dateOfBirth="1990-01-01",
                maritalStatus=1,
                race=1,
                ethnicity=1),
        )

        created_patient, etag = await patient_client.create(new_patient)
        print(f'Created patient: {created_patient}, ETag: {etag}')
        # print(f'Created patient: {created_patient}, ETag: {etag}')
        await patient_client.get_by_id(created_patient.id)

        r = await patient_client.search(QueryDto.Literal(PatientQ.WithId(created_patient.id)))
        print(f'Searched patient by ID: {r}')

        new_order = CreateOrderDto(
            patientId=created_patient.id,
            careLocationId=created_carelocation.id,
            primaryCarePhysicianId=None,
            interpretingPhysicianId=created_physician.id,
            referringPhysicianId=created_physician.id,
        )
        created_order, etag = await order_client.create(new_order)
        print(f'Created order: {created_order}, ETag: {etag}')

        new_study = CreateStudyDto(
            orderId=created_order.id,
            encounterType=1,
            studyType=1,
            studyAttributes={
                'studyName': 'Example Study',
                'description': 'This is an example study description.',
                'studyDate': '2023-10-01',
                'studyTime': '10:00:00',
                'studyLocationId': created_carelocation.id,
                'physicianId': created_physician.id,
            },
            insurances=[]
        )
        created_study, etag = await study_client.create(new_study)
        print(f'Created study: {created_study}, ETag: {etag}')

        new_scheduling_constraint = CreateSchedulingConstraintDto(
            name='Example Constraint',
            description='This is an example scheduling constraint.',
            expression=Expr.VarName('test'),
            isHardConstraint=True
        )
        print(new_scheduling_constraint.to_json())
        created_scheduling_constraint, etag = await scheduling_constraint_client.create(new_scheduling_constraint)
        print(f'Created scheduling constraint: {created_scheduling_constraint}, ETag: {etag}')


        file_api = FileApi(session)
        file_api.set_bearer_token(token)
        upload_token_dto = RequestUploadTokenDto(
            contextEntityType='Patient',
            contextEntityId=created_patient.id,
            purpose='Test file upload'
        )
        upload_token_response = await file_api.request_upload_token(upload_token_dto)
        print(f'Requested upload token: {upload_token_response}')

        # make a small temp png file using PIL
        import tempfile
        import os
        from PIL import Image
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        temp_file_path = temp_file.name
        with Image.new('RGB', (100, 100), color='blue') as img:
            img.save(temp_file_path)
        temp_file.close()


        print(f'Temp file created at: {temp_file_path}')
        upload_response = await file_api.upload_file(
            file_path=temp_file_path,
            upload_token=upload_token_response['uploadToken'],
            mime="image/png"
        )
        print(f'File uploaded successfully: {upload_response}')

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())
