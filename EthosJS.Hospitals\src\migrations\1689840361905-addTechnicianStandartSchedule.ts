import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTechnicianStandartSchedule1689840361905 implements MigrationInterface {
    name = 'addTechnicianStandartSchedule1689840361905'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technicians" ADD "standard_schedule" jsonb NOT NULL DEFAULT \'{"mon":{"shift":"day_off"},"tue":{"shift":"day_off"},"wed":{"shift":"day_off"},"thu":{"shift":"day_off"},"fri":{"shift":"day_off"},"sat":{"shift":"day_off"},"sun":{"shift":"day_off"}}\'');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technicians" DROP COLUMN "standard_schedule"');
    }

}
