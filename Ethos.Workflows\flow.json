{"types": {"NonEmptyString": {"type": "AliasTypeDef", "name": "NonEmptyString", "description": "Non-empty string", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "Sex": {"type": "AliasTypeDef", "name": "Sex", "description": "Person's Birth Sex", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "Gender": {"type": "AliasTypeDef", "name": "Gender", "description": "Person's Gender", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "MaritalStatus": {"type": "AliasTypeDef", "name": "MaritalStatus", "description": "Marital status", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "Race": {"type": "AliasTypeDef", "name": "Race", "description": "User Race", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "Ethnicity": {"type": "AliasTypeDef", "name": "Ethnicity", "description": "User Ethnicity", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "CountryName": {"type": "AliasTypeDef", "name": "CountryName", "description": "Country name", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "StateName": {"type": "AliasTypeDef", "name": "StateName", "description": "State name", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "EmailUse": {"type": "AliasTypeDef", "name": "EmailUse", "description": "Email use", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "AddressUse": {"type": "AliasTypeDef", "name": "AddressUse", "description": "Address use", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "AddressType": {"type": "AliasTypeDef", "name": "AddressType", "description": "Address type", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "NamePrefix": {"type": "AliasTypeDef", "name": "NamePrefix", "description": "Name prefix", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "NameSuffix": {"type": "AliasTypeDef", "name": "NameSuffix", "description": "Name suffix", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "SSN": {"type": "AliasTypeDef", "name": "SSN", "description": "Social Security Number", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "InvalidSSN", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[0-9]{3}-[0-9]{2}-[0-9]{4}$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "SomeMRN": {"type": "AliasTypeDef", "name": "SomeMRN", "description": "Medical Record Number", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "InvalidMRN", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[a-zA-Z0-9]{1,20}$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "Email": {"type": "AliasTypeDef", "name": "Email", "description": "Email address", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "Email", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "BirthDate": {"type": "AliasTypeDef", "name": "BirthDate", "description": "Date of birth", "target": {"type": "RefType", "name": "Date"}, "validation": [{"level": "warning", "name": "DateOfBirthMightBeUnrealistic", "require": {"type": "BinOp", "left": {"type": "Call", "func": "age", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "Date"}, "base_type": {"type": "RefType", "name": "Date"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": "<=", "right": {"type": "IntLit", "value": 150, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "DateOfBirthIsInTheFuture", "require": {"type": "BinOp", "left": {"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "Date"}, "base_type": {"type": "RefType", "name": "Date"}}}, "op": "<", "right": {"type": "Call", "func": "nowdate", "args": [], "extra": {"type": {"type": "RefType", "name": "Date"}, "base_type": {"type": "RefType", "name": "Date"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "NamePart": {"type": "AliasTypeDef", "name": "NamePart", "description": "Name part", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "TooLong", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": "<=", "right": {"type": "IntLit", "value": 50, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "TooShort", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">=", "right": {"type": "IntLit", "value": 2, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "ContainsNonLatinCharacters", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[a-zA-Z-]+$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "CityName": {"type": "AliasTypeDef", "name": "CityName", "description": "City name", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "TooLong", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": "<=", "right": {"type": "IntLit", "value": 50, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "ContainsNonLatinCharacters", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[a-zA-Z-]+$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "PostalCode": {"type": "AliasTypeDef", "name": "PostalCode", "description": "Postal code", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "TooShort", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">=", "right": {"type": "IntLit", "value": 3, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "TooLong", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": "<=", "right": {"type": "IntLit", "value": 16, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "ContainsNonLatinCharacters", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[a-zA-Z0-9- ]+$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "Address": {"type": "StructTypeDef", "name": "Address", "description": "Address", "fields": {"line1": {"type": "RefType", "name": "String"}, "line2": {"type": "NullableOf", "inner": {"type": "RefType", "name": "String"}}, "city": {"type": "RefType", "name": "CityName"}, "state": {"type": "RefType", "name": "StateName"}, "postalCode": {"type": "RefType", "name": "PostalCode"}, "country": {"type": "RefType", "name": "CountryName"}}, "validation": [{"level": "error", "name": "NoAddressLine", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "line1", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}]}, "PhoneType": {"type": "AliasTypeDef", "name": "PhoneType", "description": "Phone type", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "PhoneNumber": {"type": "AliasTypeDef", "name": "PhoneNumber", "description": "Phone number", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "InvalidPhoneNumber", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[0-9]{3}-[0-9]{3}-[0-9]{4}$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "PhoneNumberWithUse": {"type": "StructTypeDef", "name": "PhoneNumberWithUse", "description": "Phone number with purpose", "fields": {"type": {"type": "RefType", "name": "Integer"}, "phoneNumber": {"type": "RefType", "name": "PhoneNumber"}, "use": {"type": "RefType", "name": "Integer"}, "allowsSMS": {"type": "NullableOf", "inner": {"type": "RefType", "name": "Boolean"}}, "allowsVoice": {"type": "NullableOf", "inner": {"type": "RefType", "name": "Boolean"}}, "allowsCommunication": {"type": "NullableOf", "inner": {"type": "RefType", "name": "Boolean"}}, "extension": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NonEmptyString"}}}, "validation": []}, "AddressWithUse": {"type": "StructTypeDef", "name": "AddressWithUse", "description": "Address with use", "fields": {"address": {"type": "RefType", "name": "Address"}, "use": {"type": "RefType", "name": "AddressUse"}}, "validation": []}, "TimeOfDay": {"type": "StructTypeDef", "name": "TimeOfDay", "description": "Time of day", "fields": {"hour": {"type": "RefType", "name": "Integer"}, "minute": {"type": "RefType", "name": "Integer"}}, "validation": [{"level": "error", "name": "InvalidHour", "require": {"type": "BinOp", "left": {"type": "BinOp", "left": {"type": "Var", "name": "hour", "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">=", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "op": "&&", "right": {"type": "BinOp", "left": {"type": "Var", "name": "hour", "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": "<", "right": {"type": "IntLit", "value": 24, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "InvalidMinute", "require": {"type": "BinOp", "left": {"type": "BinOp", "left": {"type": "Var", "name": "minute", "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">=", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "op": "&&", "right": {"type": "BinOp", "left": {"type": "Var", "name": "minute", "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": "<", "right": {"type": "IntLit", "value": 60, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}]}, "TimeRange": {"type": "StructTypeDef", "name": "TimeRange", "description": "Time range", "fields": {"start": {"type": "RefType", "name": "TimeOfDay"}, "end": {"type": "RefType", "name": "TimeOfDay"}}, "validation": []}, "NamePartOrInitial": {"type": "AliasTypeDef", "name": "NamePartOrInitial", "description": "Name part or initial", "target": {"type": "RefType", "name": "String"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "TooLong", "require": {"type": "BinOp", "left": {"type": "Call", "func": "len", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": "<=", "right": {"type": "IntLit", "value": 50, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "ContainsNonLatinCharacters", "require": {"type": "Call", "func": "matches", "args": [{"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}, {"type": "StringLit", "value": "^[a-zA-Z-]+$", "extra": {"type": {"type": "RefType", "name": "String"}, "base_type": {"type": "RefType", "name": "String"}}}], "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "EmergencyContactRelationship": {"type": "AliasTypeDef", "name": "EmergencyContactRelationship", "description": "Emergency contact relationship", "target": {"type": "RefType", "name": "Integer"}, "validation": [{"level": "error", "name": "ValueIsEmpty", "require": {"type": "BinOp", "left": {"type": "Var", "name": "value", "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}], "is_categorical": false, "default_values": []}, "EmailWithUse": {"type": "StructTypeDef", "name": "EmailWithUse", "description": "Email with purpose", "fields": {"email": {"type": "RefType", "name": "Email"}, "use": {"type": "RefType", "name": "EmailUse"}}, "validation": []}, "PersonName": {"type": "StructTypeDef", "name": "PersonName", "description": "Person name", "fields": {"firstNames": {"type": "ListOf", "inner": {"type": "RefType", "name": "NonEmptyString"}, "min_size": 1, "max_size": null}, "lastNames": {"type": "ListOf", "inner": {"type": "RefType", "name": "NonEmptyString"}, "min_size": 1, "max_size": null}, "middleInitials": {"type": "ListOf", "inner": {"type": "RefType", "name": "NonEmptyString"}, "min_size": null, "max_size": null}, "middleNames": {"type": "ListOf", "inner": {"type": "RefType", "name": "NonEmptyString"}, "min_size": null, "max_size": null}, "prefixes": {"type": "ListOf", "inner": {"type": "RefType", "name": "NonEmptyString"}, "min_size": null, "max_size": null}, "suffixes": {"type": "ListOf", "inner": {"type": "RefType", "name": "NonEmptyString"}, "min_size": null, "max_size": null}}, "validation": []}, "InsuranceHolder": {"type": "StructTypeDef", "name": "InsuranceHolder", "description": null, "fields": {"name": {"type": "RefType", "name": "NamePart"}, "dateOfBirth": {"type": "RefType", "name": "BirthDate"}, "relationship": {"type": "RefType", "name": "Integer"}}, "validation": []}, "Insurance": {"type": "StructTypeDef", "name": "Insurance", "description": null, "fields": {"insuranceCarrier": {"type": "RefType", "name": "Integer"}, "planType": {"type": "RefType", "name": "Integer"}, "insuranceId": {"type": "NullableOf", "inner": {"type": "RefType", "name": "String"}}, "policyId": {"type": "RefType", "name": "NonEmptyString"}, "groupNumber": {"type": "RefType", "name": "NonEmptyString"}, "memberId": {"type": "RefType", "name": "NonEmptyString"}, "insuranceHolder": {"type": "NullableOf", "inner": {"type": "RefType", "name": "InsuranceHolder"}}, "phoneNumber": {"type": "NullableOf", "inner": {"type": "RefType", "name": "PhoneNumberWithUse"}}, "email": {"type": "NullableOf", "inner": {"type": "RefType", "name": "EmailWithUse"}}, "address": {"type": "NullableOf", "inner": {"type": "RefType", "name": "AddressWithUse"}}}, "validation": []}, "AddressWithUseType": {"type": "StructTypeDef", "name": "AddressWithUseType", "description": null, "fields": {"use": {"type": "RefType", "name": "AddressUse"}, "type": {"type": "RefType", "name": "AddressType"}, "address": {"type": "RefType", "name": "Address"}}, "validation": []}, "Demographics": {"type": "StructTypeDef", "name": "Demographics", "description": null, "fields": {"dateOfBirth": {"type": "RefType", "name": "BirthDate"}, "gender": {"type": "RefType", "name": "Gender"}, "birthSex": {"type": "RefType", "name": "Gender"}, "maritalStatus": {"type": "RefType", "name": "MaritalStatus"}, "race": {"type": "RefType", "name": "Race"}, "ethnicity": {"type": "RefType", "name": "Ethnicity"}}, "validation": []}, "Identifier": {"type": "StructTypeDef", "name": "Identifier", "description": null, "fields": {"system": {"type": "RefType", "name": "String"}, "value": {"type": "RefType", "name": "String"}}, "validation": []}, "PhysicalMeasurements": {"type": "StructTypeDef", "name": "PhysicalMeasurements", "description": null, "fields": {"heightInches": {"type": "RefType", "name": "Real"}, "weightPounds": {"type": "RefType", "name": "Real"}, "neckSize": {"type": "RefType", "name": "Real"}, "bmi": {"type": "RefType", "name": "Real"}}, "validation": [{"level": "error", "name": "HeightIsInvalid", "require": {"type": "BinOp", "left": {"type": "Var", "name": "heightInches", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "HeightMightBeUnrealistic", "require": {"type": "BinOp", "left": {"type": "Var", "name": "heightInches", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": "<", "right": {"type": "IntLit", "value": 200, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "WeightIsInvalid", "require": {"type": "BinOp", "left": {"type": "Var", "name": "weightPounds", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "WeightMightBeUnrealistic", "require": {"type": "BinOp", "left": {"type": "Var", "name": "weightPounds", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": "<", "right": {"type": "IntLit", "value": 1000, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "NeckSizeIsInvalid", "require": {"type": "BinOp", "left": {"type": "Var", "name": "neckSize", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "NeckSizeMightBeInvalid", "require": {"type": "BinOp", "left": {"type": "BinOp", "left": {"type": "Var", "name": "neckSize", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": ">", "right": {"type": "IntLit", "value": 5, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "op": "&&", "right": {"type": "BinOp", "left": {"type": "Var", "name": "neckSize", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": "<", "right": {"type": "IntLit", "value": 50, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "error", "name": "BMIIsInvalid", "require": {"type": "BinOp", "left": {"type": "Var", "name": "bmi", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": ">", "right": {"type": "IntLit", "value": 0, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}, {"level": "warning", "name": "BMIMightBeUnrealistic", "require": {"type": "BinOp", "left": {"type": "BinOp", "left": {"type": "Var", "name": "bmi", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": ">", "right": {"type": "IntLit", "value": 10, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "op": "&&", "right": {"type": "BinOp", "left": {"type": "Var", "name": "bmi", "extra": {"type": {"type": "RefType", "name": "Real"}, "base_type": {"type": "RefType", "name": "Real"}}}, "op": "<", "right": {"type": "IntLit", "value": 100, "extra": {"type": {"type": "RefType", "name": "Integer"}, "base_type": {"type": "RefType", "name": "Integer"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}, "extra": {"type": {"type": "RefType", "name": "Boolean"}, "base_type": {"type": "RefType", "name": "Boolean"}}}}]}, "GuardianType": {"type": "AliasTypeDef", "name": "GuardianType", "description": "Guardian type", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}, "GuardianBasicInformation": {"type": "StructTypeDef", "name": "GuardianBasicInformation", "description": "Guardian information", "fields": {"guardianType": {"type": "RefType", "name": "Integer"}, "prefix": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NamePrefix"}}, "firstName": {"type": "RefType", "name": "NamePart"}, "middleName": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NamePartOrInitial"}}, "lastName": {"type": "RefType", "name": "NamePart"}, "suffix": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NameSuffix"}}}, "validation": []}, "GuardianDemographics": {"type": "StructTypeDef", "name": "GuardianDemographics", "description": "Guardian demographics", "fields": {"gender": {"type": "RefType", "name": "Gender"}, "dateOfBirth": {"type": "RefType", "name": "BirthDate"}, "ssn": {"type": "RefType", "name": "SSN"}, "relationShipToPatient": {"type": "RefType", "name": "String"}, "idType": {"type": "RefType", "name": "String"}, "idNumber": {"type": "RefType", "name": "String"}, "addresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "Address"}, "min_size": null, "max_size": null}, "phoneNumbers": {"type": "ListOf", "inner": {"type": "RefType", "name": "PhoneNumberWithUse"}, "min_size": null, "max_size": null}}, "validation": []}, "TechnicianPreference": {"type": "AliasTypeDef", "name": "TechnicianPreference", "description": "Technician preference", "target": {"type": "RefType", "name": "Integer"}, "validation": [], "is_categorical": false, "default_values": []}}, "flows": {"AddNewPatient": {"name": "AddNewPatient", "types": {"PatientInformation": {"type": "StructTypeDef", "name": "PatientInformation", "description": null, "fields": {"prefix": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NamePrefix"}}, "firstName": {"type": "RefType", "name": "NamePart"}, "middleName": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NamePartOrInitial"}}, "lastName": {"type": "RefType", "name": "NamePart"}, "suffix": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NameSuffix"}}, "ssn": {"type": "RefType", "name": "SSN"}, "mrn": {"type": "NullableOf", "inner": {"type": "RefType", "name": "SomeMRN"}}}, "validation": []}, "PhoneNumberContact": {"type": "StructTypeDef", "name": "PhoneNumberContact", "description": null, "fields": {"type": {"type": "RefType", "name": "PhoneType"}, "value": {"type": "RefType", "name": "PhoneNumber"}, "preferredTime": {"type": "RefType", "name": "Integer"}, "allowsSMS": {"type": "RefType", "name": "Boolean"}, "allowsVoice": {"type": "RefType", "name": "Boolean"}, "isPreferred": {"type": "RefType", "name": "Boolean"}}, "validation": []}, "EmailContact": {"type": "StructTypeDef", "name": "EmailContact", "description": null, "fields": {"use": {"type": "RefType", "name": "EmailUse"}, "value": {"type": "RefType", "name": "Email"}, "isPreferred": {"type": "RefType", "name": "Boolean"}}, "validation": []}, "EmergencyContact": {"type": "StructTypeDef", "name": "EmergencyContact", "description": null, "fields": {"prefix": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NamePrefix"}}, "firstName": {"type": "RefType", "name": "NamePart"}, "middleName": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NamePartOrInitial"}}, "lastName": {"type": "RefType", "name": "NamePart"}, "suffix": {"type": "NullableOf", "inner": {"type": "RefType", "name": "NameSuffix"}}, "relationship": {"type": "RefType", "name": "EmergencyContactRelationship"}, "contactInformation": {"type": "RefType", "name": "PhoneNumber"}}, "validation": []}, "ContactInformation": {"type": "StructTypeDef", "name": "ContactInformation", "description": null, "fields": {"phoneNumbers": {"type": "ListOf", "inner": {"type": "RefType", "name": "PhoneNumberContact"}, "min_size": null, "max_size": null}, "emails": {"type": "ListOf", "inner": {"type": "RefType", "name": "EmailContact"}, "min_size": null, "max_size": null}, "emergencyContacts": {"type": "ListOf", "inner": {"type": "RefType", "name": "EmergencyContact"}, "min_size": null, "max_size": null}}, "validation": []}, "Guardian": {"type": "StructTypeDef", "name": "Guardian", "description": null, "fields": {"guardianBasicInformation": {"type": "RefType", "name": "GuardianBasicInformation"}}, "validation": []}, "SchedulingPreferences": {"type": "StructTypeDef", "name": "SchedulingPreferences", "description": null, "fields": {"technicianPreference": {"type": "NullableOf", "inner": {"type": "RefType", "name": "TechnicianPreference"}}, "preferredDaysOfWeek": {"type": "ListOf", "inner": {"type": "RefType", "name": "Integer"}, "min_size": null, "max_size": null}}, "validation": []}, "AddBasicInformationStep": {"type": "StructTypeDef", "name": "AddBasicInformationStep", "description": "Data for step AddBasicInformation", "fields": {"patientInformation": {"type": "RefType", "name": "PatientInformation"}, "demographics": {"type": "RefType", "name": "Demographics"}, "physicalMeasurements": {"type": "RefType", "name": "PhysicalMeasurements"}}, "validation": []}, "AddContactsStep": {"type": "StructTypeDef", "name": "AddContactsStep", "description": "Data for step AddContacts", "fields": {"contactInformation": {"type": "RefType", "name": "ContactInformation"}}, "validation": []}, "AddAddressesStep": {"type": "StructTypeDef", "name": "AddAddressesStep", "description": "Data for step AddAddresses", "fields": {"physicalAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "billingAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "deliveryAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}}, "validation": []}, "AddInsurancesStep": {"type": "StructTypeDef", "name": "AddInsurancesStep", "description": "Data for step AddInsurances", "fields": {"insurances": {"type": "ListOf", "inner": {"type": "RefType", "name": "Insurance"}, "min_size": 1, "max_size": null}}, "validation": []}, "AddGuardiansStep": {"type": "StructTypeDef", "name": "AddGuardiansStep", "description": "Data for step Add<PERSON><PERSON><PERSON>", "fields": {"guardians": {"type": "ListOf", "inner": {"type": "RefType", "name": "Guardian"}, "min_size": null, "max_size": null}}, "validation": []}, "AddClinicalInformationStep": {"type": "StructTypeDef", "name": "AddClinicalInformationStep", "description": "Data for step AddClinicalInformation", "fields": {"clinicalConsiderations": {"type": "ListOf", "inner": {"type": "RefType", "name": "Integer"}, "min_size": null, "max_size": null}, "schedulingPreferences": {"type": "RefType", "name": "SchedulingPreferences"}, "additionalPatientNotes": {"type": "NullableOf", "inner": {"type": "RefType", "name": "String"}}, "caregiverInformation": {"type": "NullableOf", "inner": {"type": "RefType", "name": "String"}}}, "validation": []}, "CommittedState": {"type": "StructTypeDef", "name": "CommittedState", "description": "Data for state Committed", "fields": {"patientId": {"type": "RefType", "name": "Guid"}}, "validation": []}, "AddedBasicInformationState": {"type": "StructTypeDef", "name": "AddedBasicInformationState", "description": "Data for state AddedBasicInformation", "fields": {"patientInformation": {"type": "RefType", "name": "PatientInformation"}, "demographics": {"type": "RefType", "name": "Demographics"}, "physicalMeasurements": {"type": "RefType", "name": "PhysicalMeasurements"}}, "validation": []}, "AddedContactsState": {"type": "StructTypeDef", "name": "AddedContactsState", "description": "Data for state AddedContacts", "fields": {"patientInformation": {"type": "RefType", "name": "PatientInformation"}, "demographics": {"type": "RefType", "name": "Demographics"}, "physicalMeasurements": {"type": "RefType", "name": "PhysicalMeasurements"}, "contactInformation": {"type": "RefType", "name": "ContactInformation"}}, "validation": []}, "AddedAddressesState": {"type": "StructTypeDef", "name": "AddedAddressesState", "description": "Data for state AddedAddresses", "fields": {"patientInformation": {"type": "RefType", "name": "PatientInformation"}, "demographics": {"type": "RefType", "name": "Demographics"}, "physicalMeasurements": {"type": "RefType", "name": "PhysicalMeasurements"}, "contactInformation": {"type": "RefType", "name": "ContactInformation"}, "physicalAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "billingAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "deliveryAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}}, "validation": []}, "AddedInsurancesState": {"type": "StructTypeDef", "name": "AddedInsurancesState", "description": "Data for state AddedInsurances", "fields": {"patientInformation": {"type": "RefType", "name": "PatientInformation"}, "demographics": {"type": "RefType", "name": "Demographics"}, "physicalMeasurements": {"type": "RefType", "name": "PhysicalMeasurements"}, "contactInformation": {"type": "RefType", "name": "ContactInformation"}, "physicalAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "billingAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "deliveryAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "insurances": {"type": "ListOf", "inner": {"type": "RefType", "name": "Insurance"}, "min_size": 1, "max_size": null}}, "validation": []}, "AddedGuardiansState": {"type": "StructTypeDef", "name": "AddedGuardiansState", "description": "Data for state AddedGuardians", "fields": {"patientInformation": {"type": "RefType", "name": "PatientInformation"}, "demographics": {"type": "RefType", "name": "Demographics"}, "physicalMeasurements": {"type": "RefType", "name": "PhysicalMeasurements"}, "contactInformation": {"type": "RefType", "name": "ContactInformation"}, "physicalAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "billingAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "deliveryAddresses": {"type": "ListOf", "inner": {"type": "RefType", "name": "AddressWithUseType"}, "min_size": null, "max_size": null}, "insurances": {"type": "ListOf", "inner": {"type": "RefType", "name": "Insurance"}, "min_size": 1, "max_size": null}, "guardians": {"type": "ListOf", "inner": {"type": "RefType", "name": "Guardian"}, "min_size": null, "max_size": null}}, "validation": []}}, "steps": {"AddBasicInformation": {"type": "Step", "name": "AddBasicInformation", "description": "Add basic information", "from": null, "to": "AddedBasicInformation", "data": "AddBasicInformationStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "AddContacts": {"type": "Step", "name": "AddContacts", "description": "Add contact information", "from": "AddedBasicInformation", "to": "AddedContacts", "data": "AddContactsStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "AddAddresses": {"type": "Step", "name": "AddAddresses", "description": "Add demographics", "from": "AddedContacts", "to": "AddedAddresses", "data": "AddAddressesStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "AddInsurances": {"type": "Step", "name": "AddInsurances", "description": "Add insurance information", "from": "AddedAddresses", "to": "AddedInsurances", "data": "AddInsurancesStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "AddGuardians": {"type": "Step", "name": "AddGuardians", "description": "Add guardian information", "from": "AddedInsurances", "to": "AddedGuardians", "data": "AddGuardiansStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "AddClinicalInformation": {"type": "Step", "name": "AddClinicalInformation", "description": "Add clinical information", "from": "AddedGuardians", "to": "Committed", "data": "AddClinicalInformationStep", "is_auto": false, "is_trivial": false, "is_final": false, "validation": []}}, "states": {"Committed": {"type": "State", "name": "Committed", "description": "Patient added successfully", "data": "CommittedState", "is_deprecated": false, "is_final": true}, "AddedBasicInformation": {"type": "State", "name": "AddedBasicInformation", "description": null, "data": "AddedBasicInformationState", "is_deprecated": false, "is_final": false}, "AddedContacts": {"type": "State", "name": "AddedContacts", "description": null, "data": "AddedContactsState", "is_deprecated": false, "is_final": false}, "AddedAddresses": {"type": "State", "name": "AddedAddresses", "description": null, "data": "AddedAddressesState", "is_deprecated": false, "is_final": false}, "AddedInsurances": {"type": "State", "name": "AddedInsurances", "description": null, "data": "AddedInsurancesState", "is_deprecated": false, "is_final": false}, "AddedGuardians": {"type": "State", "name": "AddedGuardians", "description": null, "data": "AddedGuardiansState", "is_deprecated": false, "is_final": false}}}, "AddNewOrder": {"name": "AddNewOrder", "types": {"StudyPreferences": {"type": "StructTypeDef", "name": "StudyPreferences", "description": null, "fields": {"encounterType": {"type": "RefType", "name": "Integer"}, "studyType": {"type": "RefType", "name": "Integer"}, "studyAttributes": {"type": "RefType", "name": "Json"}}, "validation": []}, "AddStudyStep": {"type": "StructTypeDef", "name": "AddStudyStep", "description": "Data for step AddStudy", "fields": {"studyPreferences": {"type": "RefType", "name": "StudyPreferences"}, "associatedInsurance": {"type": "ListOf", "inner": {"type": "RefType", "name": "Guid"}, "min_size": null, "max_size": null}}, "validation": []}, "AddCareLocationStep": {"type": "StructTypeDef", "name": "AddCareLocationStep", "description": "Data for step AddCareLocation", "fields": {"careLocation": {"type": "RefType", "name": "Guid"}}, "validation": []}, "AddPhysiciansStep": {"type": "StructTypeDef", "name": "AddPhysiciansStep", "description": "Data for step AddPhysicians", "fields": {"orderingPhysician": {"type": "RefType", "name": "Guid"}, "interpretingPhysician": {"type": "RefType", "name": "Guid"}, "referringPhysician": {"type": "NullableOf", "inner": {"type": "RefType", "name": "Guid"}}, "primaryCarePhysician": {"type": "NullableOf", "inner": {"type": "RefType", "name": "Guid"}}}, "validation": []}, "ReviewAndSubmitOrderStep": {"type": "StructTypeDef", "name": "ReviewAndSubmitOrderStep", "description": "Data for step ReviewAndSubmitOrder", "fields": {}, "validation": []}, "OrderSubmittedState": {"type": "StructTypeDef", "name": "OrderSubmittedState", "description": "Data for state OrderSubmitted", "fields": {"orderId": {"type": "RefType", "name": "Guid"}}, "validation": []}, "AddedStudyState": {"type": "StructTypeDef", "name": "AddedStudyState", "description": "Data for state AddedStudy", "fields": {"studyPreferences": {"type": "RefType", "name": "StudyPreferences"}, "associatedInsurance": {"type": "ListOf", "inner": {"type": "RefType", "name": "Guid"}, "min_size": null, "max_size": null}}, "validation": []}, "AddedCareLocationState": {"type": "StructTypeDef", "name": "AddedCareLocationState", "description": "Data for state AddedCareLocation", "fields": {"studyPreferences": {"type": "RefType", "name": "StudyPreferences"}, "associatedInsurance": {"type": "ListOf", "inner": {"type": "RefType", "name": "Guid"}, "min_size": null, "max_size": null}, "careLocation": {"type": "RefType", "name": "Guid"}}, "validation": []}, "AddedPhysiciansState": {"type": "StructTypeDef", "name": "AddedPhysiciansState", "description": "Data for state AddedPhysicians", "fields": {"studyPreferences": {"type": "RefType", "name": "StudyPreferences"}, "associatedInsurance": {"type": "ListOf", "inner": {"type": "RefType", "name": "Guid"}, "min_size": null, "max_size": null}, "careLocation": {"type": "RefType", "name": "Guid"}, "orderingPhysician": {"type": "RefType", "name": "Guid"}, "interpretingPhysician": {"type": "RefType", "name": "Guid"}, "referringPhysician": {"type": "NullableOf", "inner": {"type": "RefType", "name": "Guid"}}, "primaryCarePhysician": {"type": "NullableOf", "inner": {"type": "RefType", "name": "Guid"}}}, "validation": []}}, "steps": {"AddStudy": {"type": "Step", "name": "AddStudy", "description": "Add new study", "from": null, "to": "AddedStudy", "data": "AddStudyStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "AddCareLocation": {"type": "Step", "name": "AddCareLocation", "description": null, "from": "AddedStudy", "to": "AddedCareLocation", "data": "AddCareLocationStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "AddPhysicians": {"type": "Step", "name": "AddPhysicians", "description": null, "from": "AddedCareLocation", "to": "AddedPhysicians", "data": "AddPhysiciansStep", "is_auto": false, "is_trivial": true, "is_final": false, "validation": []}, "ReviewAndSubmitOrder": {"type": "Step", "name": "ReviewAndSubmitOrder", "description": null, "from": "AddedPhysicians", "to": "OrderSubmitted", "data": "ReviewAndSubmitOrderStep", "is_auto": false, "is_trivial": false, "is_final": false, "validation": []}}, "states": {"OrderSubmitted": {"type": "State", "name": "OrderSubmitted", "description": "Order submitted successfully", "data": "OrderSubmittedState", "is_deprecated": false, "is_final": true}, "AddedStudy": {"type": "State", "name": "AddedStudy", "description": null, "data": "AddedStudyState", "is_deprecated": false, "is_final": false}, "AddedCareLocation": {"type": "State", "name": "AddedCareLocation", "description": null, "data": "AddedCareLocationState", "is_deprecated": false, "is_final": false}, "AddedPhysicians": {"type": "State", "name": "AddedPhysicians", "description": null, "data": "AddedPhysiciansState", "is_deprecated": false, "is_final": false}}}}}