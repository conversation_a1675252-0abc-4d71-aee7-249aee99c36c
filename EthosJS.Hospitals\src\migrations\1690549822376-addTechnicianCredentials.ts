import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTechnicianCredentials1690549822376 implements MigrationInterface {
    name = 'addTechnicianCredentials1690549822376'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('CREATE TABLE "technician_credentials" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "valid_until" date, "credential_id" integer NOT NULL, "technician_id" integer NOT NULL, CONSTRAINT "PK_ee31d59854016627e25ebda69ad" PRIMARY KEY ("id"))');
      await queryRunner.query('ALTER TABLE "technician_credentials" ADD CONSTRAINT "FK_105943131b163dc4bc315a87faa" FOREIGN KEY ("credential_id") REFERENCES "credentials"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "technician_credentials" ADD CONSTRAINT "FK_70098702ae81fd87230c67bff8f" FOREIGN KEY ("technician_id") REFERENCES "technicians"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technician_credentials" DROP CONSTRAINT "FK_70098702ae81fd87230c67bff8f"');
      await queryRunner.query('ALTER TABLE "technician_credentials" DROP CONSTRAINT "FK_105943131b163dc4bc315a87faa"');
      await queryRunner.query('DROP TABLE "technician_credentials"');
    }

}
