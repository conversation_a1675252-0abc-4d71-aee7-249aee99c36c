import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, InternalServerErrorException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Catch(InternalServerErrorException)
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(public reflector: Reflector) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    // In certain situations `httpAdapter` might not be available in the
    // constructor method, thus we should resolve it here.

    const ctx = host.switchToHttp();

    const httpStatus = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

    const responseBody: any = {
      statusCode: httpStatus,
      timestamp: new Date().toISOString(),
      message: 'Internal server error',
    };

    ctx.getResponse().status(httpStatus).json(responseBody);
  }
}
