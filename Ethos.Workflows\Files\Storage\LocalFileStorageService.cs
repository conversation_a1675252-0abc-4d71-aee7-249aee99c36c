namespace Ethos.Workflows.Files;

using Microsoft.Extensions.Options;

public class LocalFileStorageService : IFileStorageService
{
    private readonly string _basePath;
    private readonly ILogger<LocalFileStorageService> _logger;

    public LocalFileStorageService(IOptions<StorageOptions> storageOptions, ILogger<LocalFileStorageService> logger)
    {
        _logger = logger;
        var options = storageOptions.Value;
        if (options.Provider != StorageProvider.Local || string.IsNullOrEmpty(options.LocalBasePath))
        {
            throw new InvalidOperationException("LocalFileStorageService is not configured correctly.");
        }

        _basePath = Path.GetFullPath(options.LocalBasePath); // Ensure absolute path

        // Ensure base directory exists
        if (!Directory.Exists(_basePath))
        {
            try
            {
                Directory.CreateDirectory(_basePath);
                _logger.LogInformation("Created local storage base directory: {BasePath}", _basePath);
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "Failed to create local storage base directory: {BasePath}", _basePath);
                throw;
            }
        }
    }
    
    private bool HasWriteAccess(string path)
    {
        try
        {
            // Attempt to create a temporary file to check write access
            string testFile = Path.Combine(path, ".ignore_write_access_test.tmp");
            using (var fs = File.Create(testFile)) { }
            File.Delete(testFile);
            return true;
        }
        catch
        {
            return false;
        }
    }
    
    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        // Check if the base path exists and is writable
        try
        {
            return Directory.Exists(_basePath) && HasWriteAccess(_basePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking health of local file storage service.");
            return false;
        }
    }

    private string GetFullPath(string storagePath)
    {
        // Combine and sanitize path to prevent directory traversal
        var safeRelativePath = storagePath.Replace("..", "").TrimStart('/', '\\'); // Basic sanitization
        return Path.Combine(_basePath, safeRelativePath);
    }

    public Task DeleteFileAsync(string storagePath, CancellationToken cancellationToken = default)
    {
        string fullPath = GetFullPath(storagePath);
        try
        {
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                _logger.LogInformation("Deleted local file: {FullPath}", fullPath);
            }
            else
            {
                _logger.LogWarning("Attempted to delete non-existent local file: {FullPath}", fullPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting local file: {FullPath}", fullPath);
            // Decide on re-throwing
        }
        return Task.CompletedTask;
    }

    public Task<Stream?> GetFileStreamAsync(string storagePath, CancellationToken cancellationToken = default)
    {
        string fullPath = GetFullPath(storagePath);
        try
        {
            if (File.Exists(fullPath))
            {
                // Open with FileShare.Read to allow concurrent reads if necessary
                return Task.FromResult<Stream?>(new FileStream(fullPath, FileMode.Open, FileAccess.Read, FileShare.Read, 4096, useAsync: true));
            }
            _logger.LogWarning("Local file not found: {FullPath}", fullPath);
            return Task.FromResult<Stream?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading local file stream: {FullPath}", fullPath);
            return Task.FromResult<Stream?>(null);
        }
    }

    public Task<string?> GetFileUrlAsync(string storagePath, TimeSpan? expiryTime = null, CancellationToken cancellationToken = default)
    {
        // Local files generally aren't served via direct URL from the backend like this.
        // They'd be served via a dedicated download endpoint (like the one designed).
        _logger.LogWarning("GetFileUrlAsync is not applicable for LocalFileStorageService. StoragePath: {StoragePath}", storagePath);
        return Task.FromResult<string?>(null);
    }

    public async Task SaveFileAsync(Stream stream, string storagePath, string contentType, CancellationToken cancellationToken = default)
    {
        string fullPath = GetFullPath(storagePath);
        try
        {
            // Ensure directory exists for the file
            string? directory = Path.GetDirectoryName(fullPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogDebug("Created directory for local file: {Directory}", directory);
            }

            // Use FileStream to write asynchronously
            using (var fileStream = new FileStream(fullPath, FileMode.Create, FileAccess.Write, FileShare.None, 4096, useAsync: true))
            {
                await stream.CopyToAsync(fileStream, cancellationToken);
            }
            _logger.LogInformation("Saved local file: {FullPath}", fullPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving local file: {FullPath}", fullPath);
            // Clean up potentially partial file
            if (File.Exists(fullPath))
            {
                try { File.Delete(fullPath); } catch { /* Best effort */ }
            }
            throw new IOException($"Failed to save file to local storage at path {fullPath}", ex);
        }
    }
}