using System.Reflection;
using System.Text.Json;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Workflow;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class WorkflowController : ControllerBase
{
    private readonly AppDbContext _dbContext;
    private readonly IAuthorizationService _authService;

    public WorkflowController(AppDbContext dbContext, IAuthorizationService authService)
    {
        _dbContext = dbContext;
        _authService = authService;
    }
    
    [HttpGet("flows")]
    public async Task<JsonElement> GetFlows()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = "Ethos.Workflows.flow.json";
        using var stream = assembly.GetManifestResourceStream(resourceName);
        if (stream == null)
            throw new FileNotFoundException($"Cannot find embedded resource '{resourceName}'.");
        var text = new StreamReader(stream).ReadToEnd();
        return JsonSerializer.Deserialize<JsonElement>(text);
    }

    [HttpGet("myTasks")]
    public async Task<IActionResult> GetMyTasks()
    {
        // Extract external user ID from the token
        var externalId = User.FindFirst("oid")?.Value;
        if (string.IsNullOrEmpty(externalId))
            return Unauthorized();

        // Retrieve local ApplicationUser
        var appUser = await _dbContext.Users
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.ExternalId == externalId);

        if (appUser == null)
            return Unauthorized();

        var userRoles = appUser.Roles.Select(r => r.Name).ToList();

        // Now fetch all workflow instances that belong to states in these roles
        var instances = await _dbContext.WorkflowInstances
            .Where(i => true) // FIXME
            .ToListAsync();

        return Ok(instances);
    }
    
    [HttpGet("profile")]
    public async Task<IActionResult> GetProfile()
    {
        // Extract OID claim from Azure AD B2C
        var externalId = User.FindFirst("http://schemas.microsoft.com/identity/claims/objectidentifier")?.Value;
        if (string.IsNullOrEmpty(externalId))
        {
            return Unauthorized();
        }

        // Lookup local user record
        var appUser = await _dbContext.Users
            .FirstOrDefaultAsync(u => u.ExternalId == externalId);

        if (appUser == null)
        {
            // Optionally create a new user record
            appUser = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                ExternalId = externalId,
                Email = User.FindFirst("emails")?.Value ?? "<EMAIL>",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.Users.Add(appUser);
            await _dbContext.SaveChangesAsync();
        }

        return Ok(new
        {
            appUser.Id,
            appUser.Email,
            Roles = appUser.Roles.Select(r => r.Name).ToList()
        });
    }
}