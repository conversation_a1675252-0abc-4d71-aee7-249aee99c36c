import aiohttp
import logging
from dataclasses import dataclass, field, asdict
from typing import Optional, List, Dict, Any
from uuid import UUID
import asyncio
from FlowTypes import *

class AddNewPatientState:
    """Parent base class for all states in AddNewPatient."""

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddNewPatientState':
        tpe = obj.get("$type")
        match tpe:
            case "AddedBasicInformation":
                return AddedBasicInformation.from_dict(obj)
            case "AddedContactInformation":
                return AddedContactInformation.from_dict(obj)
            case "AddedPhysicalMeasurements":
                return AddedPhysicalMeasurements.from_dict(obj)
            case "AddedInsuranceInformation":
                return AddedInsuranceInformation.from_dict(obj)
            case "Start":
                return Start.from_dict(obj)
            case _:
                raise ValueError(f"Unknown $type for this flow: {tpe}")

@dataclass
class AddedBasicInformation(AddNewPatientState):
    name: PersonName
    ssn: SSN
    demographics: Demographics
    guardian: Optional[Guardian]

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddedBasicInformation':
        return AddedBasicInformation(
                    name=obj["name"],
                    ssn=obj["ssn"],
                    demographics=obj["demographics"],
                    guardian=obj["guardian"]
                )

@dataclass
class AddedContactInformation(AddNewPatientState):
    name: PersonName
    ssn: SSN
    demographics: Demographics
    guardian: Optional[Guardian]
    contactInformation: ContactInformation

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddedContactInformation':
        return AddedContactInformation(
                    name=obj["name"],
                    ssn=obj["ssn"],
                    demographics=obj["demographics"],
                    guardian=obj["guardian"],
                    contactInformation=obj["contactInformation"]
                )

@dataclass
class AddedPhysicalMeasurements(AddNewPatientState):
    name: PersonName
    ssn: SSN
    demographics: Demographics
    guardian: Optional[Guardian]
    contactInformation: ContactInformation
    physicalMeasurements: PhysicalMeasurements

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddedPhysicalMeasurements':
        return AddedPhysicalMeasurements(
                    name=obj["name"],
                    ssn=obj["ssn"],
                    demographics=obj["demographics"],
                    guardian=obj["guardian"],
                    contactInformation=obj["contactInformation"],
                    physicalMeasurements=obj["physicalMeasurements"]
                )

@dataclass
class AddedInsuranceInformation(AddNewPatientState):
    name: PersonName
    ssn: SSN
    demographics: Demographics
    guardian: Optional[Guardian]
    contactInformation: ContactInformation
    physicalMeasurements: PhysicalMeasurements
    insurances: List[Insurance]

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddedInsuranceInformation':
        return AddedInsuranceInformation(
                    name=obj["name"],
                    ssn=obj["ssn"],
                    demographics=obj["demographics"],
                    guardian=obj["guardian"],
                    contactInformation=obj["contactInformation"],
                    physicalMeasurements=obj["physicalMeasurements"],
                    insurances=obj["insurances"]
                )

@dataclass
class Start(AddNewPatientState):

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'Start':
        return Start(
                    
                )

@dataclass
class StartRequest:
    pass  # No fields required for this step

@dataclass
class AddBasicInformationRequest:
    name: PersonName
    ssn: SSN
    demographics: Demographics
    guardian: Optional[Guardian]

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddBasicInformationRequest':
        return AddBasicInformationRequest(
                    name=obj["name"],
                    ssn=obj["ssn"],
                    demographics=obj["demographics"],
                    guardian=obj["guardian"]
                )

@dataclass
class AddContactInformationRequest:
    contactInformation: ContactInformation

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddContactInformationRequest':
        return AddContactInformationRequest(
                    contactInformation=obj["contactInformation"]
                )

@dataclass
class AddPhysicalMeasurementsRequest:
    physicalMeasurements: PhysicalMeasurements

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddPhysicalMeasurementsRequest':
        return AddPhysicalMeasurementsRequest(
                    physicalMeasurements=obj["physicalMeasurements"]
                )

@dataclass
class AddInsuranceInformationRequest:
    insurances: List[Insurance]

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'AddInsuranceInformationRequest':
        return AddInsuranceInformationRequest(
                    insurances=obj["insurances"]
                )

@dataclass
class ReviewAndCommitRequest:
    pass  # No fields required for this step

class AddNewPatientClient:
    def __init__(self, base_url: str, session: aiohttp.ClientSession, logger: logging.Logger = None):
        self.base_url = base_url
        self.session = session
        self.logger = logger if logger else logging.getLogger(__name__)

    async def get_state(self, instance_id: UUID) -> 'AddNewPatientState':
        url = f"{self.base_url}/api/AddNewPatient/state/{instance_id}"
        self.logger.debug(f"GET AddNewPatient state: {instance_id}")
        async with self.session.get(url) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            data = await resp.json()
        return AddNewPatientState.from_dict(data)

    async def start(self, instance_id: Optional[UUID], request: StartRequest) -> Tuple[UUID, 'AddNewPatientState']:
        url = f"{self.base_url}/api/AddNewPatient/start"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST AddNewPatient.Start: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new AddNewPatientState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, Start.from_dict(outputData)
    async def addbasicinformation(self, instance_id: Optional[UUID], request: AddBasicInformationRequest) -> Tuple[UUID, 'AddNewPatientState']:
        url = f"{self.base_url}/api/AddNewPatient/add-basic-information"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST AddNewPatient.AddBasicInformation: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new AddNewPatientState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, AddedBasicInformation.from_dict(outputData)
    async def addcontactinformation(self, instance_id: Optional[UUID], request: AddContactInformationRequest) -> Tuple[UUID, 'AddNewPatientState']:
        url = f"{self.base_url}/api/AddNewPatient/add-contact-information"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST AddNewPatient.AddContactInformation: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new AddNewPatientState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, AddedContactInformation.from_dict(outputData)
    async def addphysicalmeasurements(self, instance_id: Optional[UUID], request: AddPhysicalMeasurementsRequest) -> Tuple[UUID, 'AddNewPatientState']:
        url = f"{self.base_url}/api/AddNewPatient/add-physical-measurements"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST AddNewPatient.AddPhysicalMeasurements: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new AddNewPatientState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, AddedPhysicalMeasurements.from_dict(outputData)
    async def addinsuranceinformation(self, instance_id: Optional[UUID], request: AddInsuranceInformationRequest) -> Tuple[UUID, 'AddNewPatientState']:
        url = f"{self.base_url}/api/AddNewPatient/add-insurance-information"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST AddNewPatient.AddInsuranceInformation: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new AddNewPatientState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, AddedInsuranceInformation.from_dict(outputData)
    async def reviewandcommit(self, instance_id: Optional[UUID], request: ReviewAndCommitRequest) -> Tuple[UUID, None]:
        url = f"{self.base_url}/api/AddNewPatient/review-and-commit"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST AddNewPatient.ReviewAndCommit: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # final step => no new state returned
            return None

