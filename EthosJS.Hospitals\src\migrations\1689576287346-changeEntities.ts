import { MigrationInterface, QueryRunner } from 'typeorm';

export class changeEntities1689576287346 implements MigrationInterface {
    name = 'changeEntities1689576287346'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technician_schedules" ADD "capacity" integer NOT NULL DEFAULT 1');
      await queryRunner.query('ALTER TABLE "facilities" ALTER COLUMN "number_of_beds" DROP DEFAULT');
      await queryRunner.query('ALTER TABLE "technician_schedules" ADD CONSTRAINT "FK_755e058a3ce690fe565844f7f94" FOREIGN KEY ("technician_id") REFERENCES "technicians"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "technician_schedules" ADD CONSTRAINT "FK_7cd1ce1d642de278f001dc38998" FOREIGN KEY ("facility_id") REFERENCES "facilities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "schedules" ADD CONSTRAINT "FK_78640849585cf4a62df4373f384" FOREIGN KEY ("technician_id") REFERENCES "technicians"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "schedules" ADD CONSTRAINT "FK_9c67aa42d8a001d9f000260f1da" FOREIGN KEY ("study_id") REFERENCES "studies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "schedules" DROP CONSTRAINT "FK_9c67aa42d8a001d9f000260f1da"');
      await queryRunner.query('ALTER TABLE "schedules" DROP CONSTRAINT "FK_78640849585cf4a62df4373f384"');
      await queryRunner.query('ALTER TABLE "technician_schedules" DROP CONSTRAINT "FK_7cd1ce1d642de278f001dc38998"');
      await queryRunner.query('ALTER TABLE "technician_schedules" DROP CONSTRAINT "FK_755e058a3ce690fe565844f7f94"');
      await queryRunner.query('ALTER TABLE "facilities" ALTER COLUMN "number_of_beds" SET DEFAULT 0');
      await queryRunner.query('ALTER TABLE "technician_schedules" DROP COLUMN "capacity"');
    }

}
