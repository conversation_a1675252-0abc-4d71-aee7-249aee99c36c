﻿using System.Net.Http.Headers;
using Microsoft.AspNetCore.Connections.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json.Linq;

namespace Ethos.ReferenceData
{
    /// <summary>
    /// 
    /// </summary>
    public static class Extensions
    {
        public enum FilterOp
        {
            <PERSON>q,
            Ne,
            <PERSON>w,
            <PERSON>,
            <PERSON><PERSON>,
            <PERSON><PERSON>,
            <PERSON>,
            <PERSON><PERSON>,
            <PERSON>
        }

        public static IQueryable<ReferenceDataSetKeyValue> Filter(this IQueryable<ReferenceDataSetKeyValue> query, string? filter)
        {
            filter = filter?.Trim()?.ToLower();
            if (string.IsNullOrEmpty(filter))
                return query;

            return query.Filter("*", filter, FilterOp.Sw.ToString());

            //return query.Where(k => (k.Value.StringValue ?? string.Empty).ToLower().StartsWith(filter) ||
            //                         k.SetValues.Any(sv => (sv.Value.StringValue ?? string.Empty).ToLower().StartsWith(filter)));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="query"></param>
        /// <param name="property"></param>
        /// <param name="value"></param>
        /// <param name="listType"></param>
        /// <returns></returns>
        public static IQueryable<ReferenceDataSetKeyValue> Filter(this IQueryable<ReferenceDataSetKeyValue> query, string? property, object? value, string? op)
        {
            if (value is null)
                return query;

            FilterOp _filterOp = FilterOp.Sw;

            if (!string.IsNullOrEmpty(op) && !Enum.TryParse(op, true, out _filterOp))
                throw new Exception($"Invalid filter operator: {op}");

            if (string.IsNullOrEmpty(property))
                property = "*";

            var tmpResult = query.Where(k => k.SetValues.Any(sv => string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)));

            if (!tmpResult.Any())
                return tmpResult;

            ReferenceDataListType listType = ReferenceDataListType.String;

            if (!"*".Equals(property))
            {
                listType = tmpResult.Select(sk => sk.Value.List.Type).First();
                value = ReferenceDataListValue.ConvertValue(listType, value);
            }

            switch (listType)
            {
                case ReferenceDataListType.String:
                    var strVal = value?.ToString()?.ToLower() ?? string.Empty;
                    return _filterOp switch
                    {
                        FilterOp.Eq => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && string.Equals(sv.Value.StringValue.ToLower(), strVal))),
                        FilterOp.Ne => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && !string.Equals(sv.Value.StringValue.ToLower(), strVal))),
                        FilterOp.Sw => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.StringValue.ToLower().StartsWith(strVal))),
                        FilterOp.Ew => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.StringValue.ToLower().EndsWith(strVal))),
                        FilterOp.Co => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.StringValue.ToLower().Contains(strVal))),
                        _ => throw new Exception($"Invalid filter operator: {op}"),
                    };

                case ReferenceDataListType.Float:
                    if (value is double dblVal)
                    {
                        return _filterOp switch
                        {
                            FilterOp.Eq => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.FloatValue == dblVal)),
                            FilterOp.Ne => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.IntegerValue != dblVal)),
                            FilterOp.Gt => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.FloatValue > dblVal)),
                            FilterOp.Lt => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.FloatValue < dblVal)),
                            FilterOp.Ge => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.FloatValue >= dblVal)),
                            FilterOp.Le => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.FloatValue <= dblVal)),
                            _ => throw new Exception($"Invalid filter operator: {op}"),
                        };
                    }
                    break;

                case ReferenceDataListType.Integer:
                    if (value is long lngVal)
                    {
                        return _filterOp switch
                        {
                            FilterOp.Eq => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.IntegerValue == lngVal)),
                            FilterOp.Ne => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.IntegerValue != lngVal)),
                            FilterOp.Gt => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.IntegerValue > lngVal)),
                            FilterOp.Lt => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.IntegerValue < lngVal)),
                            FilterOp.Ge => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.IntegerValue >= lngVal)),
                            FilterOp.Le => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.IntegerValue <= lngVal)),
                            _ => throw new Exception($"Invalid filter operator: {op}"),
                        };
                    }
                    break;

                case ReferenceDataListType.Boolean:
                    if (value is bool boolVal)
                    {
                        return _filterOp switch
                        {
                            FilterOp.Eq => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.BooleanValue == boolVal)),
                            FilterOp.Ne => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.BooleanValue != boolVal)),
                            _ => throw new Exception($"Invalid filter operator: {op}"),
                        };
                    }
                    break;

                case ReferenceDataListType.DateTime:
                    if (value is DateTimeOffset dtVal)
                    {
                        return _filterOp switch
                        {
                            FilterOp.Eq => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.DateTimeValue == dtVal)),
                            FilterOp.Ne => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.DateTimeValue != dtVal)),
                            FilterOp.Gt => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.DateTimeValue > dtVal)),
                            FilterOp.Lt => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.DateTimeValue < dtVal)),
                            FilterOp.Ge => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.DateTimeValue >= dtVal)),
                            FilterOp.Le => tmpResult.Where(k => k.SetValues.Any(sv => (string.Equals(sv.Value.List.Name.ToLower(), property.ToLower()) || string.Equals("*", property)) && sv.Value.DateTimeValue <= dtVal)),
                            _ => throw new Exception($"Invalid filter operator: {op}"),
                        };
                    }
                    break;
            }
            return query;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="query"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static IQueryable<ReferenceDataSetKeyValue> ParseAndFilter(this IQueryable<ReferenceDataSetKeyValue> query, string? filter)
        {
            if (string.IsNullOrEmpty(filter))
                return query;

            var filterParts = filter.Split(' ');

            if (filterParts.Length < 3)
                return query.Filter("*", filter, FilterOp.Sw.ToString());

            return query.Filter(filterParts[0], string.Join(' ', filterParts[2..]), filterParts[1]);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="headerName"></param>
        /// <param name="values"></param>
        /// <returns></returns>
        public static ControllerBase WithHeader(this ControllerBase controller, string headerName, IEnumerable<string?> values, 
            IEqualityComparer<string?>? stringComparer = null)
        {
            stringComparer ??= StringComparer.CurrentCulture;
            values ??= [];
            values = values.Where(v => !string.IsNullOrEmpty(v));

            if (controller.HttpContext.Response.Headers.TryGetValue(headerName, out var headerVal))
            {
                var appended = values.Union(headerVal, stringComparer);
                controller.HttpContext.Response.Headers[headerName] = new StringValues([.. appended]);
            }
            else
                controller.HttpContext.Response.Headers.Append(headerName, new StringValues([.. values]));

            return controller;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="controller"></param>
        /// <param name="links"></param>
        /// <returns></returns>
        public static ControllerBase WithEtag(this ControllerBase controller, string etag, bool isWeak = true)
        {
            if (string.IsNullOrEmpty(etag))
                return controller;

            if (!isWeak)
            {
                if (!etag.StartsWith('"'))
                    etag = $"\"{etag}";

                if (!etag.EndsWith('"'))
                    etag = $"{etag}\"";
            }

            controller.HttpContext.Response.Headers.Link = new StringValues(etag);

            var etagObj = new EntityTagHeaderValue(etag, isWeak);
            return WithHeader(controller, "ETag", [etagObj.ToString()]);
        }
    }
}
