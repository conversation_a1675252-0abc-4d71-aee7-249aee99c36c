import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFacilityEquipments1689756799245 implements MigrationInterface {
    name = 'addFacilityEquipments1689756799245'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('CREATE TABLE "facility_equipments" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "equipment_id" integer NOT NULL, "facility_id" integer NOT NULL, "count" integer NOT NULL, CONSTRAINT "PK_8f3b5cdd5255e13ffa54aef6571" PRIMARY KEY ("id"))');
      await queryRunner.query('ALTER TABLE "facility_equipments" ADD CONSTRAINT "FK_9766edc5dad36c04e8c7b150841" FOREIGN KEY ("equipment_id") REFERENCES "equipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "facility_equipments" ADD CONSTRAINT "FK_80f7926fcc571fed98721bf652c" FOREIGN KEY ("facility_id") REFERENCES "facilities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "facility_equipments" DROP CONSTRAINT "FK_80f7926fcc571fed98721bf652c"');
      await queryRunner.query('ALTER TABLE "facility_equipments" DROP CONSTRAINT "FK_9766edc5dad36c04e8c7b150841"');
      await queryRunner.query('DROP TABLE "facility_equipments"');
    }

}
