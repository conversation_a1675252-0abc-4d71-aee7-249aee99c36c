﻿FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
# Install packages once, cache the debs across builds
RUN apt-get update \
    && apt-get install -y --no-install-recommends postgresql-client \
    && rm -rf /var/lib/apt/lists/*
WORKDIR /app
# ARG APP_UID=10001
RUN useradd -u 10001 -r -s /sbin/nologin appuser

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# COPY ["Directory.Packages.props", "Directory.Packages.props"]
COPY ["Ethos.Utilities/Ethos.Utilities.csproj", "Ethos.Utilities/"]
COPY ["Directory.Packages.props", "Ethos.Utilities/"]
COPY ["Ethos.Model/Ethos.Model.csproj", "Ethos.Model/"]
COPY ["Directory.Packages.props", "Ethos.Model/"]

COPY ["FastAuthClient/FastAuthClient.csproj", "FastAuthClient/"]
COPY ["Directory.Packages.props", "FastAuthClient/"]

COPY ["Ethos.ReferenceData.Client/Ethos.ReferenceData.Client.csproj", "Ethos.ReferenceData.Client/"]
COPY ["Directory.Packages.props", "Ethos.ReferenceData.Client/"]
COPY ["Ethos.Auth/Ethos.Auth.csproj", "Ethos.Auth/"]
COPY ["Directory.Packages.props", "Ethos.Auth/"]

COPY ["Ethos.Workflows/Ethos.Workflows.csproj", "Ethos.Workflows/"]
COPY ["Directory.Packages.props", "Ethos.Workflows/"]

RUN dotnet restore "Ethos.Workflows/Ethos.Workflows.csproj"

COPY ["Ethos.Utilities/", "Ethos.Utilities/"]
COPY ["Ethos.Model/", "Ethos.Model/"]
COPY ["Ethos.Auth/", "Ethos.Auth/"]
COPY ["Ethos.ReferenceData.Client/", "Ethos.ReferenceData.Client/"]
COPY ["Ethos.Workflows/", "Ethos.Workflows/"]
COPY ["FastAuthClient/", "FastAuthClient/"]

RUN dotnet build "Ethos.Workflows/Ethos.Workflows.csproj" -c $BUILD_CONFIGURATION -o /app/build

# Copy the absolute minimal information to reconstruct the git commit
COPY [".git/HEAD", "git/HEAD"]
COPY [".git/refs/*", "git/refs/"]
RUN bash -c ' \
    if [ ! -f "git/HEAD" ]; then \
        echo "unknown_branch" > GIT_BRANCH; \
        echo "unknown_commit" > GIT_COMMIT; \
    else \
        RAW_GIT_HEAD_CONTENT=$(cat git/HEAD); \
        if [[ $RAW_GIT_HEAD_CONTENT == ref:* ]]; then \
            BRANCH_FULL_PATH=$(echo "$RAW_GIT_HEAD_CONTENT" | cut -d" " -f2); \
            echo "${BRANCH_FULL_PATH#refs/heads/}" > GIT_BRANCH; \
            if [ -f "git/$BRANCH_FULL_PATH" ]; then \
                cat "git/$BRANCH_FULL_PATH" > GIT_COMMIT; \
            else \
                echo "commit_not_found_for_branch" > GIT_COMMIT; \
            fi; \
        else \
            # Detached HEAD: content of git/HEAD is the commit SHA
            echo "HEAD" > GIT_BRANCH; \
            cat git/HEAD > GIT_COMMIT; \
        fi; \
    fi; \
    echo "--- Extracted Git Info ---"; \
    echo "Branch: $(cat GIT_BRANCH)"; \
    echo "Commit: $(cat GIT_COMMIT)"; \
    echo "------------------------"; \
    rm -rf git; \
    '

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Ethos.Workflows/Ethos.Workflows.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM runtime AS final

COPY --from=publish /app/publish .
COPY --from=build /src/GIT_COMMIT .
COPY --from=build /src/GIT_BRANCH .

RUN export GIT_COMMIT=$(cat GIT_COMMIT) && \
    export GIT_BRANCH=$(cat GIT_BRANCH)

# Create a /app/tmp directory for temporary files and make sure it is accessible by the appuser
RUN mkdir -p /app/tmp && \
    chown appuser:appuser /app/tmp && \
    chmod 1755 /app/tmp

# --chmod=755 --chown=appuser:appuser
COPY "./Ethos.Workflows/entrypoint.sh" "entrypoint.sh"
RUN chmod +x ./entrypoint.sh

USER appuser
ENTRYPOINT ["./entrypoint.sh"]