using Ethos.ReferenceData;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Ethos.Auth;
using Microsoft.OpenApi.Models;
using Microsoft.OpenApi.Writers;
using Swashbuckle.AspNetCore.Swagger;
using Ethos.ReferenceData.Client;
//using Llc.GoodConsulting.AspNet.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers(options =>
{
})
    .AddNewtonsoftJson(options =>
{
    options.SerializerSettings.Converters.Add(new NewtonsoftJsonDynamicObjectConverter());
    options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
    options.SerializerSettings.Formatting = Formatting.Indented;
    options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v3", new OpenApiInfo
    {
        Title = "Ethos Reference Data API",
        Version = "v3",
        Description = "Ethos Reference Data API Documentation",
        Contact = new OpenApiContact
        {
            Name = "Ethos Team",
            Email = "<EMAIL>"
        }
    });

    // Force Swagger to use OpenAPI 3.0
    c.UseAllOfToExtendReferenceSchemas();

    // Add JWT Authentication support in Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        Description = "JWT Authorization header using the Bearer scheme."
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
});

builder.Services.AddSwaggerGen();

builder.Services.AddProblemDetails();

//builder.Services.AddLogging();

builder.Services.ConfigureEthosAuthorization(builder.Configuration);

//builder.Services.AddSingleton<ApiUsageMiddleware>().ConfigureApiUsageTracking(builder.Configuration);
//builder.Services.AddScoped<ApiUsageTransactionService>();
//builder.Services.AddScoped<ApiUsageCleanupService>();

// CORS is good
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowLocalhost",
        builder => builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

builder.Services.AddEthosAuthorization();

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory());
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true);
builder.Configuration.AddEnvironmentVariables();

builder.Services.AddSingleton(builder.Configuration);

builder.Services.AddDbContext<AppDbContext>(options =>
{
    var dataSourceBuilder = new NpgsqlDataSourceBuilder(builder.Configuration.GetConnectionString("DefaultConnection"));
    dataSourceBuilder.EnableDynamicJson();
    var dataSource = dataSourceBuilder.Build();
    options.UseNpgsql(dataSource, builder => builder.MigrationsHistoryTable("__EFMigrationsHistory", "RefData"));
}, contextLifetime: ServiceLifetime.Scoped, optionsLifetime: ServiceLifetime.Singleton);

builder.Services.AddHttpClient<IReferenceDataClient, ReferenceDataClient>();

var app = builder.Build();

app.UseCors("AllowLocalhost");

// Do EF migrations
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

    if (app.Environment.IsDevelopment())
    {
        //var pendingMigrations = context.Database.GetPendingMigrations();

        //if (context.Database.HasPendingModelChanges())
        //{
        //    var migrationName = "DynamicMigration_" + DateTime.Now.Ticks;
        //    var projectDir = Directory.GetCurrentDirectory();
        //    var efTool = "dotnet";
        //    var arguments = $"ef migrations add {migrationName} --project \"{projectDir}\"";

        //    ProcessStartInfo processInfo = new(efTool, arguments)
        //    {
        //        RedirectStandardOutput = true,
        //        RedirectStandardError = true,
        //        UseShellExecute = false,
        //        CreateNoWindow = true
        //    };

        //    Process process = new()
        //    {
        //        StartInfo = processInfo
        //    };
        //    process.Start();

        //    process.WaitForExit();

        //    var output = process.StandardOutput.ReadToEnd();
        //    var error = process.StandardError.ReadToEnd();
        //    Console.WriteLine($"Migration created: {output}");
        //    if (!string.IsNullOrEmpty(error))
        //        Console.WriteLine($"Migration Errors: {error}");

        //    if (process.ExitCode != 0)
        //        throw new Exception("Migration Creation Failed");

        //    // long enough to do the migration?
        //    Thread.Sleep(10000);
        //}
    }
    context.Database.EnsureCreated();
    context.Database.Migrate();
    //context.Database.EnsureCreated();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    //app.UseSwagger();

    app.UseSwagger(c =>
    {
        c.SerializeAsV2 = false; // Ensure OpenAPI 3.0 output
    });
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v3/swagger.json", "Ethos Reference Data API v3");
        c.RoutePrefix = "swagger";
    });
    //app.UseSwaggerUI();
}

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();
app.UseEthosAuthorization();
//app.UseApiUsageTracking();

app.UseEndpoints(endpoints =>
{
    // Updated custom endpoint to use proper serialization
    endpoints.MapGet("/api/openapi.json", async context =>
    {
        var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
        var swagger = swaggerProvider.GetSwagger("v3");

        // Use Swagger's built-in serializer
        var jsonWriter = new StringWriter();
        swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(jsonWriter.ToString());
    }).AllowAnonymous();

    endpoints.MapControllers();
});


//app.UseRouting().UseEndpoints(endpoints =>
//{
//    // Updated custom endpoint to use proper serialization
//    endpoints.MapGet("/api/openapi.json", async context =>
//    {
//        var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
//        var swagger = swaggerProvider.GetSwagger("v3");

//        // Use Swagger's built-in serializer
//        var jsonWriter = new StringWriter();
//        swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

//        context.Response.ContentType = "application/json";
//        await context.Response.WriteAsync(jsonWriter.ToString());
//    }).AllowAnonymous();
//    app.UseAuthentication();
//    app.UseAuthorization();
//    app.UseEthosAuthorization(() => new EthosAuthorizationOptions()
//    {
//        DefaultAllow = false,
//        UseProblemDetails = true,
//        ProblemDetails = new Microsoft.AspNetCore.Mvc.ProblemDetails()
//        {
//            Detail = "Fuck off",
//            Title = "Fuck off"
//        },
//        Configuration = builder.Configuration,
//    });
//    endpoints.MapControllers();
//    // Updated custom endpoint to use proper serialization
//    //endpoints.MapGet("/api/openapi.json", async context =>
//    //{
//    //    var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
//    //    var swagger = swaggerProvider.GetSwagger("v3");

//    //    // Use Swagger's built-in serializer
//    //    var jsonWriter = new StringWriter();
//    //    swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

//    //    context.Response.ContentType = "application/json";
//    //    await context.Response.WriteAsync(jsonWriter.ToString());
//    //}).WithMetadata(new { AllowAnonymous = true }).AllowAnonymous();
//});

//app.UseHttpsRedirection();
app.UseExceptionHandler();
app.UseStatusCodePages();

//app.MapControllers();

app.Run();

