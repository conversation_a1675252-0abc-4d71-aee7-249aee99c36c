{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },

  "UploadTokenJwt": {
    "SecretKey": "VerySecureJWTKeyMustBeAtLeast32Chars!",
    "Issuer": "our-app-name",
    "Audience": "our-app-users",
    "ExpiryMinutes": 5
  },

  "Storage": {
    "Provider": "Local",
    "LocalBasePath": "./tmp/"
  },

  "Auth": {
    "Local": {
      "Enabled": true,
      "Key": "VerySecureJWTKeyMustBeAtLeast32Chars!",
      "Issuer": "our-app-name",
      "Audience": "our-app-users"
    },

    "AzureAD": {
      "Enabled": true,
      "ClientId": "a8d641a8-7112-4c1f-ad32-cf156f81459c",
      "TenantId": "5d067e44-b2ee-4df6-9bdb-839a7d05c039",
      "Instance": "https://ethoshbcustnonprod.b2clogin.com/",
      "Issuer": "https://ethoshbcustnonprod.b2clogin.com/5d067e44-b2ee-4df6-9bdb-839a7d05c039/v2.0/",
      "Policy": "B2C_1_Sign_Up_and_Sign_On"
    }
  },
  //"BaseUrl": "https://pre-prod.fastauth.com/api/external",
  "FastAuth": {
    "BaseUrl": "https://goodconsulting.llc/api/external",
    "ApiKey": "f84658f6-22ec-8ed3-2677-eb6bcf951d0b",
    "ApiSecret": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************.0LUduhGsNDMBVizHTkmwEdpRrnrJvZ6vhlQi9TxQABc",
    "WebhookId": "your-webhook-id",
    "WebhookSigningSecret": "webhooksecret1234567890",
    "WebhookTimestampToleranceSeconds": 300,
    "DefaultAssignedOwnerForAuthorization": "phc-support",
    "DefaultDiagnosisQualifierCode": "ABF",
    "ProcessingTimeoutMinutes": 30,
    "WebhookWaitTimeoutHours": 24,
    "MaxRetriesForStep": 3,
    "RetryDelayMinutes": 15
  }
}
