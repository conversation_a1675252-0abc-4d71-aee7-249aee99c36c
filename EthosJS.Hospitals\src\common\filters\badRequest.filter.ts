import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  BadRequestException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ValidationError } from 'class-validator';
import { Response } from 'express';
import * as _ from 'lodash';

@Catch(BadRequestException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(public reflector: Reflector) {}

  catch(exception: BadRequestException, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    let statusCode = exception.getStatus();
    const exceptionResponse = <any>exception.getResponse();
    let validationErrors = exceptionResponse.message;
    if (
      Array.isArray(validationErrors) &&
        validationErrors[0] instanceof ValidationError
    ) {
      statusCode = HttpStatus.UNPROCESSABLE_ENTITY;
      validationErrors = this.validationFilter(exceptionResponse.message);
    }

    exceptionResponse.statusCode = statusCode;
    exceptionResponse.message = validationErrors;

    response.status(statusCode).json(exceptionResponse);
  }

  private validationFilter(validationErrors: ValidationError[]): any {
    return validationErrors.map(validationError => {
      const constraints: { [key: string]: any } = {};
      let children = validationError.children;
      const childrenExist = children.length > 0;

      for (const [constraintKey, constraint] of Object.entries(
          validationError?.constraints || '',
      )) {
        // convert default messages
        if (!constraint) {
          // convert error message to error.fields.{key} syntax for i18n translation
          constraints[constraintKey] =
              'error.fields.' + _.snakeCase(constraintKey);
        }
      }

      if (childrenExist) {
        children = this.validationFilter(validationError.children);
      }

      return { ...validationError, constraints, children };
    });
  }
}
