﻿using System.Net;
using System.Net.Http.Headers;
using System.Text;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.Extensions.Primitives;
using System.Security.Cryptography;

namespace Ethos.PlatformManager
{
    /// <summary>
    /// 
    /// </summary>
    public class PasswordHasher
    {
        // These constants define the parameters for PBKDF2.
        // Increasing iterations or salt size increases security but also computation time.
        private const int SaltSize = 16; // 16 bytes = 128 bits
        private const int KeySize = 32;  // 32 bytes = 256 bits (for SHA256)
        private const int Iterations = 10000; // Recommended minimum for PBKDF2 (NIST recommends 10,000 to 100,000 or more)

        /// <summary>
        /// Hashes a plaintext password using a randomly generated salt and PBKDF2.
        /// The resulting hash includes the salt, iteration count, and the derived key,
        /// separated by delimiters for easy storage and retrieval.
        /// </summary>
        /// <param name="password">The plaintext password to hash.</param>
        /// <returns>A string representing the hashed password, including salt and iteration count.</returns>
        public string HashPassword(string password)
        {
            // 1. Generate a random salt
            byte[] salt;
            using (var rng = RandomNumberGenerator.Create())
                rng.GetBytes(salt = new byte[SaltSize]);

            // 2. Derive a key from the password and salt using PBKDF2
            // Rfc2898DeriveBytes implements PBKDF2 (Password-Based Key Derivation Function 2)
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
            byte[] hash = pbkdf2.GetBytes(KeySize); // Get the derived key (hash)

            // 3. Combine the salt and hash into a single string for storage
            // Format: {iterations}.{salt_base64}.{hash_base64}
            // This allows us to retrieve the salt and iterations needed for verification.
            return $"{Iterations}.{Convert.ToBase64String(salt)}.{Convert.ToBase64String(hash)}";
        }

        /// <summary>
        /// Verifies a plaintext password against a stored hashed password.
        /// </summary>
        /// <param name="password">The plaintext password entered by the user.</param>
        /// <param name="hashedPassword">The stored hashed password obtained from HashPassword.</param>
        /// <returns>True if the password matches the hash, false otherwise.</returns>
        public bool VerifyPassword(string password, string hashedPassword)
        {
            // 1. Parse the stored hashed password to extract iterations, salt, and hash
            var parts = hashedPassword.Split('.');
            if (parts.Length != 3)
                return false;

            int iterations = int.Parse(parts[0]);
            byte[] salt = Convert.FromBase64String(parts[1]);
            byte[] storedHash = Convert.FromBase64String(parts[2]);

            // 2. Re-derive the hash using the provided plaintext password and the extracted salt/iterations
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, iterations, HashAlgorithmName.SHA256);
            byte[] newHash = pbkdf2.GetBytes(KeySize);

            // 3. Compare the newly derived hash with the stored hash
            // Use CryptographicOperations.FixedTimeEquals for constant-time comparison
            // to prevent timing attacks. This is available in .NET Core 3.0+
            if (newHash.Length != storedHash.Length)
                return false;
            return CryptographicOperations.FixedTimeEquals(newHash, storedHash);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public interface IBasicAuthUserProvider
    {
        Task<bool> ValidateCredentials(string username, string password);

        Task<BasicUserCredential> CreateCredential(string username, string password);
    }

    /// <summary>
    /// 
    /// </summary>
    public struct BasicUserCredential
    {
        public string Username { get; set; }
        public string PasswordHash { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="config"></param>
    public class BasicAuthUserProvider(IConfiguration config) : IBasicAuthUserProvider
    {
        readonly IConfiguration _config = config;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public Task<bool> ValidateCredentials(string username, string password)
        {
            var passwordHash = _config.GetValue<string>($"Auth:Basic:{username.ToLower()}");

            if (string.IsNullOrEmpty(passwordHash))
                return Task.FromResult(false);

            var hasher = new PasswordHasher();

            if (!hasher.VerifyPassword(password, passwordHash))
                return Task.FromResult(false);

            return Task.FromResult(true);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public Task<BasicUserCredential> CreateCredential(string username, string password)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(username))
                throw new ArgumentException("Username and password are required.");

            var hasher = new PasswordHasher();
            var hash = hasher.HashPassword(password);
            return Task.FromResult(new BasicUserCredential { Username = username.ToLower(), PasswordHash = hash });
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class BasicAuthenticationOptions : AuthenticationSchemeOptions
    {
        public string Realm { get; set; } = "Ethos";
    }

    /// <summary>
    /// 
    /// </summary>
    public class BasicAuthenticationHandler : AuthenticationHandler<BasicAuthenticationOptions>
    {
        private readonly IBasicAuthUserProvider _userService;

        public BasicAuthenticationHandler(
            IOptionsMonitor<BasicAuthenticationOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            IBasicAuthUserProvider userService)
            : base(options, logger, encoder)
        {
            _userService = userService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            if (!Request.Headers.TryGetValue("Authorization", out StringValues value))
            {
                // No Authorization header found, so no authentication attempt
                return AuthenticateResult.NoResult();
            }

            if (!AuthenticationHeaderValue.TryParse(value, out var authenticationHeader))
            {
                // Invalid Authorization header format
                return AuthenticateResult.Fail("Invalid authorization header format.");
            }

            if (authenticationHeader.Scheme != AuthenticationSchemes.Basic.ToString())
            {
                // Not a Basic scheme header
                return AuthenticateResult.NoResult();
            }

            if (string.IsNullOrEmpty(authenticationHeader.Parameter))
                return AuthenticateResult.NoResult();

            string username;
            string password;
            try
            {
                var credentialBytes = Convert.FromBase64String(authenticationHeader.Parameter);
                var credentials = Encoding.UTF8.GetString(credentialBytes).Split(':', 2); // Split only on first colon

                if (credentials.Length != 2)
                    return AuthenticateResult.Fail("Invalid authentication credential format.");

                username = credentials[0];
                password = credentials[1];
            }
            catch (Exception ex)
            {
                return AuthenticateResult.Fail($"Error decoding authentication credentials: {ex.Message}");
            }

            // Validate credentials using the injected service
            bool isValidUser = await _userService.ValidateCredentials(username, password);

            if (!isValidUser)
                return AuthenticateResult.Fail("Invalid username or password.");

            // Authentication successful, create claims and principal
            var claims = new[] {
                new Claim(ClaimTypes.NameIdentifier, username),
                new Claim(ClaimTypes.Name, username),
                // Add other claims here (e.g., roles from your user store)
            };
            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return AuthenticateResult.Success(ticket);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="properties"></param>
        /// <returns></returns>
        protected override async Task HandleChallengeAsync(AuthenticationProperties properties)
        {
            Response.Headers.WWWAuthenticate = $"Basic realm=\"{Options.Realm}\"";
            Response.StatusCode = (int)HttpStatusCode.Unauthorized;
            await Response.CompleteAsync();
        }
    }
}

