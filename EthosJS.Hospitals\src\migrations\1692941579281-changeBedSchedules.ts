import { MigrationInterface, QueryRunner } from 'typeorm';
import { IFacilityEquipment } from '@app/modules/facility/types';

export class changeBedSchedules1692941579281 implements MigrationInterface {
    name = 'changeBedSchedules1692941579281'

    public async up(queryRunner: QueryRunner): Promise<void> {
      const equipments = await queryRunner.query('SELECT * FROM equipments');
      const equipmentsMap = equipments.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      let offset = 0;
      const limit = 100;
      let bedSchedules = await queryRunner.query('SELECT id, equipments FROM bed_schedules OFFSET $1 LIMIT $2', [offset, limit]);

      while(bedSchedules.length) {
        for (const bedSchedule of bedSchedules) {
          const equipments = Object.keys(bedSchedule.equipments)
            .map((equipmentId) => {
              const equipmentCount = bedSchedule.equipments[equipmentId];
              const equipment = equipmentsMap[equipmentId];

              return {
                equipmentId,
                equipmentName: equipment.name,
                count: equipmentCount,
              };
            })
            .reduce((acc: Record<number, IFacilityEquipment>, item: any) => {
              acc[item.equipmentId] = item;

              return acc;
            }, {});


          await queryRunner.query('UPDATE bed_schedules SET equipments = $1 WHERE id = $2', [equipments, bedSchedule.id]);
        }

        offset += limit;
        bedSchedules = await queryRunner.query('SELECT id, equipments FROM bed_schedules OFFSET $1 LIMIT $2', [offset, limit]);
      }

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      let offset = 0;
      const limit = 100;
      let bedSchedules = await queryRunner.query('SELECT id, equipments FROM bed_schedules OFFSET $1 LIMIT $2', [offset, limit]);

      while(bedSchedules.length) {
        for (const bedSchedule of bedSchedules) {
          const equipments = Object.values(bedSchedule.equipments)
            .reduce((acc: Record<number, number>, item: any) => {
              acc[item.equipmentId] = item.count;

              return acc;
            }, {});


          await queryRunner.query('UPDATE bed_schedules SET equipments = $1 WHERE id = $2', [equipments, bedSchedule.id]);
        }

        offset += limit;
        bedSchedules = await queryRunner.query('SELECT id, equipments FROM bed_schedules OFFSET $1 LIMIT $2', [offset, limit]);
      }
    }

}
