using System.Collections;
using System.Collections.Immutable;
using System.Diagnostics;
using System.Reflection;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Model.Scheduling;
using Ethos.ReferenceData.Client;
using Ethos.Workflows.Api;
using Ethos.Workflows.Api.Analysis;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Testcontainers.PostgreSql;
using Npgsql;

namespace Ethos.Workflows.Tests;

public sealed record Scope : IDisposable, IAsyncDisposable
{
    public List<DisposeAction> OnExit = new List<DisposeAction>();
    
    public abstract record DisposeAction;
    public sealed record SimpleAction(Action Action) : DisposeAction;
    public sealed record AsyncAction(Func<Task> Action) : DisposeAction;
    
    public void Dispose()
    {
        // Run all actions in reverse order
        for (int i = OnExit.Count - 1; i >= 0; i--)
        {
            try
            {
                switch (OnExit[i])
                {
                    case SimpleAction simpleAction:
                        simpleAction.Action();
                        break;
                    case AsyncAction asyncAction:
                        asyncAction.Action().GetAwaiter().GetResult(); // Synchronously wait for the task to complete
                        break;
                    default:
                        throw new InvalidOperationException("Unknown action type in scope cleanup.");
                }
            }
            catch (Exception ex)
            {
                // Log or handle the exception as needed
                Console.WriteLine($"Error during scope cleanup: {ex.Message}");
            }
        }
    }
    
    public async ValueTask DisposeAsync()
    {
        // Run all actions in reverse order asynchronously
        for (int i = OnExit.Count - 1; i >= 0; i--)
        {
            try
            {
                switch (OnExit[i])
                {
                    case SimpleAction simpleAction:
                        simpleAction.Action();
                        break;
                    case AsyncAction asyncAction:
                        await asyncAction.Action();
                        break;
                    default:
                        throw new InvalidOperationException("Unknown action type in scope cleanup.");
                }
            }
            catch (Exception ex)
            {
                // Log or handle the exception as needed
                Console.WriteLine($"Error during scope cleanup: {ex.Message}");
            }
        }
    }
    
    public void Defer(Action action)
    {
        if (action == null)
            throw new ArgumentNullException(nameof(action), "Action cannot be null");

        OnExit.Add(new SimpleAction(action));
    }
    
    public void Defer(Func<Task> action)
    {
        if (action == null)
            throw new ArgumentNullException(nameof(action), "Action cannot be null");

        OnExit.Add(new AsyncAction(action));
    }
    
    public void DeferDisposal(IDisposable disposable)
    {
        if (disposable == null)
            throw new ArgumentNullException(nameof(disposable), "Disposable cannot be null");

        OnExit.Add(new SimpleAction(disposable.Dispose));
    }
    
    public void DeferDisposal(IAsyncDisposable asyncDisposable)
    {
        if (asyncDisposable == null)
            throw new ArgumentNullException(nameof(asyncDisposable), "AsyncDisposable cannot be null");

        // Correct - This creates a delegate that will call DisposeAsync() later.
        OnExit.Add(new AsyncAction(() => asyncDisposable.DisposeAsync().AsTask()));
    }
}

public static class Helpers
{
    #region Database Helpers
    public static IQueryable<object> Set(this DbContext ctx, Type entityType)
    {
        // 1. Find the generic method definition for "Set<T>()", which has no parameters.
        var setMethod = typeof(DbContext).GetMethod(nameof(DbContext.Set), Type.EmptyTypes);

        // 2. Make that generic method specific to the entityType provided.
        var genericSetMethod = setMethod!.MakeGenericMethod(entityType);
    
        // 3. Invoke the specific method on the context instance.
        var dbSet = genericSetMethod.Invoke(ctx, null);

        // 4. The result (a DbSet<TEntity>) can be cast directly to IQueryable<object> due to covariance.
        return (IQueryable<object>)dbSet!;
    }
    
    public static DbContext CreateInMemoryDatabase(Scope scope)
    {
        var options = new DbContextOptionsBuilder<DbContextBase>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString()) // Use a unique name for the in-memory database
            .Options;

        var ctx = new DbContextBase(options, null);
        scope.Defer(() => ctx.Dispose()); // Ensure context is disposed at the end of the scope
        ctx.Database.EnsureCreated(); // Create the in-memory database
        return ctx;
    }

    public static async Task<DbContext> CreateInMemorySqliteDatabase(Scope scope)
    {
        var connection = new Microsoft.Data.Sqlite.SqliteConnection("DataSource=:memory:");
        scope.Defer(() => connection.Dispose()); // Ensure connection is disposed at the end of the scope
        connection.Open();

        var options = new DbContextOptionsBuilder<DbContextBase>()
            .UseSqlite(connection) // Use the SQLite provider
            .Options;
    
        var ctx = new DbContextBase(options, null);
        scope.Defer(() => ctx.Dispose()); // Ensure context is disposed at the end of the scope
        await ctx.Database.EnsureCreatedAsync(); // Create the in-memory database
        return ctx;
    }
    
    public static async Task<DbContext> CreateTestPostgresDatabase(Scope scope)
    {
        // Define the PostgreSQL container configuration
        var postgresContainer = new PostgreSqlBuilder()
            .WithImage("postgres:16-alpine") // Use a lightweight, versioned image
            .Build();

        // Defer container disposal to the end of the scope
        // The .AsAsyncDisposable() is important for proper cleanup.
        scope.DeferDisposal(postgresContainer); 
        
        // Start the container. This will pull the image if it's not present.
        await postgresContainer.StartAsync();

        // Get the dynamically generated connection string from the running container
        var connectionString = postgresContainer.GetConnectionString();

        var options = new DbContextOptionsBuilder<DbContextBase>()
            .UseNpgsql(connectionString) // Use the Npgsql provider for PostgreSQL
            .Options;

        var ctx = new DbContextBase(options, null);
        scope.Defer(() => ctx.Dispose());
        
        // Ensure the schema is created in the test database
        await ctx.Database.EnsureCreatedAsync(); 
        
        return ctx;
    }
    #endregion
    
    #region ADTs and Types
    public static bool IsSealedRecord(this Type type)
    {
        if (!(type.IsClass && type.IsSealed))      // sealed class?
            return false;

        const BindingFlags BF = BindingFlags.Instance |
                                BindingFlags.NonPublic |
                                BindingFlags.Public |
                                BindingFlags.DeclaredOnly;

        // 1️⃣ EqualityContract property synthesised for every record
        var ecProp = type.GetProperty("EqualityContract", BF);
        if (ecProp != null && ecProp.PropertyType == typeof(Type))
            return true;

        // 2️⃣ Fallback: PrintMembers method (also synthesised)
        var pm = type.GetMethod("PrintMembers", BF);
        return pm != null;        // true == very likely a record
    }
    
    public static List<Type> GetAllConcreteDerivedTypes(Type parentType)
    {
        var assembly = parentType.Assembly;
        // Find all types that derive from the query type
        return assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && parentType.IsAssignableFrom(t))
            .ToList();
    }
    
    public static bool HasProperty(this Type entityType, string propertyName, Type? type = null)
    {
        var property = entityType.GetProperty(propertyName);
        if (property == null) return false;
        // If type is provided, check if it has the property
        if (type != null)
        {
            if (property.PropertyType != type)
            {
                // If the property exists but is not of the expected type, return false
                return false;
            }
        }
        return true;
    }
    #endregion

    #region Nullability Validation

    private static readonly NullabilityInfoContext NCtx = new();
    
    private static readonly HashSet<Type> Terminal =
    [
        typeof(string),
        typeof(decimal),
        typeof(DateTime),
        typeof(DateTimeOffset),
        typeof(Guid),
        typeof(TimeSpan)
    ];
    
    public static void ValidateInstance(object dto, Func<Type, PropertyInfo, bool>? shouldSkip = null)
    {
        if (dto == null)
            throw new ArgumentNullException(nameof(dto), "DTO cannot be null");

        // Reference-equality comparer prevents Equals/GetHashCode surprises
        var visited = new HashSet<object>(ReferenceEqualityComparer.Instance);
        ActuallyValidateInstance(dto, visited, dto.GetType().Name, shouldSkip ?? (static (_, _) => false));
    }

    private static void ActuallyValidateInstance(object dto,
                                                 HashSet<object> visited,
                                                 string path,
                                                 Func<Type, PropertyInfo, bool> shouldSkip)
    {
        // Stop if we have already seen this exact instance
        if (!visited.Add(dto)) return;

        var dtoType = dto.GetType();

        foreach (var prop in dtoType.GetProperties(BindingFlags.Public | BindingFlags.Instance))
        {
            if (shouldSkip(dtoType, prop))
                continue;
            
            // Ignore indexers
            if (prop.GetIndexParameters().Length != 0)
                continue;
            
            var value= prop.GetValue(dto);
            var nullability = NCtx.Create(prop).ReadState;    // Nullable | NotNull | Unknown
            var nextPath    = $"{path}.{prop.Name}";

            if (value == null)
            {
                // Non-nullable value-types can never be null
                if (prop.PropertyType.IsValueType &&
                    Nullable.GetUnderlyingType(prop.PropertyType) == null)
                    throw new InvalidOperationException(
                        $"{nextPath} is a non-nullable struct, but it's null.");

                // Non-nullable reference type?
                if (nullability == NullabilityState.NotNull)
                    throw new InvalidOperationException(
                        $"{nextPath} is marked non-nullable, but it's null.");

                continue;   // nullable is allowed
            }

            // Type check
            value.Should().BeAssignableTo(prop.PropertyType,
                $"Expected {nextPath} to be {prop.PropertyType.Name}, got {value.GetType().Name}");

            // ----  recurse --------------------------------------------------

            var valType = value.GetType();

            // Skip primitives / well-known terminals
            if (valType.IsPrimitive || Terminal.Contains(valType)) continue;

            // Collections (but NOT string)
            if (value is IEnumerable enumerable && valType != typeof(string))
            {
                int idx = 0;
                foreach (var elem in enumerable)
                {
                    if (elem == null)      // allow nullable element types
                    {
                        idx++;
                        continue;
                    }

                    if (visited.Contains(elem))
                    {
                        idx++;
                        continue; // already checked this object elsewhere
                    }

                    ActuallyValidateInstance(elem, visited, $"{nextPath}[{idx}]", shouldSkip);
                    idx++;
                }

                continue;
            }

            // Simple reference-type – recurse
            ActuallyValidateInstance(value, visited, nextPath, shouldSkip);
        }
    }
    
    private static void ActuallyValidateDbo(
        object dto, HashSet<object> visited,
        string path, Func<Type, PropertyInfo, bool> shouldSkip)
    {
        // Stop if we have already seen this exact instance
        if (!visited.Add(dto)) return;

        var dboType = dto.GetType();

        foreach (var prop in dboType.GetProperties(BindingFlags.Public | BindingFlags.Instance))
        {
            if (shouldSkip(dboType, prop))
                continue;
            // Ignore indexers
            if (prop.GetIndexParameters().Length != 0)
                continue;
            
            var value= prop.GetValue(dto);
            var nullability = NCtx.Create(prop).ReadState;    // Nullable | NotNull | Unknown
            var nextPath    = $"{path}.{prop.Name}";

            if (value == null)
            {
                // Non-nullable value-types can never be null
                if (prop.PropertyType.IsValueType &&
                    Nullable.GetUnderlyingType(prop.PropertyType) == null)
                    throw new InvalidOperationException(
                        $"{nextPath} is a non-nullable struct, but it's null.");

                // Non-nullable reference type?
                if (nullability == NullabilityState.NotNull)
                    throw new InvalidOperationException(
                        $"{nextPath} is marked non-nullable, but it's null.");

                continue;   // nullable is allowed
            }

            // Type check
            value.Should().BeAssignableTo(prop.PropertyType,
                $"Expected {nextPath} to be {prop.PropertyType.Name}, got {value.GetType().Name}");

            // ----  recurse --------------------------------------------------

            var valType = value.GetType();

            // Skip primitives / well-known terminals
            if (valType.IsPrimitive || Terminal.Contains(valType)) continue;

            // Collections (but NOT string)
            if (value is IEnumerable enumerable && valType != typeof(string))
            {
                int idx = 0;
                foreach (var elem in enumerable)
                {
                    if (elem == null)      // allow nullable element types
                    {
                        idx++;
                        continue;
                    }

                    if (visited.Contains(elem))
                    {
                        idx++;
                        continue; // already checked this object elsewhere
                    }

                    ActuallyValidateDbo(elem, visited, $"{nextPath}[{idx}]", shouldSkip);
                    idx++;
                }

                continue;
            }

            // Simple reference-type – recurse
            ActuallyValidateDbo(value, visited, nextPath, shouldSkip);
        }
    }

    #endregion

    #region Application Specific Helpers

    public static List<Type> GetAllEntityTypes()
    {
        var iEntityType = typeof(Ethos.Model.IEntity);
        var assembly = iEntityType.Assembly;
        return assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && iEntityType.IsAssignableFrom(t))
            .ToList();
    }
    
    public static object GetEntityIdUnsafe(object entity)
    {
        // This is a workaround to get the Id property from an entity.
        // It assumes that the entity has a property named "Id" of type Guid.
        var idProperty = entity.GetType().GetProperty("Id");
        if (idProperty == null)
            throw new InvalidOperationException($"Entity {entity.GetType().Name} does not have an Id property.");
        
        var idValue = idProperty.GetValue(entity);
        return idValue!;
    }

    public sealed record AnyController(
        Type ControllerType)
    {
        public string Name => ControllerType.Name;
        
        // public abstract class EntityControllerBase<TEntity, TInputDto, TOutputDto, TQuery> : ControllerBase
        public Type EntityType => ControllerType.BaseType!.GetGenericArguments()[0];
        public Type InputDtoType = ControllerType.BaseType!.GetGenericArguments()[1];
        public Type OutputDtoType = ControllerType.BaseType!.GetGenericArguments()[2];
        public Type QueryType = ControllerType.BaseType!.GetGenericArguments()[3];
        
        public object? NewInstance(DbContext ctx)
        {
            // Create an instance of the controller with the DbContext
            return Activator.CreateInstance(ControllerType, new object[] { ctx });
        }
        
        public MethodInfo? ApplyIncludesMethod => ControllerType.GetMethod(
            "ApplyIncludes", 
            BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);
        
        public MethodInfo? MapToDtoMethod => ControllerType.GetMethod(
            "MapToDto",
            BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.FlattenHierarchy,
            binder: null,
            types: new[] { EntityType },
            modifiers: null);

        public MethodInfo? CreateOrUpdateEntityMethod => ControllerType.GetMethod(
            "CreateOrUpdateEntity",
            BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.FlattenHierarchy,
            binder: null,
            types: new[] { EntityType, InputDtoType, typeof(Guid?) },
            modifiers: null);
    }

    public static List<AnyController> GetAllEntityControllers()
    {
        var entityControllerBaseType = typeof(Ethos.Workflows.Api.EntityControllerBase<,,,>);
        var assembly = entityControllerBaseType.Assembly;
        return assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && 
                        t.BaseType != null && 
                        t.BaseType.IsGenericType &&
                        t.BaseType.GetGenericTypeDefinition() == entityControllerBaseType)
            .Select(t => new AnyController(t))
            .ToList();
    }
    
    private static bool IsEfEntity(Type clrType, DbContext ctx)
        => ctx.Model.FindEntityType(clrType) != null;
    
    
    public static List<Type> GetAllQueryTypes()
    {
        var iPrimitiveQuery = typeof(Ethos.Model.IPrimitiveQuery);
        var assembly = iPrimitiveQuery.Assembly;
        return assembly.GetTypes()
            .Where(t => t.IsClass && t.IsAbstract && iPrimitiveQuery.IsAssignableFrom(t))
            .ToList();
    }
    
    public static List<Type> GetAllQueryConstructorTypes(Type queryType) =>
        Helpers.GetAllConcreteDerivedTypes(queryType);

    public static List<Type> GetAllDtoTypes()
    {
        var result = new List<Type>();
        // List all types that end with "Dto" and are not abstract in all assemblies
        foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
        {
            try
            {
                var dtoTypes = assembly.GetTypes()
                    .Where(t => t.IsClass && !t.IsAbstract && t.Name.EndsWith("Dto"))
                    .Where(t => t.FullName?.StartsWith("Ethos.") == true)
                    .Where(t => t.FullName?.StartsWith("Ethos.ReferenceData") == false)
                    .ToList();
                result.AddRange(dtoTypes);
            }
            catch (ReflectionTypeLoadException)
            {
                // Ignore assemblies that cannot be loaded
            }
        }
        return result;
    }
    
    public static List<(Type from, Type to)> GetToEntityConverters()
    {
        var classType = typeof(Converters);
        return classType.GetMethods()
            .Where(m => m.Name == "ToEntity" && m.IsStatic && m.GetParameters().Length == 1)
            .Select(m => (m.GetParameters()[0].ParameterType, m.ReturnType))
            .ToList();
    }
    
    public static List<(Type from, Type to)> GetToDtoConverters()
    {
        var classType = typeof(Converters);
        return classType.GetMethods()
            .Where(m => m.Name == "ToDto" && m.IsStatic && m.GetParameters().Length == 1)
            .Select(m => (m.GetParameters()[0].ParameterType, m.ReturnType))
            .ToList();
    }
    
    public static bool IsPrimitiveType(Type type) => DataModel.IsPrimitiveType(type);
    public static bool IsDtoType(Type type) => DataModel.IsDtoType(type);
    public static bool IsValidDtoProperty(Type type) => DataModel.IsValidDtoPropertyType(type);

    #endregion
}