﻿using System.Data;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using CsvHelper;
using CsvHelper.Configuration;
using System.Globalization;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using System.Linq.Expressions;
using Ethos.Auth;
using Ethos.ReferenceData.Client;
using Ethos.Model;
using System.Collections.Specialized;

namespace Ethos.ReferenceData.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Authorize]
    [ApiController]
    [EthosAuthFeature(Name = FeatureConstants.ReferenceData)]
    [Route("api/reference")]
    public class ReferenceDataController : Controller
    {
        readonly ILogger<ReferenceDataController> logger;
        readonly IServiceScopeFactory scopeFactory;
        readonly AppDbContext dbContext;
        readonly IConfiguration configuration;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="scopeFactory"></param>
        public ReferenceDataController(ILogger<ReferenceDataController> logger,
                                       IServiceScopeFactory scopeFactory,
                                       AppDbContext dbContext,
                                       IConfiguration configuration)
        {
            this.logger = logger;
            this.scopeFactory = scopeFactory;
            this.dbContext = dbContext;
            this.configuration = configuration;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.PlatformAdministrator)]
        [Route("bootstrap")]
        public async Task<IActionResult> Bootstrap()
        {
            using (var context = scopeFactory.CreateScope())      
            {
                try
                {
                    var client = context.ServiceProvider.GetRequiredService<IReferenceDataClient>();
                    if (client is ReferenceDataClient _client)
                    {
                        var baseUri = new Uri(Request.GetEncodedUrl());
                        _client.BaseUri = $"{baseUri.Scheme}://{baseUri.Host}:{baseUri.Port}/api/reference";
                        var authHeader = Request.Headers.Authorization.FirstOrDefault();
                        var token = authHeader?.Split(' ')?[1];
                        _client.BearerToken = token;
                        await _client.AddDefaultReferenceDataValues();
                        return NoContent();
                    }
                    throw new Exception("Invalid bootstrap client.");
                }
                catch (Exception ex)
                {
                    return Problem($"Error making internal request: {ex.Message}", null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <returns></returns>
        [HttpDelete]
        [EthosAuthScope(ScopeDefinitions.PlatformAdministrator)]
        [Route("sets/{setId}")]
        public async Task<IActionResult> DeleteSet([FromRoute] long setId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(DeleteSet), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var delSet = dbContext.Sets.FirstOrDefault(s => s.Id == setId);

                if (delSet is null)
                {
                    logger.LogInformation("No such set with ID {SetId}", setId);
                    return Problem($"No such set: {setId}", null, 404);
                }

                await dbContext.Sets.Where(s => s.Id == setId).ExecuteDeleteAsync();
                return NoContent();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        [HttpDelete]
        [EthosAuthScope(ScopeDefinitions.PlatformAdministrator)]
        [Route("sets")]
        public async Task<IActionResult> DeleteSet([FromQuery] string setName, string? version)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(DeleteSet), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                version ??= string.Empty;
                var delSet = dbContext.Sets.FirstOrDefault(s => string.Equals(s.Name.ToLower(), setName.ToLower()) && string.Equals(version.ToLower(), s.Version.ToLower()));

                if (delSet is null)
                {
                    logger.LogInformation("No such set with name '{SetName}' and version '{Version}'", setName, version);
                    return Problem($"No such set with name '{setName}' and version '{version}'", null, 404);
                }
                return await DeleteSet(delSet.Id);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetWrite, ScopeDefinitions.RefDataSetValueWrite)]
        [Route("jobs/{jobId}")]
        public IActionResult GetImportJob([FromRoute] Guid jobId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetImportJob), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var getSet = dbContext.ImportJobs
                            .Include(s => s.Set)
                                .ThenInclude(s => s.SetKeyValues)
                            .FirstOrDefault(v => v.JobId == jobId); 

                if (getSet is null)
                {
                    logger.LogInformation("No such import job with ID {JobId}", jobId);
                    return Problem($"No such import job: {jobId}", null, 404);
                }

                var summary = new ReferenceDataImportJobDto()
                {
                    JobId = getSet.JobId,
                    StartTime = getSet.StartTime,
                    EndTime = getSet.EndTime,
                    IsComplete = getSet.EndTime.HasValue,
                    Error = getSet.Error,
                    Set = getSet.Set is not null ? new ReferenceDataSetSummaryDto()
                    {
                        SetId = getSet.Set.Id,
                        Authority = getSet.Set.Authority,
                        Source = getSet.Set.Source,
                        Version = getSet.Set.Version,
                        Name = getSet.Set.Name,
                        Count = getSet.Set.SetKeyValues.Count,
                    } : null,
                };

                return Ok(summary);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetRead)]
        [Route("sets/{setId}")]
        public IActionResult GetReferenceDataSet([FromRoute] long setId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSet), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {

                var getSet = dbContext.Sets
                            .Include(s => s.KeyList)
                            .Include(s => s.SetKeyValues)
                            .FirstOrDefault(v => v.Id == setId);

                if (getSet is null)
                {
                    logger.LogInformation("Did not find reference data set with ID {SetId}", setId);
                    return Problem($"No such reference data set with ID: {setId}", null, 404);
                }

                logger.LogTrace("Found data set with ID {SetId}", getSet.Id);
                var summary = new ReferenceDataSetSummaryDto()
                {
                    SetId = getSet.Id,
                    Name = getSet.Name,
                    Version = getSet.Version,
                    Authority = getSet.Authority,
                    Source = getSet.Source,
                    Count = getSet.SetKeyValues.Count,
                };

                return Ok(summary);
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetRead)]
        [Route("listTypes")]
        public ActionResult<PagedResponse<ReferenceDataListTypeDto>> GetReferenceDataListTypes([FromQuery] PagingParameters pagingParameters)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataListTypes), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var listTypes = new List<ReferenceDataListTypeDto>();

                listTypes.AddRange([
                    new ReferenceDataListTypeDto(ReferenceDataListType.String),
                    new ReferenceDataListTypeDto(ReferenceDataListType.Boolean),
                    new ReferenceDataListTypeDto(ReferenceDataListType.DateTime),
                    new ReferenceDataListTypeDto(ReferenceDataListType.Integer),
                    new ReferenceDataListTypeDto(ReferenceDataListType.Float)
                    ]);

                return Ok(listTypes.AsQueryable().PaginateWithLinks(this, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetRead)]
        [Route("sets")]
        public async Task<ActionResult<PagedResponse<ReferenceDataSetSummaryDto>>> GetReferenceDataSets([FromQuery] PagingParameters pagingParameters, [FromQuery] string? setName, [FromQuery] string? version)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSets), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (!string.IsNullOrEmpty(version) && string.IsNullOrEmpty(setName))
                    return Problem("Set name is required when searching by version.", null, 400);

                var setsQuery = dbContext.Sets
                                         .Include(s => s.KeyList)
                                         .Include(s => s.SetKeyValues)
                                         .Where(s => (string.IsNullOrEmpty(setName) && string.IsNullOrEmpty(version)) ||
                                                     (!string.IsNullOrEmpty(setName) && s.Name.ToLower().Contains(setName.ToLower()) &&
                                                     ((!string.IsNullOrEmpty(version) && s.Version.ToLower().Contains(version.ToLower())) ||
                                                     string.IsNullOrEmpty(version))));

                return Ok(await setsQuery.PaginateWithLinksAsync(this, (sets) =>
                {
                    List<ReferenceDataSetSummaryDto> data = [];

                    foreach (var set in sets)
                    {
                        data.Add(new ReferenceDataSetSummaryDto()
                        {
                            SetId = set.Id,
                            Name = set.Name,
                            Version = set.Version,
                            Authority = set.Authority,
                            Source = set.Source,
                            Count = set.SetKeyValues.Count,
                        });
                    }
                    return data;
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetRead)]
        [Route("lists/{listId}")]
        public IActionResult GetReferenceDataList([FromRoute] long listId)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataList), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var list = dbContext.Lists
                                    .Include(l => l.Values)
                                    .Include(l => l.Sets)
                                    .FirstOrDefault(l => l.Id == listId);

                if (list is null)
                {
                    logger.LogInformation("Did not find reference data list with ID {ListId}", listId);
                    return Problem($"No such reference data list with ID: {listId}", null, 404);
                }

                logger.LogTrace("Found list with ID {ListId}", list.Id);
                var summary = new ReferenceDataListSummaryDto()
                {
                    ListId = list.Id,
                    Name = list.Name,
                    Type = new ReferenceDataListTypeDto(list.Type),
                    ValueCount = list.Values.Count,
                    SetCount = list.Sets.Count,
                };

                return Ok(summary);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetWrite)]
        [Route("lists")]
        public IActionResult CreateReferenceDataList([FromBody] ReferenceDataListDto list)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateReferenceDataList), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (list is null || string.IsNullOrEmpty(list?.Name?.Trim()))
                {
                    logger.LogError("Attempted to create a reference data list without a name.");
                    return Problem("List name is required.", null, 400);
                }
                else
                    list.Name = NormalizeName(list.Name);

                if (!Enum.TryParse<ReferenceDataListType>(list.Type, true, out var listType))
                {
                    logger.LogError("Invalid list type: {ListType}", list.Type);
                    return Problem($"Invalid list type: {list.Type}", null, 400);
                }

                var existingList = dbContext.GetList(list.Name, listType);

                if (existingList is not null)
                {
                    logger.LogInformation("Attempted to create a new list with name '{ListName}' but a list with that name and type already exists.", list.Name);
                    return Problem($"List already exists: {existingList.Name}", null, 409);
                }

                var newList = new ReferenceDataList()
                {
                    Name = list.Name,
                    Type = listType,
                };

                dbContext.Lists.Add(newList);
                dbContext.SaveChanges(true);

                return Created(Url.Action(nameof(GetReferenceDataList), new { listId = newList.Id }), new ReferenceDataListSummaryDto()
                {
                    Name = newList.Name,
                    Type = new ReferenceDataListTypeDto(newList.Type),
                    ListId = newList.Id,
                });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="set"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetWrite)]
        [Route("sets")]
        public IActionResult CreateReferenceDataSet([FromBody] ReferenceDataSetDto set)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(CreateReferenceDataSet), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (set is null || string.IsNullOrEmpty(set?.Name?.Trim()))
                {
                    logger.LogError("Attempted to create a new reference data set without a name.");
                    return Problem("Set name is required.", null, 400);
                }
                else
                    set.Name = NormalizeName(set.Name);

                if (string.IsNullOrEmpty(set.Key?.Trim()))
                {
                    logger.LogError("Attempted to create a new reference data set without a key name.");
                    return Problem("Set key name is required.", null, 400);
                }
                else
                    set.Key = NormalizeName(set.Key);

                if (!Enum.TryParse<ReferenceDataListType>(set.KeyType, true, out var listType))
                {
                    logger.LogError("Invalid key type: {ListType}", set.KeyType);
                    return Problem($"Invalid key type: {set.KeyType}", null, 400);
                }

                set.Version ??= string.Empty;

                long keyListId = 0;

                if (dbContext.SetExists(set.Name, set.Version, set.Key, set.Source, set.Authority, Guid.Empty))
                {
                    logger.LogInformation("Set with name '{SetName}' and version {SetVersion} already exists.", set.Name, set.Version);
                    return Problem($"Set with name '{set.Name}' and version {set.Version} already exists.", null, 409);
                }

                var keyList = dbContext.GetList(set.Key, listType);

                if (keyList is not null)
                {
                    logger.LogDebug("Found existing list with name '{ListName}' as key with type '{ListType}' for new set with name '{SetName}'.", keyList.Name, keyList.Type, set.Name);

                    if (keyList.Type != listType)
                    {
                        logger.LogInformation("List '{ListName}' has type '{ListType}' but type '{KeyListType}' was specified.", keyList.Name, keyList.Type, listType);
                        return Problem($"List '{keyList.Name}' has type '{keyList.Type}' and cannot be used with the specified type: {listType}", null, 400);
                    }
                    keyListId = keyList.Id;
                }

                if (keyListId == 0)
                {
                    var newList = new ReferenceDataList()
                    {
                        Name = set.Key,
                        Type = listType,
                    };

                    dbContext.Lists.Add(newList);
                    dbContext.SaveChanges();
                    keyListId = newList.Id;
                }

                var newSet = new ReferenceDataSet()
                {
                    KeyListId = keyListId,
                    Authority = set.Authority,
                    Source = set.Source,
                    Name = set.Name,
                    TenantId = Guid.Empty,
                    Version = set.Version,
                };

                dbContext.Sets.Add(newSet);
                dbContext.SaveChanges();

                return Created(Url.Action(nameof(GetReferenceDataSet), new { setId = newSet.Id }), new ReferenceDataSetSummaryDto()
                {
                    Name = newSet.Name,
                    SetId = newSet.Id,
                    Authority = newSet.Authority,
                    Source = newSet.Source,
                    Version = newSet.Version,
                });
            }
        }

        [HttpPost]
        [HttpPut]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueWrite)]
        [Route("lists/{listId}/values")]
        public IActionResult AddValueToList([FromRoute] long listId, [FromBody] ReferenceDataListValueDto data)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataListValues), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var list = dbContext.Lists.FirstOrDefault(l => l.Id == listId);

                if (list is null)
                {
                    logger.LogInformation("Did not find reference data list with ID {ListId}", listId);
                    return Problem($"No such reference data list with ID: {listId}", null, 404);
                }

                var convertedVal = ReferenceDataListValue.ConvertValue(list.Type, data.Value);

                var listVal = dbContext.GetListValue(list.Name, convertedVal);

                if (listVal is not null)
                    return Ok(new ReferenceDataListValueDto() { Value = listVal.Value, ValueId = listVal.Id });

                var newListVal = dbContext.GetOrCreateListValue(list.Name, convertedVal, list.Type);

                return Created((string?)null, new ReferenceDataListValueDto() { Value = newListVal.Value, ValueId = newListVal.Id });
            }
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="ReferenceDataException"></exception>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueWrite)]
        [Route("sets/{setId}/values")]
        public IActionResult AddValueToSet([FromRoute] long setId, [FromBody] ReferenceDataValueSetDto data)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(AddValueToSet), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                try
                {
                    var setKeyValue = AddValueToSet(setId, data, CreationContext.Update);
                    return Created(Url.Action(nameof(GetReferenceDataSetValue), new { setId, valueId = setKeyValue.Id }), ReferenceDataResultSet.GetSetKeyValueDto(setKeyValue));
                }
                catch (ReferenceDataException rex)
                {
                    return Problem(rex.Message, null, rex.StatusCode);
                }
                catch (Exception ex)
                {
                    return Problem(ex.Message, null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="data"></param>
        /// <param name="creationContext"></param>
        /// <returns></returns>
        /// <exception cref="ReferenceDataException"></exception>
        ReferenceDataSetKeyValue AddValueToSet(long setId, ReferenceDataValueSetDto data, CreationContext creationContext = CreationContext.Update, long? keyTriggerId = null)
        {
            var set = dbContext.Sets.Include(s => s.KeyList).FirstOrDefault(s => s.Id == setId);

            if (set is null)
            {
                logger.LogError("Did not find reference data set with ID {SetId} while adding new set value.", setId);
                throw new ReferenceDataException(400, $"No such data set with ID: {setId}");
            }

            var keyName = set.KeyList.Name;
            var keyValueType = set.KeyList.Type;

            var keyDataItem = data.FirstOrDefault(d => string.Equals(keyName, d.Name, StringComparison.OrdinalIgnoreCase));

            var keyValueValue = keyDataItem?.Value;
            var convertedKeyValueValue = ReferenceDataListValue.ConvertValue(keyValueType, keyValueValue);

            if (keyValueValue is null || string.IsNullOrEmpty(keyValueValue?.ToString()))
            {
                logger.LogWarning("Missing value for key '{KeyName}' in set '{SetName}'", keyName, set.Name);
                throw new ReferenceDataException(400, $"Missing value for key '{keyName}' in set '{set.Name}'");
            }

            long finalSetValueId = 0;

            using var transaction = dbContext.Database.BeginTransaction();
            try
            {
                foreach (var v in data)
                {
                    long mappedKeySetValueId = 0;

                    // are we mapping from one set value to another set key value?
                    if (v.InSet is not null)
                    {
                        ReferenceDataSet? mappedSet = null;
                        if (v.InSet.SetId > 0)
                        {
                            mappedSet = dbContext.Sets.Include(s => s.SetKeyValues).FirstOrDefault(s => s.Id == v.InSet.SetId);

                            if (mappedSet is null)
                            {
                                logger.LogError("Reference data set with ID {SetId} does not exist while mapping set value '{ValueName}' to external key value.", v.InSet.SetId, v.Name);
                                throw new ReferenceDataException(404, $"Data set with ID {v.InSet.SetId} was not found while mapping '{v.Name}' with value '{v.Value}'.");
                            }
                        }
                        else if (!string.IsNullOrEmpty(v.InSet.Name) && !string.IsNullOrEmpty(v.InSet.Version))
                        {
                            mappedSet = dbContext.Sets.Include(s => s.SetKeyValues).FirstOrDefault(s => string.Equals(s.Name.ToLower(), v.InSet.Name) &&
                                                                                                        string.Equals(s.Version.ToLower(), v.InSet.Version));
                            if (mappedSet is null)
                            {
                                logger.LogError("Reference data set with name '{SetName}' and version '{SetVersion}' does not exist while mapping set value '{ValueName}' to external key value", v.InSet.Name, v.InSet.Version, v.Name);
                                throw new ReferenceDataException(404, $"Data set with name '{v.InSet.Name}' and version '{v.InSet.Version}' was not found while mapping '{v.Name}' with value '{v.Value}'.");
                            }
                        }
                        else
                        {
                            logger.LogError("Attempted to map set '{ListName}' with value '{ListValue}' to an external key but no target data set was provided.", v.Name, v.Value);
                            throw new ReferenceDataException(400, $"Attempted to map '{v.Name}' with value '{v.Value}' to an external key but no target data set was provided.");
                        }

                        // find the value first
                        var keyValueInSet = dbContext.GetListValue(v.Name, v.Value);

                        if (keyValueInSet is null)
                        {
                            logger.LogError("Value '{Value}' does not exist in list '{ListName}'", v.Value, v.Name);
                            throw new ReferenceDataException(404, $"Value '{v.Value}' does not exist in list '{v.Name}'");
                        }

                        // find the key specified
                        var mappedKey = mappedSet.SetKeyValues.FirstOrDefault(kv => kv.ValueId == keyValueInSet.Id);

                        if (mappedKey is null)
                        {
                            logger.LogError("Value '{Value}' does not exist as a key in set '{SetName}'.", v.Value, mappedSet.Name);
                            throw new ReferenceDataException(404, $"Value '{v.Value}' does not exist as a key in set '{mappedSet.Name}'");
                        }

                        mappedKeySetValueId = mappedKey.Id;
                    }

                    var listType = ReferenceDataList.GuessListType(v.Value);
                    var setValue = dbContext.GetOrCreateSetValue(set.Id, creationContext, keyName, convertedKeyValueValue, v.Name, v.Value, listType, v.Alias, Guid.Empty, keyTriggerId);

                    if (mappedKeySetValueId > 0)
                    {
                        setValue.MappedKeyValueId = mappedKeySetValueId;
                        dbContext.Update(setValue);
                        dbContext.SaveChanges();
                    }

                    if (finalSetValueId == 0)
                        finalSetValueId = setValue?.KeyValue?.Id ?? 0;
                }

                transaction.Commit();

                if (finalSetValueId > 0)
                {
                    var finalVal = dbContext.SetKeyValues.Include(k => k.Value)
                                                        .ThenInclude(v => v.List)
                                                     .Include(k => k.SetValues)
                                                        .ThenInclude(sv => sv.Value)
                                                        .ThenInclude(v => v.List)
                                                    .Include(k => k.SetValues)
                                                        .ThenInclude(k => k.MappedKeyValue)
                                                        .ThenInclude(mkv => mkv.SetValues)
                                                        .ThenInclude(mkv => mkv.Value)
                                                        .ThenInclude(mkv => mkv.List)
                                                    .FirstOrDefault(kv => kv.SetId == setId && kv.Id == finalSetValueId);
                    if (finalVal is not null)
                        return finalVal;
                }
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
            throw new ReferenceDataException(500, "Unknown error while creating set value.");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueWrite)]
        [Route("sets/{setId}/alternates")]
        public IActionResult AddAlternateValueToSet([FromRoute] long setId, [FromBody] ReferenceDataSetAlternateDto data)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(AddAlternateValueToSet), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var set = dbContext.Sets.Include(s => s.AlternateKeyValues)
                                            .ThenInclude(a => a.KeyValue)
                                            .ThenInclude(kv => kv.SetValues)
                                            .ThenInclude(a => a.Value)
                                            .ThenInclude(l => l.List)
                                        .Include(s => s.AlternateKeyValues)
                                            .ThenInclude(a => a.KeyValue)
                                            .ThenInclude(k => k.Value)
                                        .Include(s => s.KeyList)
                                   .FirstOrDefault(s => s.Id == setId);

                if (set is null)
                {
                    logger.LogError("Did not find reference data set with ID {SetId}.", setId);
                    return Problem($"No such reference data set with ID: {setId}", null, 404);
                }

                ReferenceDataSet? alternateSet = null;

                // find the set and key value specified as the alternate
                if (data.SetId.HasValue && data.SetId.Value > 0)
                {
                    alternateSet = dbContext.Sets.Include(s => s.SetKeyValues)
                                                        .ThenInclude(k => k.Value)
                                                        .ThenInclude(v => v.List)
                                                     .Include(k => k.SetValues)
                                                        .ThenInclude(sv => sv.Value)
                                                        .ThenInclude(v => v.List)
                                                    .Include(k => k.SetValues)
                                                        .ThenInclude(k => k.MappedKeyValue)
                                                        .ThenInclude(mkv => mkv.SetValues)
                                                        .ThenInclude(mkv => mkv.Value)
                                                        .ThenInclude(mkv => mkv.List)
                                                      .FirstOrDefault(s => s.Id == data.SetId.Value);
                }

                data.Version ??= string.Empty;

                if (alternateSet is null && !string.IsNullOrEmpty(data.Name))
                {
                    alternateSet = dbContext.Sets.Include(s => s.SetKeyValues)
                                                        .ThenInclude(k => k.Value)
                                                        .ThenInclude(v => v.List)
                                                     .Include(k => k.SetValues)
                                                        .ThenInclude(sv => sv.Value)
                                                        .ThenInclude(v => v.List)
                                                    .Include(k => k.SetValues)
                                                        .ThenInclude(k => k.MappedKeyValue)
                                                        .ThenInclude(mkv => mkv.SetValues)
                                                        .ThenInclude(mkv => mkv.Value)
                                                        .ThenInclude(mkv => mkv.List)
                                                    .Include(s => s.KeyList)
                                                    .FirstOrDefault(s => string.Equals(s.Name.ToLower(), data.Name.ToLower()) &&
                                                                         string.Equals((s.Version ?? string.Empty).ToLower(), data.Version.ToLower()));
                }

                if (alternateSet is null)
                {
                    if (data.SetId.HasValue && data.SetId.Value > 0)
                    {
                        logger.LogError("Did not find alternate reference data set with ID {SetId}.", data.SetId);
                        return Problem($"No such reference data set with ID: {data.SetId}", null, 404);
                    }
                    else if (!string.IsNullOrEmpty(data.Name))
                    {
                        logger.LogError("Did not find alternate reference data set with name {SetName} and version {SetVersion}.", data.Name, data.Version);
                        return Problem($"No such reference data set with name '{data.Name}' and version '{data.Version}'", null, 404);
                    }
                    else
                    {
                        logger.LogError("Missing alternate reference data set details while adding new alternate key.");
                        return Problem($"Alternate data set detail is required to add an alternate key to set '{set.Name}' with version '{set.Version}'", null, 400);
                    }
                }

                var convertedKeyVal = ReferenceDataListValue.ConvertValue(alternateSet.KeyList.Type, data.Value);

                var alternateSetKeyValue = alternateSet.SetKeyValues.FirstOrDefault(kv => Equals(kv?.Value?.Value, convertedKeyVal));

                if (alternateSetKeyValue is null)
                {
                    logger.LogError("Value '{Value}' is not a key in alternate data set '{AlternateSetName}' with version '{AlternateSetVersion}'", data.Value, alternateSet.Name, alternateSet.Version);
                    return Problem($"Value '{data.Value}' is not a key in alternate data set '{alternateSet.Name}' with version '{alternateSet.Version}'", null, 404);
                }

                // does this alternate already exist in the current set?
                var existingAlt = set.AlternateKeyValues.FirstOrDefault(kv => kv.KeyValueId == alternateSetKeyValue.Id);

                if (existingAlt is not null)
                {
                    return Problem($"Value '{data.Value}' from set '{alternateSet.Name}' is already an alternate key value in set '{set.Name}'", null, 409);
                }

                var newAlterateValue = new ReferenceDataSetKeyValueAlternate()
                {
                    KeyValueId = alternateSetKeyValue.Id,
                    TenantId = Guid.Empty,
                    SetId = set.Id,
                    SchemaBehavior = (data?.Behavior?.OnNewValueRequireKeyValueOnly ?? false) ? SchemaBehavior.KeyOnly : SchemaBehavior.All,
                    TriggerBehavior = (data?.Behavior?.OnSelectRequireNewValue ?? false) ? KeyTriggerBehavior.Require :
                                      (data?.Behavior?.OnSelectAllowNewValue ?? false) ? KeyTriggerBehavior.Allow :
                                      KeyTriggerBehavior.None,
                };

                dbContext.KeyValueAlternates.Add(newAlterateValue);
                dbContext.SaveChanges();

                return CreatedAtAction(nameof(GetReferenceDataSetAlternateValues), new { setId = set.Id }, new ReferenceDataSetAlternateDto()
                {
                    Name = alternateSet.Name,
                    Version = alternateSet.Version,
                    SetId = alternateSet.Id,
                    Value = convertedKeyVal,
                    Behavior = new ReferenceDataSetAlternateBehaviorDto()
                    {
                        OnNewValueRequireAllFields = newAlterateValue.SchemaBehavior == SchemaBehavior.All,
                        OnNewValueRequireKeyValueOnly = newAlterateValue.SchemaBehavior == SchemaBehavior.KeyOnly,
                        OnSelectAllowNewValue = newAlterateValue.TriggerBehavior != KeyTriggerBehavior.None,
                        OnSelectRequireNewValue = newAlterateValue.TriggerBehavior == KeyTriggerBehavior.Require,
                    },
                    Data = ReferenceDataResultSet.GetSetKeyValueDto(alternateSetKeyValue),
                    CreationSchema = dbContext.GetKeySetValueFields(set, newAlterateValue.SchemaBehavior)
                });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="alternate"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueWrite)]
        [Route("sets/{setId}/alternates/process")]
        public IActionResult ProcessAlternateKeyTriggerBehavior([FromRoute] long setId, [FromBody] ReferenceDataSetAlternateProcessDto alternate)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(ProcessAlternateKeyTriggerBehavior), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var set = dbContext.Sets.Include(s => s.AlternateKeyValues)
                                            .ThenInclude(a => a.KeyValue)
                                            .ThenInclude(kv => kv.SetValues)
                                            .ThenInclude(a => a.Value)
                                            .ThenInclude(l => l.List)
                                        .Include(s => s.AlternateKeyValues)
                                            .ThenInclude(a => a.KeyValue)
                                            .ThenInclude(k => k.Value)
                                        .Include(s => s.AlternateKeyValues)
                                            .ThenInclude(s => s.KeyValue)
                                            .ThenInclude(s => s.Set)
                                        .Include(s => s.KeyList)
                                    .FirstOrDefault(s => s.Id == setId);

                if (set is null)
                {
                    logger.LogError("Did not find reference data set with ID {SetId}.", setId);
                    return Problem($"No such reference data set with ID: {setId}", null, 404);
                }

                alternate.TenantId ??= Guid.Empty;

                var alternateKey = set.AlternateKeyValues.OrderByDescending(ak => ak.TenantId)
                                                         .FirstOrDefault(ak => Equals(ak.KeyValue.Value.Value, alternate.SelectedValue) &&
                                                                                      ((ak.TenantId == alternate.TenantId && alternate.TenantId != Guid.Empty) || ak.TenantId == Guid.Empty));

                if (alternateKey is null)
                {
                    return Problem($"Value '{alternate.SelectedValue}' is not an alternate key for set '{set.Name}' with ID {setId}.", null, 404);
                }

                // if no special behavior is defined, exit
                if (alternateKey.TriggerBehavior == KeyTriggerBehavior.None)
                    return NoContent();

                if (alternate.Schema is null || alternate.Schema.Count == 0)
                {
                    if (alternateKey.TriggerBehavior == KeyTriggerBehavior.Require)
                        return Problem($"Alternate key '{alternate.SelectedValue}' requires data to create a new value in set '{set.Name}' but no data was provided.", null, 400);
                    else
                        return NoContent();
                }

                try
                {
                    var createdKeyValue = AddValueToSet(setId, alternate.Schema, CreationContext.Trigger, alternateKey.KeyValue.Id);
                    return Created(Url.Action(nameof(GetReferenceDataSetValue), new { setId, valueId = createdKeyValue.Id }), ReferenceDataResultSet.GetSetKeyValueDto(createdKeyValue));
                }
                catch (ReferenceDataException rex)
                {
                    return Problem(rex.Message, null, rex.StatusCode);
                }
                catch (Exception ex)
                {
                    return Problem(ex.Message, null, 500);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("sets/{setId}/alternates")]
        public async Task<IActionResult> GetReferenceDataSetAlternateValues([FromRoute] long setId, [FromQuery] PagingParameters pagingParameters)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSetAlternateValues), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var set = dbContext.Sets.Include(s => s.KeyList)
                                        .Include(s => s.SetValues)
                                            .ThenInclude(sv => sv.Value)
                                            .ThenInclude(sv => sv.List)
                                        .FirstOrDefault(s => s.Id == setId);

                if (set is null)
                {
                    logger.LogError("Did not find reference data set with ID {SetId}.", setId);
                    return Problem($"No such reference data set with ID: {setId}", null, 404);
                }

                var list = new List<ReferenceDataSetAlternateDto>();

                var alternateKeys = dbContext.KeyValueAlternates.Include(a => a.KeyValue)
                                                                    .ThenInclude(kv => kv.SetValues)
                                                                    .ThenInclude(a => a.Value)
                                                                    .ThenInclude(l => l.List)
                                                                .Include(a => a.KeyValue)
                                                                    .ThenInclude(k => k.Value)
                                                                .Include(s => s.KeyValue)
                                                                    .ThenInclude(s => s.Set)
                                             .Where(ak => ak.SetId == set.Id)
                                             .OrderBy(ak => ak.KeyValue.Set.Id)
                                             .OrderBy(lv => lv.KeyValueId);

                return Ok(await alternateKeys.PaginateWithLinksAsync(this, (keys) =>
                {
                    list.AddRange(keys.Select(ak => new ReferenceDataSetAlternateDto()
                    {
                        Name = ak.KeyValue.Set.Name,
                        Version = ak.KeyValue.Set.Version,
                        SetId = ak.KeyValue.Set.Id,
                        Value = ak.KeyValue.Value.Value,
                        Behavior = new ReferenceDataSetAlternateBehaviorDto()
                        {
                            OnNewValueRequireAllFields = ak.SchemaBehavior == SchemaBehavior.All,
                            OnNewValueRequireKeyValueOnly = ak.SchemaBehavior == SchemaBehavior.KeyOnly,
                            OnSelectAllowNewValue = ak.TriggerBehavior != KeyTriggerBehavior.None,
                            OnSelectRequireNewValue = ak.TriggerBehavior == KeyTriggerBehavior.Require,
                        },
                        Data = ReferenceDataResultSet.GetSetKeyValueDto(ak.KeyValue),
                        CreationSchema = dbContext.GetKeySetValueFields(set, ak.SchemaBehavior)
                    }));
                    return list;
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listName"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("lists/{listId}/values")]
        public async Task<IActionResult> GetReferenceDataListValues([FromRoute] long listId, [FromQuery] PagingParameters pagingParameters)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataListValues), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var list = dbContext.Lists.FirstOrDefault(l => l.Id == listId);

                if (list is null)
                {
                    logger.LogInformation("Did not find reference data list with ID {ListId}", listId);
                    return Problem($"No such reference data list with ID: {listId}", null, 404);
                }

                var listValueQuery = dbContext.ListValues
                                              .Where(lv => lv.ListId == list.Id)
                                              .OrderBy(lv => lv.Id);

                logger.LogTrace("Found list with ID {ListId}", list.Id);

                return Ok(await listValueQuery.PaginateWithLinksAsync(this, (listValues) =>
                {
                    List<object> data = [];
                    var listTypeStr = list.Type.ToString();

                    foreach (var listValue in listValues)
                        data.Add(new { valueId = listValue.Id, value = listValue.Value, type = listTypeStr });

                    return data;
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <param name="valueId"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("sets/{setId}/values/{valueId}")]
        public async Task<IActionResult> GetReferenceDataSetValue([FromRoute] long setId, [FromRoute] long valueId, [FromQuery] int? maxDepth = 3)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSetValue), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var parentSet = dbContext.Sets.FirstOrDefault(v => v.Id == setId);

                if (parentSet is null)
                {
                    logger.LogInformation("No reference data set found with ID {SetId}.", setId);
                    return Problem($"No such set exists with ID {setId}.", null, 404);
                }

                if (maxDepth > 5)
                    return Problem("Max recursion depth is 5.", null, 400);

                var setValues = await GetRecursiveSetKeyValues(k => k.SetId == parentSet.Id && k.Id == valueId, maxDepth ?? 3);

                var setValue = setValues.FirstOrDefault();

                if (setValue is not null)
                {
                    logger.LogTrace("Found set value key with ID {ValueId} in set with ID {SetId}.", valueId, parentSet.Id);
                    var valueOutput = ReferenceDataResultSet.GetSetKeyValueDto(setValue);
                    return Ok(valueOutput);
                }
                else
                {
                    logger.LogInformation("Did not find set value key with ID {ValueId} in set with ID {SetId}.", valueId, parentSet.Id);
                    return Problem($"No such key value in set with name '{parentSet.Name}': {valueId}", null, 404);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("sets/keys/{id}")]
        public async Task<IActionResult> GetReferenceDataSetKeyValue([FromRoute] long id, [FromQuery] int? maxDepth = 3)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSetKeyValue), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (maxDepth > 5)
                    return Problem("Max recursion depth is 5.", null, 400);

                var setValues = await GetRecursiveSetKeyValues(k => k.Id == id, maxDepth ?? 3);

                var setValue = setValues.FirstOrDefault();

                if (setValue is not null)
                {
                    var setTotalCount = dbContext.SetKeyValues.Count(sv => sv.SetId == setValue.Set.Id);

                    logger.LogTrace("Found set value key with ID {Id} in set with ID {SetId}.", id, setValue.Set.Id);
                    var valueOutput = ReferenceDataResultSet.GetSetKeyValueDto(setValue);
                    var result = new
                    {
                        set = new ReferenceDataSetSummaryDto()
                        {
                            Authority = setValue.Set.Authority,
                            Source = setValue.Set.Source,
                            Version = setValue.Set.Version,
                            Name = setValue.Set.Name,
                            SetId = setValue.Set.Id,
                            Count = setTotalCount
                        },
                        value = valueOutput
                    };
                    return Ok(result);
                }
                else
                {
                    logger.LogInformation("Did not find set value key with ID {Id}.", id);
                    return Problem($"No such key value with ID {id}", null, 404);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="maxDepth"></param>
        /// <returns></returns>
        async Task<IList<ReferenceDataSetKeyValue>> GetRecursiveSetKeyValues(Expression<Func<ReferenceDataSetKeyValue, bool>> predicate,
                                                                            int maxDepth = 3)
        {
            if (maxDepth > 5)
                throw new ArgumentException("Max recursion depth is 5.");

            var setValues = await dbContext.SetKeyValues.Include(k => k.Value)
                                                        .ThenInclude(v => v.List)
                                                     .Include(k => k.Set)
                                                     .Include(k => k.SetValues)
                                                        .ThenInclude(sv => sv.Value)
                                                        .ThenInclude(v => v.List)
                                                     .Where(predicate)
                                                     .OrderBy(k => k.Id)
                                                     .ToListAsync();

            if (maxDepth > 1)
            {
                foreach (var setKey in setValues)
                    await dbContext.RecursiveLoadMappedKeys(setKey, [], 2, maxDepth);
            }

            return setValues;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("sets/keys")]
        public async Task<IActionResult> GetReferenceDataSetKeyValues([FromQuery] long[] ids, [FromQuery] PagingParameters pagingParameters, [FromQuery] int? maxDepth = 3)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSetKeyValues), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (maxDepth > 5)
                    return Problem("Max recursion depth is 5.", null, 400);

                var setValues = await GetRecursiveSetKeyValues(k => ids.Contains(k.Id), maxDepth ?? 3);

                var setTotalCount = setValues.Count;

                if (setValues is not null && setValues.Count > 0)
                {
                    return Ok(setValues.AsQueryable().PaginateWithLinks(this, (values) =>
                    {
                        List<object> data = [];

                        foreach (var setValue in values)
                        {
                            var valueOutput = ReferenceDataResultSet.GetSetKeyValueDto(setValue);
                            var result = new
                            {
                                set = new ReferenceDataSetSummaryDto()
                                {
                                    Authority = setValue.Set.Authority,
                                    Source = setValue.Set.Source,
                                    Version = setValue.Set.Version,
                                    Name = setValue.Set.Name,
                                    SetId = setValue.Set.Id,
                                    Count = setTotalCount,
                                },
                                value = valueOutput
                            };
                            data.Add(result);
                        }
                        return data;

                    }, pagingParameters.limit, pagingParameters.offset));
                }
                else
                {
                    logger.LogInformation("Did not find set value key with ID {Id}.", ids);
                    return Problem($"No such key value with ID {ids}", null, 404);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        static string NormalizeName(string? name)
        {
            name = name?.Trim() ?? string.Empty;
            if (name.Contains(' '))
                name = name.Replace(' ', '-');
            return name;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("sets/values")]
        public async Task<IActionResult> GetReferenceDataSetValues([FromQuery] string setName, [FromQuery] string? version, [FromQuery] string? filter, [FromQuery] PagingParameters pagingParameters, [FromQuery] int? maxDepth = 3)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSetValues), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                var parentSet = await dbContext.Sets.Where(s => string.Equals(s.Name.ToLower(), setName.ToLower()) &&
                                                                string.Equals(s.Version.ToLower(), (version ?? string.Empty).ToLower()))
                                                    .ToListAsync();

                if (parentSet is null || parentSet.Count == 0)
                {
                    logger.LogInformation("No reference data set found with name {SetName} and version {SetVersion}.", setName, version);
                    return Problem($"No such set exists with name '{setName}' and version '{version}'.", null, 404);
                }

                if (parentSet.Count > 1)
                {
                    logger.LogInformation("More than one reference data set found with name {SetName} and version {SetVersion}.", setName, version);
                    return Problem($"More than one reference data set found with name '{setName}' and version '{version}'.", null, 409);
                }

                return await GetReferenceDataSetValues(parentSet[0].Id, filter, pagingParameters, maxDepth);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("validate")]
        public IActionResult ValidateReferenceData([FromBody] ReferenceDataValidationDto payload)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(ValidateReferenceData), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (payload.Count == 0)
                    return NoContent();

                var validationErrors = new Dictionary<string, string[]>();
                var setCache = new List<ReferenceDataSet>();

                foreach (var item in payload)
                {
                    var setId = 0L;
                    string setName, version;

                    if (item.SetId.HasValue && item.SetId.Value > 0)
                    {
                        var set = setCache.FirstOrDefault(sc => sc.Id == item.SetId.Value) ?? dbContext.Sets.FirstOrDefault(s => s.Id == item.SetId.Value);
                        if (set is null)
                        {
                            validationErrors.Add(payload.IndexOf(item).ToString(), [$"Set with ID {item.SetId.Value} was not found."]);
                            continue;
                        }

                        if (!setCache.Any(sc => sc.Id == item.SetId.Value))
                            setCache.Add(set);

                        setId = item.SetId.Value;
                        setName = set.Name;
                        version = set.Version;
                    }
                    else if (!string.IsNullOrEmpty(item.SetName))
                    {
                        item.Version ??= string.Empty;
                        var set = setCache.FirstOrDefault(sc => string.Equals(sc.Name.ToLower(), item.SetName.ToLower()) &&
                                                                string.Equals(sc.Version.ToLower(), item.Version.ToLower()))
                                  ?? dbContext.Sets.FirstOrDefault(sc => string.Equals(sc.Name.ToLower(), item.SetName.ToLower()) &&
                                                                         string.Equals(sc.Version.ToLower(), item.Version.ToLower()));
                        if (set is null)
                        {
                            validationErrors.Add(payload.IndexOf(item).ToString(), [$"Set with name '{item.SetName}' and version '{item.Version}' was not found."]);
                            continue;
                        }

                        if (!setCache.Any(sc => sc.Id == set.Id))
                            setCache.Add(set);

                        setId = set.Id;
                        setName = set.Name;
                        version = set.Version;
                    }
                    else
                    {
                        validationErrors.Add(payload.IndexOf(item).ToString(), ["Set name or ID is required to validate reference data"]);
                        continue;
                    }

                    if (item.Value is null)
                    {
                        validationErrors.Add(payload.IndexOf(item).ToString(), ["No value provided for validation"]);
                        continue;
                    }

                    switch (item.ValidationType)
                    {
                        case ReferenceDataValidationType.Key:
                            // validate the value of the key
                            if (!dbContext.IsKeyInSet(setId, item.Value))
                            {
                                validationErrors.Add(payload.IndexOf(item).ToString(), [$"Key value '{item.Value}' does not exist in set '{setName}' with version '{version}'."]);
                                continue;
                            }
                            break;

                        case ReferenceDataValidationType.Id:
                            var keyId = 0L;

                            // validate the ID of the item in the set to ensure it belongs to this set
                            if (typeof(long).IsAssignableFrom(item.Value.GetType()))
                                keyId = Convert.ToInt64(item.Value);

                            if (keyId < 1)
                            {
                                validationErrors.Add(payload.IndexOf(item).ToString(), [$"ID '{keyId}' is invalid"]);
                                continue;
                            }

                            var refItem = dbContext.SetKeyValues.FirstOrDefault(sc => sc.SetId == setId && sc.Id == keyId);

                            if (refItem is null)
                            {
                                validationErrors.Add(payload.IndexOf(item).ToString(), [$"ID '{item.Value}' does not exist in set '{setName}' with version '{version}'."]);
                                continue;
                            }
                            break;

                        case ReferenceDataValidationType.Value:

                            if (string.IsNullOrEmpty(item.PropertyName))
                            {
                                validationErrors.Add(payload.IndexOf(item).ToString(), [$"Value '{item.Value}' cannot be validated because no property name was specified"]);
                                continue;
                            }

                            var listType = ReferenceDataList.GuessListType(item.Value);

                            var listIds = dbContext.GetListsByNameOrAlias(item.PropertyName, listType).Select(l => l.Id);

                            // TODO: This needs work
                            var refValue = dbContext.SetValues.Include(sv => sv.Value)
                                                              .ThenInclude(v => v.List)
                                                              .FirstOrDefault(sv => sv.SetId == setId && listIds.Contains(sv.Value.ListId) &&
                                                               (
                                                                                          (listType == ReferenceDataListType.Float && sv.Value.FloatValue == (double?)item.Value) ||
                                                                                          (listType == ReferenceDataListType.Integer && sv.Value.IntegerValue == (long?)item.Value) ||
                                                                                          (listType == ReferenceDataListType.Boolean && sv.Value.BooleanValue == (bool?)item.Value) ||
                                                                                          (listType == ReferenceDataListType.DateTime && sv.Value.DateTimeValue == (DateTimeOffset?)item.Value) ||
                                                                                          (listType == ReferenceDataListType.String && sv.Value.StringValue == (string?)item.Value)
                                                               ));

                            if (refValue is null)
                            {
                                validationErrors.Add(payload.IndexOf(item).ToString(), [$"Property '{item.PropertyName}' with value '{item.Value}' does not exist in set '{setName}' with version '{version}'."]);
                                continue;
                            }
                            break;
                    }
                }

                if (validationErrors.Count > 0)
                    return ValidationProblem(new ValidationProblemDetails(validationErrors));

                return NoContent();      
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setName"></param>
        /// <param name="version"></param>
        /// <param name="valueId"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead)]
        [Route("sets/{setId}/values")]
        public async Task<IActionResult> GetReferenceDataSetValues([FromRoute] long setId, [FromQuery] string? filter, [FromQuery] PagingParameters pagingParameters, [FromQuery] int? maxDepth = 3)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(GetReferenceDataSetValues), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (maxDepth > 5)
                    return Problem("Max recursion depth is 5.", null, 400);

                var parentSet = dbContext.Sets.FirstOrDefault(v => v.Id == setId);

                if (parentSet is null)
                {
                    logger.LogInformation("No reference data set found with ID {SetId}.", setId);
                    return Problem($"No such set exists with ID {setId}.", null, 404);
                }

                var setValues = await dbContext.SetKeyValues
                                                     .Include(k => k.SetValues)
                                                        .ThenInclude(sv => sv.Value)
                                                        .ThenInclude(v => v.List)
                                                     .Where(sv => sv.SetId == parentSet.Id)
                                                     .ParseAndFilter(filter)
                                                     .OrderBy(k => k.Id)
                                                     .ToListAsync();

                //var setValues = await GetRecursiveSetKeyValues(kv => kv.SetId == parentSet.Id, maxDepth ?? 3);

                return Ok(await setValues.AsQueryable().PaginateWithLinks<ReferenceDataSetKeyValue, ReferenceDataSetKeyValueDto>(this, async (values) =>
                {
                    List<ReferenceDataSetKeyValueDto> data = [];

                    if (maxDepth > 0)
                    {
                        var _values = await GetRecursiveSetKeyValues(kv => values.Select(v => v.Id).Contains(kv.Id), maxDepth ?? 3);
                        foreach (var setValue in _values)
                        {
                            var valueOutput = ReferenceDataResultSet.GetSetKeyValueDto(setValue);
                            data.Add(valueOutput);
                        }
                    }
                    else
                    {
                        foreach (var setValue in values)
                        {
                            var valueOutput = ReferenceDataResultSet.GetSetKeyValueDto(setValue);
                            data.Add(valueOutput);
                        }
                    }
                    return data;
                }, pagingParameters.limit, pagingParameters.offset));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listName"></param>
        /// <param name="value"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead, ScopeDefinitions.RefDataSetRead)]
        [Route("search/keys")]
        public async Task<IActionResult> SearchKeys([FromQuery] string? listName, [FromQuery] string? value, [FromQuery] string type, [FromQuery] PagingParameters pagingParameters, [FromQuery] int? maxDepth = 5)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(SearchKeys), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                logger.LogTrace("Entering method");

                if (maxDepth > 5)
                    return Problem("Max recursion depth is 5.", null, 400);

                if (string.IsNullOrEmpty(listName) && string.IsNullOrEmpty(value))
                    return ValidationProblem($"{nameof(listName)} and/or {nameof(value)} is required.", null, 422);

                ReferenceDataListType listType = ReferenceDataListType.String;

                if (!string.IsNullOrEmpty(type))
                {
                    if (!Enum.TryParse(type, true, out listType))
                    {
                        logger.LogInformation("Invalid list type: {ListType}", type);
                        return Problem($"Invalid list type: {type}", null, 400);
                    }
                }

                try
                {
                    List<long> listIds = [];

                    IQueryable<ReferenceDataList>? list = null;

                    if (!string.IsNullOrEmpty(listName))
                    {
                        list = dbContext.Lists.Where(l => string.Equals(l.Name.ToLower(), listName.ToLower()));

                        if (list is null || !list.Any())
                        {
                            logger.LogInformation("Did not find any list(s) with name {ListName}.", listName);
                            return Problem($"No such list: {listName}", null, 404);
                        }
                    }

                    object? convertedVal = null;

                    if (!string.IsNullOrEmpty(value))
                        convertedVal = ReferenceDataListValue.ConvertValue(listType, value);

                    list = list ?? dbContext.Lists.Where(l => l.Type == listType);

                    listIds.AddRange(list.Select(l => l.Id));
                    value ??= string.Empty;

                    IQueryable<ReferenceDataSetKeyValue> matches = dbContext.SetKeyValues.Include(sv => sv.Value)
                                                                                            .ThenInclude(sv => sv.List)
                                                                                         .Include(v => v.SetValues)
                                                                                            .ThenInclude(sv => sv.Value)
                                                                                            .ThenInclude(sv => sv.List)
                                                                                         //.Include(v => v.SetValues)
                                                                                         //   .ThenInclude(k => k.MappedKeyValue)
                                                                                         //   .ThenInclude(mkv => mkv.SetValues)
                                                                                         //   .ThenInclude(mkv => mkv.Value)
                                                                                         //   .ThenInclude(mkv => mkv.List)
                                                                                         .Include(sv => sv.Set);

                    if (listIds.Count == 0 && convertedVal is null)
                        return Ok(new PagedResponse<object>([], pagingParameters.limit, pagingParameters.offset, 0));

                    switch (listType)
                    {
                        case ReferenceDataListType.String:
                            if (listIds.Count > 0)
                                matches = matches.Where(v => listIds.Contains(v.Value.ListId) &&
                                                             (!string.IsNullOrEmpty(value) && string.Equals(v.Value.StringValue.ToLower(), value.ToLower()) ||
                                                             string.IsNullOrEmpty(value)));
                            else
                                matches = matches.Where(v => v.Value.List.Type == listType &&
                                                             !string.IsNullOrEmpty(value) &&
                                                             string.Equals(v.Value.StringValue.ToLower(), value.ToLower()));
                            break;

                        case ReferenceDataListType.Float:
                            double? dblSearchVal = convertedVal is double dblVal ? dblVal : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => listIds.Contains(v.Value.ListId) &&
                                                             (dblSearchVal != null && Equals(v.Value.FloatValue, dblSearchVal) ||
                                                             dblSearchVal == null));
                            else
                                matches = matches.Where(v => v.Value.List.Type == listType &&
                                                             dblSearchVal != null && Equals(v.Value.FloatValue, dblSearchVal));
                            break;

                        case ReferenceDataListType.Integer:
                            long? longSearchVal = convertedVal is long longVal ? longVal : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => listIds.Contains(v.Value.ListId) &&
                                                             (longSearchVal != null && Equals(v.Value.IntegerValue, longSearchVal) ||
                                                             longSearchVal == null));
                            else
                                matches = matches.Where(v => v.Value.List.Type == listType &&
                                                             longSearchVal != null && Equals(v.Value.IntegerValue, longSearchVal));
                            break;

                        case ReferenceDataListType.Boolean:
                            bool? boolSearchVal = convertedVal is bool boolVal ? boolVal : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => listIds.Contains(v.Value.ListId) &&
                                                             (boolSearchVal != null && Equals(v.Value.BooleanValue, boolSearchVal) ||
                                                             boolSearchVal == null));
                            else
                                matches = matches.Where(v => v.Value.List.Type == listType &&
                                                             boolSearchVal != null && Equals(v.Value.BooleanValue, boolSearchVal));
                            break;

                        case ReferenceDataListType.DateTime:
                            DateTimeOffset? dtoSearchVal = convertedVal is DateTimeOffset dto ? dto : convertedVal is DateTime dt ? new DateTimeOffset(dt) : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => listIds.Contains(v.Value.ListId) &&
                                                             (dtoSearchVal != null && Equals(v.Value.DateTimeValue, dtoSearchVal) ||
                                                             dtoSearchVal == null));
                            else
                                matches = matches.Where(v => v.Value.List.Type == listType &&
                                                             dtoSearchVal != null && Equals(v.Value.DateTimeValue, dtoSearchVal));
                            break;

                    }

                    matches = matches.OrderBy(m => m.SetId)
                                     .OrderBy(m => m.ValueId);

                    var results = await matches.ToListAsync();

                    if (maxDepth > 0)
                    {
                        foreach (var setKey in results)
                            await dbContext.RecursiveLoadMappedKeys(setKey, [], 1, maxDepth ?? 3);
                    }

                    return Ok(results.AsQueryable().PaginateWithLinks(this, (matches) =>
                    {
                        List<Dictionary<string, object>> mappings = [];

                        foreach (var set in matches.Select(i => i.Set).Distinct())
                        {
                            var keyValuesIncluded = matches.Where(i => i.SetId == set.Id);

                            if (!keyValuesIncluded.Any())
                                continue;

                            var thisListKey = keyValuesIncluded.Select(i => i.Value.List).FirstOrDefault();

                            if (thisListKey is null)
                                continue;

                            var thisVal = new Dictionary<string, object>
                            {
                                {
                                    "set",
                                    new
                                    {
                                        name = set.Name,
                                        setId = set.Id,
                                        version = set.Version,
                                        source = set.Source,
                                        authority = set.Authority,
                                        key = thisListKey.Name
                                    }
                                },
                            };

                            var resultVal = keyValuesIncluded.Select(ReferenceDataResultSet.GetSetKeyValueDto);
                            thisVal.Add("data", resultVal);

                            mappings.Add(thisVal);
                        }

                        return mappings;
                    }, pagingParameters.limit, pagingParameters.offset));
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "{ExceptionMessage}", ex.Message);
                    return Problem("Internal server error.", null, 500);
                }
                finally
                {
                    logger.LogTrace("Leaving method");
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listName"></param>
        /// <param name="value"></param>
        /// <param name="type"></param>
        /// <param name="pagingParameters"></param>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueRead, ScopeDefinitions.RefDataSetRead)]
        [Route("search/values")]
        public async Task<IActionResult> SearchValues([FromQuery] string? listName, [FromQuery] string? value, [FromQuery] string type, [FromQuery] PagingParameters pagingParameters, [FromQuery] int? maxDepth = 5)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(SearchValues), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                logger.LogTrace("Entering method");

                if (maxDepth > 5)
                    return Problem("Max recursion depth is 5.", null, 400);

                if (string.IsNullOrEmpty(listName) && string.IsNullOrEmpty(value))
                    return ValidationProblem($"{nameof(listName)} and/or {nameof(value)} is required.", null, 422);

                ReferenceDataListType listType = ReferenceDataListType.String;

                if (!string.IsNullOrEmpty(type))
                {
                    if (!Enum.TryParse(type, true, out listType))
                    {
                        logger.LogInformation("Invalid list type: {ListType}", type);
                        return Problem($"Invalid list type: {type}", null, 400);
                    }
                }

                try
                {
                    List<long> listIds = [];
                    IQueryable<ReferenceDataList>? list = null;

                    if (!string.IsNullOrEmpty(listName))
                    {
                        list = dbContext.Lists.Where(l => string.Equals(l.Name.ToLower(), listName.ToLower()));

                        if (list is null || !list.Any())
                        {
                            logger.LogInformation("Did not find any list(s) with name {ListName}.", listName);
                            return Problem($"No such list: {listName}", null, 404);
                        }

                        // find aliases and the list(s) associated with them
                        var listsWithAlias = dbContext.SetValues.Where(sv => string.Equals(sv.Alias.ToLower(), listName.ToLower()));
                    }

                    object? convertedVal = null;

                    if (!string.IsNullOrEmpty(value))
                        convertedVal = ReferenceDataListValue.ConvertValue(listType, value);

                    list = list ?? dbContext.Lists.Where(l => l.Type == listType);

                    listIds.AddRange(list.Select(l => l.Id));
                    value ??= string.Empty;

                    IQueryable<ReferenceDataSetKeyValue> matches = dbContext.SetKeyValues.Include(sv => sv.Value)
                                                                        .ThenInclude(sv => sv.List)
                                                                      .Include(v => v.SetValues)
                                                                        .ThenInclude(sv => sv.Value)
                                                                        .ThenInclude(sv => sv.List)
                                                                        .Include(v => v.SetValues)
                                                                        .ThenInclude(k => k.MappedKeyValue)
                                                        .ThenInclude(mkv => mkv.SetValues)
                                                        .ThenInclude(mkv => mkv.Value)
                                                        .ThenInclude(mkv => mkv.List)
                                                                      .Include(sv => sv.Set);

                    if (listIds.Count == 0 && convertedVal is null)
                        return Ok(new PagedResponse<object>([], pagingParameters.limit, pagingParameters.offset, 0));

                    switch (listType)
                    {
                        case ReferenceDataListType.String:
                            if (listIds.Count > 0)
                                matches = matches.Where(v => v.SetValues.Any(sv => listIds.Contains(sv.Value.ListId) &&
                                                                                  (!string.IsNullOrEmpty(value) && string.Equals(sv.Value.StringValue.ToLower(), value.ToLower()) ||
                                                                                  string.IsNullOrEmpty(value))));
                            else
                                matches = matches.Where(v => v.SetValues.Any(sv => sv.Value.List.Type == listType &&
                                                                                  !string.IsNullOrEmpty(value) &&
                                                                                  string.Equals(sv.Value.StringValue.ToLower(), value.ToLower())));
                            break;

                        case ReferenceDataListType.Float:
                            double? dblSearchVal = convertedVal is double dblVal ? dblVal : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => v.SetValues.Any(sv => listIds.Contains(sv.Value.ListId) &&
                                                                                  (dblSearchVal != null && Equals(sv.Value.FloatValue, dblSearchVal) ||
                                                                                  dblSearchVal == null)));
                            else
                                matches = matches.Where(v => v.SetValues.Any(sv => sv.Value.List.Type == listType &&
                                                                                  dblSearchVal != null && Equals(sv.Value.FloatValue, dblSearchVal)));
                            break;

                        case ReferenceDataListType.Integer:
                            long? longSearchVal = convertedVal is long longVal ? longVal : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => v.SetValues.Any(sv => listIds.Contains(sv.Value.ListId) &&
                                                                                  (longSearchVal != null && Equals(sv.Value.IntegerValue, longSearchVal) ||
                                                                                  longSearchVal == null)));
                            else
                                matches = matches.Where(v => v.SetValues.Any(sv => sv.Value.List.Type == listType &&
                                                                                  longSearchVal != null && Equals(sv.Value.IntegerValue, longSearchVal)));
                            break;

                        case ReferenceDataListType.Boolean:
                            bool? boolSearchVal = convertedVal is bool boolVal ? boolVal : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => v.SetValues.Any(sv => listIds.Contains(sv.Value.ListId) &&
                                                                                  (boolSearchVal != null && Equals(sv.Value.BooleanValue, boolSearchVal) ||
                                                                                  boolSearchVal == null)));
                            else
                                matches = matches.Where(v => v.SetValues.Any(sv => sv.Value.List.Type == listType &&
                                                                                  boolSearchVal != null && Equals(sv.Value.BooleanValue, boolSearchVal)));
                            break;

                        case ReferenceDataListType.DateTime:
                            DateTimeOffset? dtoSearchVal = convertedVal is DateTimeOffset dto ? dto : convertedVal is DateTime dt ? new DateTimeOffset(dt) : null;
                            if (listIds.Count > 0)
                                matches = matches.Where(v => v.SetValues.Any(sv => listIds.Contains(sv.Value.ListId) &&
                                                             (dtoSearchVal != null && Equals(sv.Value.DateTimeValue, dtoSearchVal) ||
                                                              dtoSearchVal == null)));
                            else
                                matches = matches.Where(v => v.SetValues.Any(sv => sv.Value.List.Type == listType &&
                                                             dtoSearchVal != null && Equals(sv.Value.DateTimeValue, dtoSearchVal)));
                            break;

                    }

                    matches = matches.OrderBy(m => m.SetId)
                                     .OrderBy(m => m.ValueId);

                    var results = await matches.ToListAsync();

                    if (maxDepth > 0)
                    {
                        foreach (var setKey in results)
                            await dbContext.RecursiveLoadMappedKeys(setKey, [], 1, maxDepth ?? 3);
                    }

                    return Ok(results.AsQueryable().PaginateWithLinks(this, (matches) =>
                    {
                        List<Dictionary<string, object>> mappings = [];

                        foreach (var set in matches.Select(i => i.Set).Distinct())
                        {
                            var keyValuesIncluded = matches.Where(i => i.SetId == set.Id);

                            if (!keyValuesIncluded.Any())
                                continue;

                            var thisListKey = keyValuesIncluded.Select(i => i.Value.List).FirstOrDefault();

                            if (thisListKey is null)
                                continue;

                            var thisVal = new Dictionary<string, object>
                            {
                                {
                                    "set",
                                    new
                                    {
                                        name = set.Name,
                                        setId = set.Id,
                                        version = set.Version,
                                        source = set.Source,
                                        authority = set.Authority,
                                        key = thisListKey.Name
                                    }
                                },
                            };

                            var resultVal = keyValuesIncluded.Select(ReferenceDataResultSet.GetSetKeyValueDto);
                            thisVal.Add("data", resultVal);

                            mappings.Add(thisVal);
                        }

                        return mappings;

                    }, pagingParameters.limit, pagingParameters.offset));
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "{ExceptionMessage}", ex.Message);
                    return Problem("Internal server error.", null, 500);
                }
                finally
                {
                    logger.LogTrace("Leaving method");
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="csvFile"></param>
        /// <param name="setName"></param>
        /// <param name="setKey"></param>
        /// <param name="version"></param>
        /// <param name="source"></param>
        /// <param name="authority"></param>
        /// <param name="typeMappings"></param>
        /// <returns></returns>
        /// <exception cref="ReferenceDataException"></exception>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueWrite, ScopeDefinitions.RefDataSetWrite)]
        [Route("internal/importCsv")]
        public ActionResult<ReferenceDataImportJob> ImportCsv(IFormFile csvFile, [FromForm] string setName, [FromForm] string setKey, [FromForm] string? version,
            [FromForm] string? source, [FromForm] string? authority, [FromForm] Dictionary<string, string>? typeMappings)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(ImportCsv), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                logger.LogTrace("Entering method");

                try
                {
                    typeMappings ??= [];
                    var keyType = typeMappings.FirstOrDefault(tm => tm.Key.Equals(setKey, StringComparison.OrdinalIgnoreCase)).Value;
                    
                    if (string.IsNullOrEmpty(keyType))
                        keyType = ReferenceDataListType.String.ToString();

                    source ??= csvFile.FileName;
                    version ??= string.Empty;
                    setName = NormalizeName(setName);

                    if (dbContext.SetExists(setName, version ?? string.Empty, setKey, source, authority, Guid.Empty))
                        throw new ReferenceDataException(409, $"Set already exists with name '{setName}' and version '{version}'.");

                    if (!Enum.TryParse<ReferenceDataListType>(keyType, true, out var keyListType))
                    {
                        logger.LogError("Invalid key list type: {ListType}", keyType);
                        throw new ReferenceDataException(400, $"Invalid key list type: {keyType}");
                    }

                    var importJob = new ReferenceDataImportJob()
                    {
                        JobId = Guid.NewGuid(),
                        StartTime = DateTimeOffset.UtcNow,
                        TenantId = Guid.Empty
                    };

                    dbContext.ImportJobs.Add(importJob);
                    dbContext.SaveChanges();

                    var jobId = importJob.JobId;
                    var csvStream = new MemoryStream();
                    csvFile.OpenReadStream().CopyTo(csvStream);

                    Task.Run(() =>
                    {
                        using (var scope = scopeFactory.CreateScope())
                        {
                            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                            int valueCount = 0;

                            var job = dbContext.ImportJobs.FirstOrDefault(j => j.JobId == jobId);
                            if (job is null)
                                throw new Exception("Could not find import job!");

                            using var transaction = dbContext.Database.BeginTransaction();

                            // parse CSV
                            try
                            {
                                var set = dbContext.GetOrCreateSet(setName, version ?? string.Empty, setKey, keyListType, source, authority, Guid.Empty);
                                var keyList = set.KeyList ?? dbContext.Lists.FirstOrDefault(l => l.Id == set.KeyListId);

                                if (keyList is null)
                                    throw new ReferenceDataException(400, $"List '{setKey}' was not found as a key in set '{set.Name}'.");

                                var keyName = keyList.Name;
                                keyListType = keyList.Type;

                                var dataDictionary = new Dictionary<string, string?>();
                                csvStream.Position = 0;
                                using (var reader = new StreamReader(csvStream))
                                using (var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)))
                                {
                                    // Read the header record to get the keys
                                    csv.Read();
                                    csv.ReadHeader();
                                    var headers = csv.HeaderRecord;

                                    if (headers is null || headers.Length == 0)
                                        throw new ReferenceDataException(400, $"No headers found in CSV file '{csvFile.FileName}'");

                                    // Read the data records
                                    while (csv.Read())
                                    {
                                        // Clear the dictionary before adding new row data
                                        dataDictionary.Clear();

                                        // Iterate through the headers and get the corresponding value for each
                                        for (int i = 0; i < headers.Length; i++)
                                        {
                                            string header = headers[i];
                                            var value = csv.GetField(i);
                                            dataDictionary[header] = value;
                                        }

                                        logger.LogDebug("CSV Row Data: {CsvRow}", string.Join(", ", dataDictionary.Select(kv => $"{kv.Key}: {kv.Value}")));

                                        // process this row
                                        var keyValueThisRow = dataDictionary.FirstOrDefault(kvp => kvp.Key.Equals(keyList.Name, StringComparison.CurrentCultureIgnoreCase));

                                        if (string.IsNullOrEmpty(keyValueThisRow.Key) || string.IsNullOrEmpty(keyValueThisRow.Value))
                                            throw new ReferenceDataException(400, $"Key field '{keyList.Name}' was not found for row {csv.Parser.Row}");

                                        var convertedKeyValue = ReferenceDataListValue.ConvertValue(keyListType, keyValueThisRow.Value);

                                        foreach (var kvp in dataDictionary)
                                        {
                                            var currentListType = typeMappings.FirstOrDefault(tm => tm.Key.Equals(kvp.Key, StringComparison.OrdinalIgnoreCase)).Value;

                                            if (string.IsNullOrEmpty(currentListType))
                                                currentListType = ReferenceDataListType.String.ToString();

                                            if (!Enum.TryParse<ReferenceDataListType>(currentListType, true, out var currentListTypeType))
                                            {
                                                logger.LogError("Invalid list type for CSV field '{FieldName}': {ListType}", kvp.Key, currentListType);
                                                throw new ReferenceDataException(400, $"Invalid list type for CSV field '{kvp.Key}': {currentListType}");
                                            }

                                            var convertedVal = ReferenceDataListValue.ConvertValue(currentListTypeType, kvp.Value);

                                            var setValue = dbContext.GetOrCreateSetValue(set.Id, CreationContext.Import, keyName, convertedKeyValue, kvp.Key, convertedVal, currentListTypeType, null, Guid.Empty);
                                        }

                                        // Increment value count
                                        valueCount++;
                                    }
                                }

                                job.SetId = set.Id;
                                job.EndTime = DateTimeOffset.UtcNow;
                                dbContext.ImportJobs.Update(job);
                                dbContext.SaveChanges();
                                transaction.Commit();
                            }
                            catch (Exception ex)
                            {
                                transaction.Rollback();

                                // store the error about the job
                                job.SetId = null;
                                job.Error = ex.Message;
                                job.EndTime = DateTimeOffset.UtcNow;
                                dbContext.ImportJobs.Update(job);
                                dbContext.SaveChanges();
                            }
                        }
                    }, CancellationToken.None);

                    return Accepted(Url.Action(nameof(GetImportJob), new { jobId = importJob.JobId }), importJob);
                }
                catch (ReferenceDataException rex)
                {
                    return Problem(rex.Message, null, rex.StatusCode);
                }
                catch (Exception ex)
                {
                    return Problem(ex.Message, null, 500);
                }
                finally
                {
                    logger.LogTrace("Leaving method");
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="ReferenceDataException"></exception>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.RefDataSetValueWrite, ScopeDefinitions.RefDataSetWrite)]
        [Route("internal/intake")]
        public ActionResult<ReferenceDataImportJob> IntakeReferenceData([FromBody] ReferenceDataDto data)
        {
            using (logger.BeginScope(new { CodeMethod = nameof(IntakeReferenceData), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                logger.LogTrace("Entering method");

                try
                {
                    var setName = NormalizeName(data.Name);

                    if (string.IsNullOrEmpty(setName))
                        throw new ReferenceDataException(400, "Set name is required.");

                    data.Name = setName;

                    if (dbContext.Sets.Any(s => string.Equals(s.Name.ToLower(), data.Name.ToLower()) &&
                                                string.Equals(s.Version.ToLower(), (data.Version ?? string.Empty).ToLower())))
                    {
                        logger.LogWarning("Reference data set '{SetName}' already exists for the specified name and version.", data.Name);
                        throw new ReferenceDataException(409, $"Data set already exists with name '{data.Name}' and version '{data.Version}'.");
                    }

                    var importJob = new ReferenceDataImportJob()
                    {
                        JobId = Guid.NewGuid(),
                        StartTime = DateTimeOffset.UtcNow,
                    };

                    dbContext.ImportJobs.Add(importJob);
                    dbContext.SaveChanges();

                    var jobId = importJob.JobId;

                    Task.Run(() =>
                    {
                        using var scope = scopeFactory.CreateScope();
                        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                        var job = dbContext.ImportJobs.FirstOrDefault(j => j.JobId == jobId);

                        if (job is null)
                            return;

                        using var transaction = dbContext.Database.BeginTransaction(IsolationLevel.Snapshot);
                        try
                        {
                            var sampleKeyValue = data.Data.FirstOrDefault()?.FirstOrDefault(d => string.Equals(data.Key, d.Name, StringComparison.OrdinalIgnoreCase)
                                                                                                  && string.IsNullOrEmpty(d.InSet?.Name) && d.InSet?.SetId is null)?.Value;

                            var keyList = dbContext.GetOrCreateList(data.Key, ReferenceDataList.GuessListType(sampleKeyValue));
                            var dataSet = dbContext.GetOrCreateSet(data.Name, data.Version ?? string.Empty, keyList.Name, keyList.Type, data.Source, data.Authority, Guid.Empty);
                            var keyName = keyList.Name;

                            foreach (var val in data.Data)
                            {
                                var keyDataItem = val.FirstOrDefault(d => string.Equals(keyName, d.Name, StringComparison.OrdinalIgnoreCase) &&
                                                                          string.IsNullOrEmpty(d.InSet?.Name) && d.InSet?.SetId is null);

                                var keyValueValue = keyDataItem?.Value is string strVal ? strVal : keyDataItem?.Value?.ToString();

                                if (string.IsNullOrEmpty(keyValueValue))
                                {
                                    logger.LogWarning("Missing value for key '{KeyName}' in set '{SetName}'", keyName, data.Name);
                                    throw new ReferenceDataException(422, $"Missing value for key '{keyName}' in set '{dataSet.Name}'");
                                }

                                foreach (var v in val)
                                {
                                    long mappedKeySetValueId = 0;
                                    // are we mapping from one set value to another set key value?
                                    if (v.InSet is not null)
                                    {
                                        ReferenceDataSet? mappedSet = null;
                                        if (v.InSet.SetId > 0)
                                        {
                                            mappedSet = dbContext.Sets.Include(s => s.KeyList).Include(s => s.SetKeyValues).FirstOrDefault(s => s.Id == v.InSet.SetId);

                                            if (mappedSet is null)
                                            {
                                                logger.LogWarning("Reference data set with ID {SetId} does not exist while mapping set value {ValueName} to external key value.", v.InSet.SetId, v.Name);
                                                throw new ReferenceDataException(404, $"Reference data set with ID {v.InSet.SetId} does not exist.");
                                            }
                                        }
                                        else if (!string.IsNullOrEmpty(v.InSet.Name))
                                        {
                                            v.InSet.Version ??= string.Empty;
                                            mappedSet = dbContext.Sets.Include(s => s.KeyList).Include(s => s.SetKeyValues).FirstOrDefault(s => string.Equals(s.Name.ToLower(), v.InSet.Name) &&
                                                                                                                                                string.Equals(s.Version.ToLower(), v.InSet.Version));
                                            if (mappedSet is null)
                                            {
                                                logger.LogWarning("Reference data set with name '{SetName}' and version '{SetVersion}' does not exist while mapping set value {ValueName} to external key value", v.InSet.Name, v.InSet.Version, v.Name);
                                                throw new ReferenceDataException(404, $"Reference data set with name '{v.InSet.Name}' and version '{v.InSet.Version}' does not exist.");
                                            }
                                        }
                                        else
                                        {
                                            logger.LogWarning("Attempted to map set value to external set key value but no set detail was provided.");
                                            throw new ReferenceDataException(422, "Attempted to map set value to external set key value but no set detail was provided.");
                                        }

                                        // find the value first
                                        var keyValueInSet = dbContext.GetListValue(mappedSet.KeyList.Name, v.Value);

                                        if (keyValueInSet is null)
                                        {
                                            logger.LogWarning("No such value '{Value}' exists for list '{ListName}'", v.Value, mappedSet.KeyList.Name);
                                            throw new ReferenceDataException(422, $"No such value '{v.Value}' exists for list '{mappedSet.KeyList.Name}'");
                                        }

                                        // find the key specified
                                        var mappedKey = mappedSet.SetKeyValues.FirstOrDefault(kv => kv.ValueId == keyValueInSet.Id);

                                        if (mappedKey is null)
                                        {
                                            logger.LogWarning("Value '{Value}' does not exist as a key in set {SetName}.", v.Value, mappedSet.Name);
                                            throw new ReferenceDataException(404, $"Value '{v.Value}' does not exist as a key in set '{mappedSet.Name}'");
                                        }

                                        mappedKeySetValueId = mappedKey.Id;
                                    }

                                    var listType = ReferenceDataList.GuessListType(v.Value);
                                    var setValue = dbContext.GetOrCreateSetValue(dataSet.Id, CreationContext.Import, keyName, keyValueValue, v.Name, v.Value, listType, v.Alias, Guid.Empty);

                                    if (mappedKeySetValueId > 0)
                                    {
                                        setValue.MappedKeyValueId = mappedKeySetValueId;
                                        dbContext.Update(setValue);
                                        dbContext.SaveChanges();
                                    }
                                }
                            }

                            job.SetId = dataSet.Id;
                            job.EndTime = DateTimeOffset.UtcNow;
                            dbContext.ImportJobs.Update(job);
                            dbContext.SaveChanges();
                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            job.EndTime = DateTimeOffset.UtcNow;
                            job.Error = ex.Message;
                            job.SetId = null;

                            try
                            {
                                dbContext.ImportJobs.Update(job);
                                dbContext.SaveChanges();
                            }
                            catch (Exception jobUpdateEx)
                            {
                                logger.LogError(jobUpdateEx, "Error saving import job with ID {JobId}", job.JobId);
                            }
                        }
                    }, CancellationToken.None);

                    return Accepted(Url.Action(nameof(GetImportJob), new { jobId = importJob.JobId }), importJob);
                }
                catch (ReferenceDataException rex)
                {
                    return Problem(rex.Message, null, rex.StatusCode);
                }
                catch (Exception ex)
                {
                    return Problem(ex.Message, null, 500);
                }
                finally
                {
                    logger.LogTrace("Leaving method");
                }
            }
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataDto
    {
        public string? Source { get; set; }
        public string? Version { get; set; }
        public string? Authority { get; set; }
        public string Name { get; set; } = null!;
        public string Key { get; set; } = null!;
        public ICollection<ReferenceDataValueSetDto> Data { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetAlternateProcessDto
    {
        public object? SelectedValue { get; set; }
        public ReferenceDataValueSetDto? Schema { get; set; }
        public Guid? TenantId { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataListTypeDto
    {
        public string TypeName { get; set; } = null!;
        public int TypeId { get; set; }

        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        public object? DefaultValue { get; set; }

        public ReferenceDataListTypeDto() { }
        public ReferenceDataListTypeDto(ReferenceDataListType type)
        {
            var dto = Get(type);
            TypeName = dto.TypeName;
            TypeId = dto.TypeId;
            DefaultValue = dto.DefaultValue;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static ReferenceDataListTypeDto Get(ReferenceDataListType type)
        {
            return type switch
            {
                ReferenceDataListType.String => new ReferenceDataListTypeDto() { TypeId = (int)ReferenceDataListType.String, TypeName = ReferenceDataListType.String.ToString(), DefaultValue = ReferenceDataListValue.DefaultString },
                ReferenceDataListType.Boolean => new ReferenceDataListTypeDto() { TypeId = (int)ReferenceDataListType.Boolean, TypeName = ReferenceDataListType.Boolean.ToString(), DefaultValue = ReferenceDataListValue.DefaultBoolean },
                ReferenceDataListType.DateTime => new ReferenceDataListTypeDto() { TypeId = (int)ReferenceDataListType.DateTime, TypeName = ReferenceDataListType.DateTime.ToString(), DefaultValue = ReferenceDataListValue.DefaultDateTime },
                ReferenceDataListType.Integer => new ReferenceDataListTypeDto() { TypeId = (int)ReferenceDataListType.Integer, TypeName = ReferenceDataListType.Integer.ToString(), DefaultValue = ReferenceDataListValue.DefaultInteger },
                ReferenceDataListType.Float => new ReferenceDataListTypeDto() { TypeId = (int)ReferenceDataListType.Float, TypeName = ReferenceDataListType.Float.ToString(), DefaultValue = ReferenceDataListValue.DefaultFloat },
                _ => throw new Exception($"Invalid list type: {type}"),
            };
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValueSetDto : List<ReferenceDataValueDto> { }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValueDto
    {
        public string Name { get; set; } = null!;
        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        public object? Value { get; set; }
        public string? Alias { get; set; }
        public ReferenceDataSetReferenceDto? InSet { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetReferenceDto
    {
        public long? SetId { get; set; }
        public string? Name { get; set; }
        public string? Version { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetSummaryDto
    {
        public string? Source { get; set; }
        public string? Authority { get; set; }
        public string Name { get; set; } = null!;
        public string Version { get; set; } = null!;
        public long SetId { get; set; }
        public long Count { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataImportJobDto
    {
        public Guid JobId { get; set; }
        public bool IsComplete { get; set; }
        public DateTimeOffset StartTime { get; set; }
        public DateTimeOffset? EndTime { get; set; }
        public ReferenceDataSetSummaryDto? Set { get; set; }
        public string? Error { get; set;  }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetDto
    {
        public string Source { get; set; } = null!;
        public string Authority { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string Version { get; set; } = null!;
        public string Key { get; set; } = null!;
        public string KeyType { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetAlternateDto
    {
        public long? SetId { get; set; }
        public string? Name { get; set; }
        public string? Version { get; set; }
        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        public object? Value { get; set; }
        public ReferenceDataSetAlternateBehaviorDto? Behavior { get; set; }
        public ReferenceDataSetKeyValueDto? Data { get; set; }
        public List<KeySetValueSchemaMember>? CreationSchema { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public enum ReferenceDataValidationType
    {
        Key,
        Value,
        Id,
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValidationDetailDto
    {
        public long? SetId { get; set; }
        public string? SetName { get; set; }
        public string? Version { get; set; }
        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        public object? Value { get; set; }
        public ReferenceDataValidationType ValidationType { get; set; } = ReferenceDataValidationType.Key;
        public string? PropertyName { get; set; }

    }

    public class ReferenceDataValidationDto : List<ReferenceDataValidationDetailDto>
    {
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataSetAlternateBehaviorDto
    {
        public bool? OnSelectRequireNewValue { get; set; }
        public bool? OnSelectAllowNewValue { get; set; }
        public bool? OnNewValueRequireAllFields { get; set; }
        public bool? OnNewValueRequireKeyValueOnly { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataListSummaryDto
    {
        public string Name { get; set; } = null!;
        public long ListId { get; set; }
        public ReferenceDataListTypeDto Type { get; set; } = null!;
        public long ValueCount { get; set; }
        public long SetCount { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataListDto
    {
        public string Name { get; set; } = null!;
        public string Type { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataListValueDto
    {
        public long? ValueId { get; set; }
        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        public object? Value { get; set; }
    }
}
