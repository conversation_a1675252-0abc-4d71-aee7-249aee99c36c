using System.Collections.Immutable;
using System.Text.Json.Nodes;
using Ethos.Model.Scheduling;
using Ethos.ReferenceData.Client;

namespace Ethos.Workflows.Api.Analysis;

public static class DataModel
{
    public static bool IsPrimitiveType(Type type)
    {
        // Enums are considered primitive for this purpose
        if (type.IsEnum)
        {
            return true;
        }
        
        return type.IsPrimitive || 
               type == typeof(string) || 
               type == typeof(DateTime) || 
               type == typeof(DateOnly) ||
               type == typeof(TimeOnly) ||
               type == typeof(DateTimeOffset) || 
               type == typeof(decimal) ||
               type == typeof(JsonNode) ||
               type == typeof(JsonObject) ||
               type == typeof(Guid);
    }
    
    public static bool IsDtoType(Type type)
    {
        // Check if the type is a DTO type
        return type.IsClass && !type.IsAbstract && type.Name.EndsWith("Dto");
    }
    
    public static bool IsValidDtoPropertyType(Type type)
    {
        if (Nullable.GetUnderlyingType(type) is Type underlyingType)
        {
            type = underlyingType;
        }
        
        // Generic collections:
        if (type.IsGenericType)
        {
            var genericTypeDefinition = type.GetGenericTypeDefinition();
            if (genericTypeDefinition == typeof(List<>) || 
                genericTypeDefinition == typeof(IImmutableList<>) || 
                genericTypeDefinition == typeof(IReadOnlyList<>) || 
                genericTypeDefinition == typeof(ICollection<>) || 
                genericTypeDefinition == typeof(IEnumerable<>))
            {
                return IsValidDtoPropertyType(type.GetGenericArguments()[0]);
            }
        }

        if (type == typeof(ReferenceDataValidationDto))
        {
            // Special casing bad design of ReferenceDataValidationDto.
            return true;
        }
        
        if (type == typeof(Expr))
        {
            // Expr is special-case allowed.
            return true;
        }
        
        // Check if the type is a primitive type or a DTO type
        return IsPrimitiveType(type) || IsDtoType(type);
    }
}