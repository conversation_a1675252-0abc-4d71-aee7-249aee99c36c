using Ethos.Workflows.Api;
using Ethos.Workflows.HttpClient;
using Xunit.Abstractions;

namespace Ethos.Workflows.Tests;

public class SchedulingTests
    : IClassFixture<CustomWebApplicationFactory<Startup>>
{
    private readonly CustomWebApplicationFactory<Startup> _factory;
    private readonly ITestOutputHelper _testOutputHelper;
    private readonly IPatientApi _patientApi;
    private readonly IInsuranceApi _insuranceApi;
    private readonly IPhysicianApi _physicianApi;
    private readonly AuthHttpClient _authClient;

    public SchedulingTests(CustomWebApplicationFactory<Startup> factory, ITestOutputHelper testOutputHelper)
    {
        _factory = factory;
        _testOutputHelper = testOutputHelper;

        // Create an HttpClient for the in-memory server
        System.Net.Http.HttpClient client = _factory.CreateClient();
        _authClient = new AuthHttpClient(client);
        
        // Build our typed client using that HttpClient
        _patientApi = new PatientHttpClient(client);
        _insuranceApi = new InsuranceHttpClient(client);
        _physicianApi = new PhysicianHttpClient(client);
    }
}