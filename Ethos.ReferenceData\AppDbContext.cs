﻿using Microsoft.EntityFrameworkCore;

namespace Ethos.ReferenceData
{
    /// <summary>
    /// 
    /// </summary>
    public class AppDbContext : DbContext
    {
        /// <summary>
        /// 
        /// </summary>
        public DbSet<ReferenceDataList> Lists { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<ReferenceDataListValue> ListValues { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<ReferenceDataSet> Sets { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<ReferenceDataSetValue> SetValues { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<ReferenceDataSetKeyValue> SetKeyValues { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<ReferenceDataImportJob> ImportJobs { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<ReferenceDataSetKeyValueAlternate> KeyValueAlternates { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        public AppDbContext(DbContextOptions<AppDbContext> options)
            : base(options) { }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="optionsBuilder"></param>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="value"></param>
        /// <param name="listType"></param>
        /// <returns></returns>
        public ReferenceDataListValue? GetListValue(string? name, object? value)
        {
            long listId = 0;
            var listType = ReferenceDataList.GuessListType(value);

            if (!string.IsNullOrEmpty(name))
            {
                var referenceList = GetList(name, listType);
                if (referenceList is not null)
                {
                    listId = referenceList.Id;
                    listType = referenceList.Type;
                }

                if (listId < 1)
                    return default;
            }

            var convertedVal = ReferenceDataListValue.ConvertValue(listType, value);

            var existingValue = ListValues.Include(lv => lv.List).FirstOrDefault(v => (v.ListId == listId || listId == 0) &&
                                                                                      (
                                                                                          (listType == ReferenceDataListType.Float && v.FloatValue == (double?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.Integer && v.IntegerValue == (long?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.Boolean && v.BooleanValue == (bool?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.DateTime && v.DateTimeValue == (DateTimeOffset?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.String && v.StringValue == (string?)convertedVal)
                                                                                      ));
            return existingValue;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public IEnumerable<ReferenceDataListValue> GetListValues(object? value)
        {
            var listType = ReferenceDataList.GuessListType(value);

            var convertedVal = ReferenceDataListValue.ConvertValue(listType, value);

            var existingValue = ListValues.Include(lv => lv.List).Where(v => (listType == ReferenceDataListType.Float && v.FloatValue == (double?)convertedVal) ||
                                                                             (listType == ReferenceDataListType.Integer && v.IntegerValue == (long?)convertedVal) ||
                                                                             (listType == ReferenceDataListType.Boolean && v.BooleanValue == (bool?)convertedVal) ||
                                                                             (listType == ReferenceDataListType.DateTime && v.DateTimeValue == (DateTimeOffset?)convertedVal) ||
                                                                             (listType == ReferenceDataListType.String && v.StringValue == (string?)convertedVal));
            return existingValue;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public ReferenceDataListValue GetOrCreateListValue(string name, object? value, ReferenceDataListType? listType = null)
        {
            listType ??= ReferenceDataList.GuessListType(value);

            var referenceList = GetOrCreateList(name, listType ?? ReferenceDataList.GuessListType(value));

            var convertedVal = ReferenceDataListValue.ConvertValue(referenceList.Type, value);

            var existingValue = ListValues.Include(lv => lv.List).FirstOrDefault(v => string.Equals(v.List.Name.ToLower(), name.ToLower()) &&
                                                                                      v.List.Type == listType && (
                                                                                          (listType == ReferenceDataListType.Float && v.FloatValue == (double?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.Integer && v.IntegerValue == (long?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.Boolean && v.BooleanValue == (bool?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.DateTime && v.DateTimeValue == (DateTimeOffset?)convertedVal) ||
                                                                                          (listType == ReferenceDataListType.String && v.StringValue == (string?)convertedVal)
                                                                                      ));

            if (existingValue is null)
            {
                existingValue = new ReferenceDataListValue()
                {
                    ListId = referenceList.Id,
                    List = referenceList,
                };
                existingValue.Value = convertedVal;

                ListValues.Add(existingValue);
                SaveChanges();
            }
            return existingValue;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public ReferenceDataList? GetList(string name, ReferenceDataListType type)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            return Lists.FirstOrDefault(l => string.Equals(l.Name.ToLower(), name.ToLower()) && l.Type == type);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public ReferenceDataList GetOrCreateList(string name, ReferenceDataListType type)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            var existingList = Lists.FirstOrDefault(l => string.Equals(l.Name.ToLower(), name.ToLower()) && l.Type == type);

            if (existingList is null)
            {
                existingList = new ReferenceDataList()
                {
                    Name = name,
                    Type = type,
                };
                Lists.Add(existingList);
                SaveChanges();
            }
            return existingList;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="nameOrAlias"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public IList<ReferenceDataList> GetListsByNameOrAlias(string nameOrAlias, ReferenceDataListType type)
        {
            if (string.IsNullOrEmpty(nameOrAlias))
                throw new ArgumentNullException(nameof(nameOrAlias));

            var lists = Lists.Where(l => string.Equals(l.Name.ToLower(), nameOrAlias.ToLower()) && l.Type == type).ToList();

            var aliasedLists = SetValues.Include(sv => sv.Value)
                                        .ThenInclude(sv => sv.List)
                                        .Where(sv => !string.IsNullOrEmpty(sv.Alias) &&
                                                     string.Equals(sv.Alias.ToLower(), nameOrAlias.ToLower()) &&
                                                     sv.Value.List.Type == type)
                                        .Select(sv => sv.Value.List)
                                        .Distinct()
                                        .ToList();

            return [.. lists.Union(aliasedLists)];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="keyValue"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool IsKeyInSet(long setId, object? keyValue, Guid? tenantId = null)
        {
            tenantId ??= Guid.Empty;
            var set = Sets.Include(s => s.SetKeyValues)
                              .ThenInclude(sk => sk.Value)
                              .ThenInclude(sk => sk.List)
                           .Include(s => s.KeyList)
                          .FirstOrDefault(s => s.Id == setId &&
                                           ((tenantId == s.TenantId && tenantId != Guid.Empty) || s.TenantId == Guid.Empty))
                          ?? throw new Exception($"No data set found with ID {setId}.");

            var convertedVal = ReferenceDataListValue.ConvertValue(set.KeyList.Type, keyValue);
            return set.SetKeyValues.Any(sk => Equals(sk.Value.Value, convertedVal));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="listName"></param>
        /// <param name="listValue"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ReferenceDataSetKeyValue GetOrCreateSetKeyValue(long setId, CreationContext creationContext, string listName, object? listValue, bool enabled = true, Guid? tenantId = null,
            long? keyTriggerId = null)
        {
            tenantId ??= Guid.Empty;
            var listType = ReferenceDataList.GuessListType(listValue);
            var list = GetOrCreateList(listName, listType);
            var value = GetOrCreateListValue(listName, listValue, list.Type);
            var set = Sets.FirstOrDefault(s => s.Id == setId) ?? throw new Exception($"No set found with ID {setId}.");

            if (set.KeyListId != list.Id)
                throw new Exception($"List '{list.Name}' of type '{list.Type}' cannot be used as a key in set '{set.Name}'.");

            var keyValue = SetKeyValues.FirstOrDefault(kv => kv.ValueId == value.Id &&
                                                             kv.SetId == setId &&
                                                             ((tenantId == kv.TenantId && tenantId != Guid.Empty) || kv.TenantId == Guid.Empty));

            if (keyValue is not null)
                return keyValue;

            keyValue = new ReferenceDataSetKeyValue()
            {
                SetId = setId,
                ValueId = value.Id,
                TenantId = tenantId ?? Guid.Empty,
                Enabled = enabled,
                CreationContext = creationContext,
                KeyTriggerId = keyTriggerId.HasValue && keyTriggerId.Value > 0 ? keyTriggerId : null,
            };
            SetKeyValues.Add(keyValue);
            SaveChanges();
            return keyValue;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="keyListName"></param>
        /// <param name="keyListValue"></param>
        /// <param name="listName"></param>
        /// <param name="listValue"></param>
        /// <param name="alias"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ReferenceDataSetValue GetOrCreateSetValue(long setId,
                                                         CreationContext creationContext,
                                                         string keyListName,
                                                         object? keyListValue,
                                                         string listName,
                                                         object? listValue,
                                                         ReferenceDataListType? listType = null,
                                                         string? alias = null,
                                                         Guid? tenantId = null,
                                                         long? keyTriggerId = null)
        {
            tenantId ??= Guid.Empty;
            listType ??= ReferenceDataList.GuessListType(listValue);
            var value = GetOrCreateListValue(listName, listValue, listType);
            var set = Sets.FirstOrDefault(s => s.Id == setId) ?? throw new Exception($"No set found with ID {setId}.");
            var keyValue = GetOrCreateSetKeyValue(setId, creationContext, keyListName, keyListValue, true, tenantId, keyTriggerId);
            var setValue = SetValues.FirstOrDefault(sv => sv.SetId == setId && sv.ValueId == value.Id && sv.KeyValueId == keyValue.Id &&
                                                          ((tenantId == sv.TenantId && tenantId != Guid.Empty) || sv.TenantId == Guid.Empty));

            if (setValue is not null)
                return setValue;

            setValue = new ReferenceDataSetValue()
            {
                SetId = setId,
                ValueId = value.Id,
                KeyValueId = keyValue.Id,
                TenantId = tenantId ?? Guid.Empty,
                Alias = alias?.Trim() ?? string.Empty,
            };

            SetValues.Add(setValue);
            SaveChanges();
            return setValue;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="version"></param>
        /// <param name="keyListName"></param>
        /// <param name="source"></param>
        /// <param name="authority"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public bool SetExists(string name,
                              string version,
                              string keyListName,
                              string? source = null,
                              string? authority = null,
                              Guid? tenantId = null)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            tenantId ??= Guid.Empty;
            source ??= string.Empty;
            authority ??= string.Empty;
            version ??= string.Empty;

            if (string.IsNullOrEmpty(version))
                version = new Version(1, 0).ToString();

            return Sets.Any(s => ((s.TenantId == tenantId && tenantId != Guid.Empty) || s.TenantId == Guid.Empty) &&
                                 string.Equals(s.Name.ToLower(), name.ToLower()) &&
                                 string.Equals((s.Version ?? string.Empty).ToLower(), version.ToLower()));

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setKeyValue"></param>
        /// <param name="visitedIds"></param>
        /// <param name="level"></param>
        /// <param name="maxLevel"></param>
        /// <returns></returns>
        public async Task RecursiveLoadMappedKeys(ReferenceDataSetKeyValue setKeyValue, HashSet<long> visitedIds, int level, int maxLevel = 5)
        {
            if (level > maxLevel)
                return;

            if (setKeyValue is null)
                return;

            if (visitedIds.Contains(setKeyValue.Id))
                return;

            visitedIds.Add(setKeyValue.Id);

            //Load SetValues
            await Entry(setKeyValue)
                   .Collection(k => k.SetValues)
                   .Query()
                   .Include(sv => sv.Value)
                   .ThenInclude(v => v.List)
                   .Include(sv => sv.MappedKeyValue)
                   .ThenInclude(sv => sv.SetValues)
                   .ThenInclude(sv => sv.Value)
                   .ThenInclude(sv => sv.List)
                   .LoadAsync();

            foreach (var setValue in setKeyValue.SetValues.Where(sv => sv.MappedKeyValueId > 0 && sv.MappedKeyValue is not null))
            {
                if (setValue is null || setValue.MappedKeyValue is null)
                    continue;

                await RecursiveLoadMappedKeys(setValue.MappedKeyValue, visitedIds, level + 1, maxLevel);
            }
        }

        //public async IEnumerable<ReferenceDataSetKeyValue> RecursiveLoadMappedKeys(IEnumerable<long> ids, int level)
        //{
        //    if (level > 5 || level < 1)
        //        return [];

        //    if (!ids.Any())
        //        return [];

        //    var level1 = new Dictionary<int, List<ReferenceDataSetKeyValue>>();

        //    var baseQuery = SetKeyValues.Where(kv => ids.Contains(kv.Id))
        //            .Include(sv => sv.Value)
        //           .ThenInclude(v => v.List)
        //           .Include(v => v.SetValues)
        //           .ThenInclude(sv => sv.MappedKeyValue)
        //           .ThenInclude(sv => sv.SetValues)
        //           .ThenInclude(sv => sv.Value)
        //           .ThenInclude(sv => sv.List);

        //    var mappedToThisSet = SetValues.Include(sv => sv.MappedKeyValue).ThenInclude(sv => sv.SetValues)
        //           .ThenInclude(sv => sv.Value)
        //           .ThenInclude(sv => sv.List)
        //           .Where(sv => sv.MappedKeyValueId > 0 && )

        //    if (level >= 2)
        //    {
        //        if (!baseQuery.Any(v => v))
        //    }
            


        //    if (level >= 1)
        //    {
        //        baseQuery = baseQuery
        //            .Include(sv => sv.Value)
        //           .ThenInclude(v => v.List)
        //           .Include(v => v.SetValues)
        //           .ThenInclude(sv => sv.MappedKeyValue)
        //           .ThenInclude(sv => sv.SetValues)
        //           .ThenInclude(sv => sv.Value)
        //           .ThenInclude(sv => sv.List);
        //    }

        //    foreach (var setValue in setKeyValue.SetValues.Where(sv => sv.MappedKeyValueId > 0 && sv.MappedKeyValue is not null))
        //    {
        //        if (setValue is null || setValue.MappedKeyValue is null)
        //            continue;

        //        await RecursiveLoadMappedKeys(setValue.MappedKeyValue, visitedIds, level + 1, maxLevel);
        //    }
        //}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="setId"></param>
        /// <param name="schema"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<KeySetValueSchemaMember> GetKeySetValueFields(ReferenceDataSet sourceSet, SchemaBehavior schema = SchemaBehavior.All)
        {
            if (sourceSet is null)
                throw new Exception($"Data set is required.");

            if (sourceSet.KeyList is null)
                throw new Exception($"No key found in set '{sourceSet.Name}'");

            if (schema == SchemaBehavior.KeyOnly)
                return [ new() { DisplayName = sourceSet.KeyList.Name, Type = sourceSet.KeyList.Type.ToString(), Source = sourceSet.KeyList.Name } ];

            if (sourceSet.SetValues is null)
                throw new Exception($"No values found in set '{sourceSet.Name}'");

            var propertyLists = sourceSet.SetValues.Where(sv => (sv.MappedKeyValueId ?? 0) == 0 && 
                                                                    !string.IsNullOrEmpty(sv.Alias))
                                                      .Select(sv => (sv.Alias, sv.Value.List.Type, sv.Value.List.Name)).Distinct();

            propertyLists = propertyLists.Union(sourceSet.SetValues.Where(sv => (sv.MappedKeyValueId ?? 0) == 0 && 
                                                                 string.IsNullOrEmpty(sv.Alias))
                                                   .Select(sv => (sv.Value.List.Name, sv.Value.List.Type, sv.Value.List.Name)).Distinct());

            propertyLists = propertyLists.Union([(sourceSet.KeyList.Name, sourceSet.KeyList.Type, sourceSet.KeyList.Name)]);

            return new(propertyLists.Select(pl => new KeySetValueSchemaMember() { DisplayName = pl.Alias, Type = pl.Type.ToString(), Source = pl.Name }));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="version"></param>
        /// <param name="keyListName"></param>
        /// <param name="source"></param>
        /// <param name="authority"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public ReferenceDataSet GetOrCreateSet(string name,
                                               string version,
                                               string keyListName,
                                               ReferenceDataListType? keyListType = null,
                                               string? source = null,
                                               string? authority = null,
                                               Guid? tenantId = null)
        {
            if (string.IsNullOrEmpty(name)) 
                throw new ArgumentNullException(nameof(name));

            tenantId ??= Guid.Empty;

            if (string.IsNullOrEmpty(version))
                version = string.Empty;

            var set = Sets.Include(s => s.KeyList)
                          .FirstOrDefault(s => ((s.TenantId == tenantId && tenantId != Guid.Empty) || s.TenantId == Guid.Empty) &&
                                               string.Equals(s.Name.ToLower(), name.ToLower()) &&
                                               string.Equals(s.Version.ToLower(), version.ToLower()));

            if (set is not null)
                return set;

            var keyList = GetOrCreateList(keyListName, keyListType ?? ReferenceDataListType.String);

            set = new ReferenceDataSet()
            {
                 Name = name,
                 Authority = authority,
                 KeyListId = keyList.Id,
                 Source = source,
                 TenantId = tenantId ?? Guid.Empty,
                 Version = version
            };

            Sets.Add(set);
            SaveChanges();
            return set;
        }
		
		const string RefData = nameof(RefData);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="modelBuilder"></param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ReferenceDataList>(entity =>
            {
                entity.ToTable("ReferenceDataLists", RefData);

                entity.HasKey(l => l.Id);

                entity.Property(l => l.Id)
                      .ValueGeneratedOnAdd();

                entity.Property(l => l.Name)
                      .IsRequired();

                entity.Property(l => l.Type)
                      .IsRequired()
                      .HasDefaultValue(ReferenceDataListType.String);

                entity.HasIndex(l => new { l.Name, l.Type })
                      .IsUnique();

                entity
                    .HasMany(l => l.Values)
                    .WithOne(lv => lv.List)
                    .HasForeignKey(l => l.ListId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ReferenceDataListValue>(entity =>
            {
                entity.ToTable("ReferenceDataListValues", RefData);

                entity.Property(lv => lv.BooleanValue)
                .IsRequired().HasDefaultValue(ReferenceDataListValue.DefaultBoolean);

                entity.Property(lv => lv.DateTimeValue)
               .IsRequired().HasDefaultValue(ReferenceDataListValue.DefaultDateTime);

                entity.Property(lv => lv.IntegerValue)
                .IsRequired().HasDefaultValue(ReferenceDataListValue.DefaultInteger);

                entity.Property(lv => lv.FloatValue)
                .IsRequired().HasDefaultValue(ReferenceDataListValue.DefaultFloat);

                entity.Property(lv => lv.StringValue)
                .IsRequired().HasDefaultValue(ReferenceDataListValue.DefaultString);

                entity.HasKey(lv => lv.Id);

                entity.Property(lv => lv.Id)
                      .ValueGeneratedOnAdd();

                entity.HasIndex(lv => new { lv.ListId, lv.BooleanValue, lv.StringValue, lv.DateTimeValue, lv.IntegerValue, lv.FloatValue })
                      .IsUnique();

                entity.HasIndex(lv => new { lv.BooleanValue, lv.StringValue, lv.DateTimeValue, lv.IntegerValue, lv.FloatValue });

                entity
                    .HasOne(lv => lv.List)
                    .WithMany(l => l.Values)
                    .HasForeignKey(lv => lv.ListId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ReferenceDataSet>(entity =>
            {
                entity.ToTable("ReferenceDataSets", RefData);

                entity.HasKey(s => s.Id);

                entity.Property(s => s.Id)
                      .ValueGeneratedOnAdd();

                entity.Property(s => s.Source)
                      .IsRequired()
                      .HasDefaultValue(string.Empty);

                entity.Property(s => s.Version)
                      .IsRequired()
                      .HasDefaultValue(string.Empty);

                entity.Property(s => s.TenantId)
                      .IsRequired()
                      .HasDefaultValue(Guid.Empty);

                entity.Property(s => s.Name)
                      .IsRequired();

                entity.HasIndex(s => new { s.Name, s.Version })
                      .IsUnique();

                entity
                    .HasOne(s => s.KeyList)
                    .WithMany(l => l.Sets)
                    .HasForeignKey(s => s.KeyListId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasMany(s => s.AlternateKeyValues)
                    .WithOne(l => l.Set)
                    .HasForeignKey(l => l.SetId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasOne(s => s.ImportJob)
                    .WithOne(l => l.Set)
                    .IsRequired(false)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<ReferenceDataSetValue>(entity =>
            {
                entity.ToTable("ReferenceDataSetValues", RefData);

                entity.Property(sv => sv.Alias)
                      .IsRequired()
                      .HasDefaultValue(string.Empty);

                entity.Property(sv => sv.TenantId)
                      .IsRequired()
                      .HasDefaultValue(Guid.Empty);

                entity.HasKey(sv => new { sv.SetId, sv.TenantId, sv.ValueId, sv.KeyValueId, sv.Alias });

                entity
                    .HasOne(sv => sv.Value)
                    .WithMany(lv => lv.SetValues)
                    .HasForeignKey(sv => sv.ValueId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasOne(sv => sv.KeyValue)
                    .WithMany(kv => kv.SetValues)
                    .HasForeignKey(sv => sv.KeyValueId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasOne(sv => sv.MappedKeyValue)
                    .WithMany(kv => kv.MappedSetValues)
                    .HasForeignKey(sv => sv.MappedKeyValueId)
                    .IsRequired(false)
                    .OnDelete(DeleteBehavior.SetNull);

                entity
                    .HasOne(sv => sv.Set)
                    .WithMany(s => s.SetValues)
                    .HasForeignKey(sv => sv.SetId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ReferenceDataSetKeyValueAlternate>(entity =>
            {
                entity.ToTable("ReferenceDataSetKeyValueAlternates", RefData);

                entity.Property(s => s.TenantId)
                      .IsRequired()
                      .HasDefaultValue(Guid.Empty);

                entity.Property(s => s.TriggerBehavior)
                      .IsRequired()
                      .HasDefaultValue(KeyTriggerBehavior.None);

                entity.Property(s => s.SchemaBehavior)
                      .IsRequired()
                      .HasDefaultValue(SchemaBehavior.All);

                entity.HasKey(kv => new { kv.SetId, kv.KeyValueId, kv.TenantId });

                entity
                    .HasOne(kv => kv.KeyValue)
                    .WithMany(kv => kv.AlternateKeys)
                    .HasForeignKey(sv => sv.KeyValueId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ReferenceDataSetKeyValue>(entity =>
            {
                entity.ToTable("ReferenceDataSetKeyValues", RefData);

                entity.Property(kv => kv.Id)
                      .ValueGeneratedOnAdd();

                entity.HasKey(kv => kv.Id);

                entity.Property(s => s.TenantId)
                      .IsRequired()
                      .HasDefaultValue(Guid.Empty);

                entity.Property(s => s.CreationContext)
                      .IsRequired()
                      .HasDefaultValue(CreationContext.System);

                entity.HasIndex(kv => new { kv.SetId, kv.ValueId, kv.TenantId })
                      .IsUnique();

                entity
                    .HasOne(kv => kv.Value)
                    .WithMany(lv => lv.SetKeyValues)
                    .HasForeignKey(sv => sv.ValueId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                     .HasOne(c => c.KeyTrigger)
                     .WithMany(c => c.TriggeredKeys)
                     .HasForeignKey(c => c.KeyTriggerId)
                     .OnDelete(DeleteBehavior.ClientSetNull)
                     .IsRequired(false)
                     .OnDelete(DeleteBehavior.SetNull);

                entity
                    .HasMany(kv => kv.MappedSetValues)
                    .WithOne(lv => lv.MappedKeyValue)
                    .HasForeignKey(sv => sv.MappedKeyValueId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity
                    .HasOne(kv => kv.Set)
                    .WithMany(s => s.SetKeyValues)
                    .HasForeignKey(kv => kv.SetId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ReferenceDataImportJob>(entity =>
            {
                entity.ToTable("ReferenceDataImportJobs", RefData);

                entity.HasKey(kv => kv.JobId);

                entity.Property(s => s.TenantId)
                      .IsRequired()
                      .HasDefaultValue(Guid.Empty);

                entity.Property(s => s.StartTime)
                      .IsRequired();

                entity
                     .HasOne(p => p.Set)
                     .WithOne(d => d.ImportJob)
                     .HasForeignKey<ReferenceDataImportJob>(d => d.SetId)
                     .IsRequired(false)
                     .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}
