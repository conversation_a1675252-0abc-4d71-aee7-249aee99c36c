using Ethos.Auth;
using Ethos.Utilities;
using Ethos.Utilities.Filtering;
using Ethos.Utilities.Pagination;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("api/licenses")]
    [Authorize]
    [EthosAuthFeature(Name = FeatureConstants.Core)]
    public class LicenseController : ControllerBase
    {
        readonly ILogger<LicenseController> _logger;
        readonly PlatformManagerDbContext _dbContext;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="dbContext"></param>
        public LicenseController(ILogger<LicenseController> logger, PlatformManagerDbContext dbContext)
        {
            _logger = logger;
            _dbContext = dbContext;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="license"></param>
        /// <returns></returns>
        [HttpPost]
        [EthosAuthScope(ScopeDefinitions.LicenseWrite)]
        public async Task<ActionResult<LicenseDto>> CreateLicense([FromBody] LicenseDto license)
        {
            if (!license.Id.HasValue || license.Id.Value == Guid.Empty)
                license.Id = Guid.NewGuid();

            if (string.IsNullOrEmpty(license.State))
                license.State = LicenseState.Active.ToString();

            if (!Enum.TryParse<LicenseState>(license.State, true, out var state))
                return this.EthosErrorBadRequest($"Invalid license state: {license.State}");

            var existingLic = _dbContext.Licenses.FirstOrDefault(l => l.Id == license.Id);

            if (existingLic is not null)
                return this.EthosErrorConflict($"License already exists with ID {license.Id}");

            if (!_dbContext.Tenants.Any(t => t.Id == license.TenantId))
                return this.EthosErrorNotFound($"No such tenant: {license.TenantId}");

            var lic = new License()
            {
                Created = DateTime.UtcNow,
                Name = license.Name,
                StartDate = license.StartDate ?? DateTimeOffset.UtcNow,
                EndDate = license.EndDate ?? DateTimeOffset.MaxValue,
                State = state,
                TenantId = license.TenantId,
                Id = license.Id.Value
            };

            _dbContext.Licenses.Add(lic);
            await _dbContext.SaveChangesAsync();
            return CreatedAtAction(nameof(GetLicense), new { licenseId = lic.Id }, GetLicenseDto(lic));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="license"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{licenseId}")]
        [EthosAuthScope(ScopeDefinitions.LicenseWrite)]
        public async Task<ActionResult<LicenseDto>> UpdateLicense([FromRoute] Guid licenseId, [FromBody] LicenseDto license)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            var existingLic = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId);

            if (existingLic is null)
                return this.EthosErrorNotFound($"License not found with ID {licenseId}");

            if (string.IsNullOrEmpty(license.State))
                license.State = existingLic.State.ToString();

            if (!Enum.TryParse<LicenseState>(license.State, true, out var state))
                return this.EthosErrorBadRequest($"Invalid license state: {license.State}");

            if (!_dbContext.Tenants.Any(t => t.Id == license.TenantId))
                return this.EthosErrorNotFound($"No such tenant: {license.TenantId}");

            existingLic.State = state;
            existingLic.Updated = DateTimeOffset.UtcNow;
            existingLic.EndDate = license.EndDate ?? existingLic.EndDate;
            existingLic.StartDate = license.StartDate ?? existingLic.StartDate;
            existingLic.TenantId = license.TenantId;

            _dbContext.Licenses.Update(existingLic);
            await _dbContext.SaveChangesAsync();
            return Ok(GetLicenseDto(existingLic));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licenseId"></param>
        /// <param name="licProd"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{licenseId}/products")]
        [EthosAuthScope(ScopeDefinitions.LicenseWrite, ScopeDefinitions.ProductRead)]
        public async Task<ActionResult<LicenseProductDto>> CreateLicenseProduct([FromRoute] Guid licenseId, [FromBody] LicenseProductDto licProd)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            var license = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId);
            if (license is null)
                return this.EthosErrorNotFound($"License not found with ID {licenseId}");

            var product = _dbContext.Products.Include(p => p.Features).ThenInclude(f => f.Feature).FirstOrDefault(p => p.Id == licProd.ProductId);

            if (product is null)
                return this.EthosErrorNotFound($"Product not found with ID {licProd.ProductId}");

            using var trans = _dbContext.Database.BeginTransaction();
            try
            {
                var lic = new LicenseProduct()
                {
                    Id = !licProd.Id.HasValue || licProd.Id.Value == Guid.Empty ? Guid.NewGuid() : licProd.Id.Value,
                    LicenseId = licenseId,
                    ProductId = product.Id,
                    Name = string.IsNullOrEmpty(licProd.Name) ? product.Name : licProd.Name,
                };

                _dbContext.LicenseProducts.Add(lic);
                await _dbContext.SaveChangesAsync();

                List<LicenseProductFeature> licensedFeatures = [];

                // Handle features on dto
                foreach (var feature in licProd.Features ?? [])
                {
                    if (feature is null)
                        continue;

                    var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == feature.FeatureId);
                    if (existingFeature is null)
                    {
                        return this.EthosErrorNotFound($"Feature with ID {feature.FeatureId} not found.");
                    }

                    // does the feature exist on the product already?
                    var existingLicFeat = product.Features.FirstOrDefault(f => f.FeatureId == feature.FeatureId);

                    if (existingLicFeat is null)
                    {
                        licensedFeatures.Add(new LicenseProductFeature()
                        {
                            Enabled = feature.Enabled,
                            FeatureId = existingFeature.Id,
                            Name = string.IsNullOrEmpty(feature.Name) ? existingFeature.Name : feature.Name,
                            LicenseProductId = lic.Id
                        });
                    }
                    else
                    {
                        licensedFeatures.Add(new LicenseProductFeature()
                        {
                            Enabled = feature.Enabled,
                            FeatureId = existingFeature.Id,
                            Name = string.IsNullOrEmpty(feature.Name) ? existingLicFeat.Name : feature.Name,
                            LicenseProductId = lic.Id,
                        });
                    }
                }

                // Add any features not present in the dto that are present on the product template
                foreach (var feat in product.Features.Where(f => !licensedFeatures.Any(lf => lf.FeatureId == f.FeatureId)))
                {
                    licensedFeatures.Add(new LicenseProductFeature()
                    {
                        Enabled = feat.Enabled,
                        FeatureId = feat.FeatureId,
                        LicenseProductId = lic.Id,
                        Name = feat.Name,
                    });
                }

                foreach (var feature in licensedFeatures)
                    _dbContext.LicenseProductFeatures.Add(feature);

                await _dbContext.SaveChangesAsync();
                trans.Commit();

                lic.Product = product;
                lic.License = license;
                lic.Features = licensedFeatures;
                return CreatedAtAction(nameof(GetLicenseProduct), new { licenseId, productId = lic.Id }, GetLicenseProductResponseDto(lic));
            }
            catch (Exception ex)
            {
                trans.Rollback();
                _logger.LogError(ex, "Error creating licensed product.");
                return Problem(ex.Message, null, 500);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licenseId"></param>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{licenseId}/products/{productId}/features")]
        [EthosAuthScope(ScopeDefinitions.LicenseRead, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<PagedResponse<LicenseFeatureDto>>> GetFeaturesOnLicenseProduct([FromRoute] Guid licenseId, [FromRoute] Guid productId, [FromQuery] PagingParameters pagingParameters)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            var existingLic = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId);

            if (existingLic is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} was not found.");

            var existingFeats = _dbContext.LicenseProductFeatures.Include(lp => lp.Feature)
                                                                 .Where(p => p.LicenseProductId == productId);

            return Ok(await existingFeats.PaginateWithLinksAsync<LicenseProductFeature, LicenseFeatureDto>(this, (feats) =>
            {
                return [..feats.Select(f => new LicenseFeatureDto()
                {
                    Enabled = f.Enabled,
                    FeatureId = f.FeatureId,
                    Name = f.Name,
                })];
            }, pagingParameters.limit, pagingParameters.offset));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="prdFeat"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{licenseId}/products/{productId}/features")]
        [EthosAuthScope(ScopeDefinitions.LicenseWrite, ScopeDefinitions.ProductRead)]
        public async Task<ActionResult<ProductDto>> AddFeatureToLicenseProduct([FromRoute] Guid licenseId, [FromRoute] Guid productId, [FromBody] LicenseFeatureDto feature)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            if (productId == Guid.Empty) 
                return this.EthosErrorBadRequest("Invalid product ID.");

            var existingLic = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId);

            if (existingLic is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} was not found.");

            var existingPrd = _dbContext.LicenseProducts.FirstOrDefault(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Licensed product with ID {productId} was not found.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == feature.FeatureId);
            if (existingFeature is null)
            {
                return this.EthosErrorNotFound($"Feature with ID {feature.FeatureId} was not found.");
            }

            _dbContext.LicenseProductFeatures.Add(new LicenseProductFeature()
            {
                Enabled = feature.Enabled,
                FeatureId = existingFeature.Id,
                Name = string.IsNullOrEmpty(feature.Name) ? existingFeature.Name : feature.Name,
                LicenseProductId = productId
            });
            await _dbContext.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licenseId"></param>
        /// <param name="productId"></param>
        /// <param name="featureId"></param>
        /// <param name="feature"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{licenseId}/products/{productId}/features/{featureId}")]
        [EthosAuthScope(ScopeDefinitions.LicenseWrite, ScopeDefinitions.ProductRead)]
        public async Task<ActionResult<ProductDto>> UpdateFeatureOnLicenseProduct([FromRoute] Guid licenseId, [FromRoute] Guid productId, [FromRoute] Guid featureId, [FromBody] LicenseFeatureDto feature)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var existingLic = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId);

            if (existingLic is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} was not found.");

            var existingPrd = _dbContext.LicenseProducts.FirstOrDefault(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Licensed product with ID {productId} was not found.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == featureId);
            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {feature.FeatureId} was not found.");

            var existingLicProdFeat = _dbContext.LicenseProductFeatures.FirstOrDefault(f => f.FeatureId == featureId && f.LicenseProductId == productId);
            if (existingLicProdFeat is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} was not found on licensed product with ID {productId}.");

            if (feature.FeatureId != Guid.Empty && feature.FeatureId != featureId)
                return this.EthosErrorBadRequest($"Feature ID {featureId} does not match feature ID {feature.FeatureId} in the request entity.");

            if (!string.IsNullOrEmpty(feature.Name))
                existingLicProdFeat.Name = feature.Name;

            existingLicProdFeat.Enabled = feature.Enabled;

            _dbContext.LicenseProductFeatures.Update(existingLicProdFeat);
            await _dbContext.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licenseId"></param>
        /// <param name="productId"></param>
        /// <param name="featureId"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{licenseId}/products/{productId}/features/{featureId}")]
        [EthosAuthScope(ScopeDefinitions.LicenseWrite, ScopeDefinitions.FeatureRead)]
        public async Task<ActionResult<ProductDto>> RemoveFeatureFromLicenseProduct([FromRoute] Guid licenseId, [FromRoute] Guid productId, [FromRoute] Guid featureId)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            if (featureId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid feature ID.");

            var existingLic = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId);

            if (existingLic is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} was not found.");

            var existingPrd = _dbContext.LicenseProducts.FirstOrDefault(p => p.Id == productId);

            if (existingPrd is null)
                return this.EthosErrorNotFound($"Licensed product with ID {productId} was not found.");

            var existingFeature = _dbContext.Features.FirstOrDefault(f => f.Id == featureId);
            if (existingFeature is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} was not found.");

            var existingLicProdFeat = _dbContext.LicenseProductFeatures.FirstOrDefault(f => f.FeatureId == featureId && f.LicenseProductId == productId);
            if (existingLicProdFeat is null)
                return this.EthosErrorNotFound($"Feature with ID {featureId} was not found on licensed product with ID {productId}.");

            _dbContext.LicenseProductFeatures.Remove(existingLicProdFeat);
            await _dbContext.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licenseId"></param>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{licenseId}/products/{productId}")]
        [EthosAuthScope(ScopeDefinitions.LicenseRead, ScopeDefinitions.ProductRead)]
        public async Task<ActionResult<LicenseProductDto>> GetLicenseProduct([FromRoute] Guid licenseId, [FromRoute] Guid productId)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            if (productId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid product ID.");

            var tenantId = this.GetTenantId();
            var canReadAllLicenses = HttpContext.User.IsAllowedScope(ScopeDefinitions.LicenseAdministrator);

            var license = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId && (l.TenantId == tenantId || canReadAllLicenses));
            if (license is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} was not found.");

            var licenseProduct = await _dbContext.LicenseProducts.Include(lp => lp.Features).FirstOrDefaultAsync(lp => lp.LicenseId == licenseId && lp.Id == productId);

            if (licenseProduct is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} does not contain licensed product ID {productId}.");

            return Ok(GetLicenseProductDto(licenseProduct));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licenseId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{licenseId}/products")]
        [EthosAuthScope(ScopeDefinitions.LicenseRead, ScopeDefinitions.ProductRead)]
        public async Task<ActionResult<PagedResponse<LicenseProductResponseDto>>> GetLicenseProducts([FromRoute] Guid licenseId, [FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            var tenantId = this.GetTenantIdOrDefault();
            var canReadAllLicenses = HttpContext.User.IsAllowedScope(ScopeDefinitions.LicenseAdministrator);

            var license = _dbContext.Licenses.FirstOrDefault(l => l.Id == licenseId && (l.TenantId == tenantId || canReadAllLicenses));

            if (license is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} was not found.");

            var licenseProducts = _dbContext.LicenseProducts.Include(lp => lp.Features).ThenInclude(f => f.Feature)
                                                            .Include(lp => lp.Product).ThenInclude(p => p.Features).ThenInclude(f => f.Feature)
                                                            .Include(lp => lp.License)
                                                            .Where(p => p.LicenseId == licenseId)
                                                            .Filter(filter)
                                                            .OrderBy(lp => lp.Id);


            return Ok(await licenseProducts.PaginateWithLinksAsync(this, (lps) =>
            {
                return lps.Select(lp => GetLicenseProductResponseDto(lp)).ToList();
            }, pagingParameters.limit, pagingParameters.offset));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lic"></param>
        /// <returns></returns>
        static LicenseDto GetLicenseDto(License lic)
        {
            return new LicenseDto()
            {
                Created = lic.Created,
                Name = lic.Name,
                StartDate = lic.StartDate,
                EndDate = lic.EndDate,
                State = lic.State.ToString(),
                TenantId = lic.TenantId,
                Id = lic.Id,
                Updated = lic.Updated,
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lic"></param>
        /// <returns></returns>
        static LicenseProductDto GetLicenseProductDto(LicenseProduct lic)
        {
            return new LicenseProductDto()
            {
                Name = lic.Name,
                Id = lic.Id,
                LicenseId = lic.LicenseId,
                ProductId = lic.ProductId,
                Features = [.. lic.Features.Select(f => new LicenseFeatureDto()
                {
                      Enabled = f.Enabled,
                      Name = f.Name,
                      FeatureId = f.FeatureId,
                })]
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lic"></param>
        /// <returns></returns>
        static LicenseProductResponseDto GetLicenseProductResponseDto(LicenseProduct lic)
        {
            return new LicenseProductResponseDto()
            {
                Name = lic.Name,
                Id = lic.Id,
                SourceProduct = lic.ProductId != Guid.Empty && lic.Product is not null ? new ProductDto()
                {
                    Id = lic.Product.Id,
                    Name = lic.Product.Name,
                    Features = [..lic.Product.Features.Select(f => new ProductFeatureDto()
                     {
                          Enabled = f.Enabled,
                          Name = f.Name,
                          FeatureId = f.FeatureId,
                     })]
                } : null,
                Features = [.. lic.Features.Select(f => new LicenseFeatureDto()
                {
                      Enabled = f.Enabled,
                      Name = f.Name,
                      FeatureId = f.FeatureId,
                })],
                License = lic.License is not null ? GetLicenseDto(lic.License) : new LicenseDto(),
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="licenseId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{licenseId}")]
        [EthosAuthScope(ScopeDefinitions.LicenseRead)]
        public async Task<ActionResult<LicenseDto>> GetLicense([FromRoute] Guid licenseId)
        {
            if (licenseId == Guid.Empty)
                return this.EthosErrorBadRequest("Invalid license ID.");

            var tenantId = this.GetTenantIdOrDefault();
            var canReadAllLicenses = HttpContext.User.IsAllowedScope(ScopeDefinitions.LicenseAdministrator);

            var license = await _dbContext.Licenses.FirstOrDefaultAsync(l => l.Id == licenseId && (l.TenantId == tenantId || canReadAllLicenses));

            if (license is null)
                return this.EthosErrorNotFound($"License with ID {licenseId} was not found.");

            return Ok(GetLicenseDto(license));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [EthosAuthScope(ScopeDefinitions.LicenseRead)]
        public async Task<ActionResult<PagedResponse<LicenseDto>>> GetLicenses([FromQuery] PagingParameters pagingParameters, [FromQuery] string? filter = null)
        {
            var isLicenseAdmin = HttpContext.User.IsAllowedScope(ScopeDefinitions.LicenseAdministrator);
            var tenantId = this.GetTenantIdOrDefault();
            var licenses = _dbContext.Licenses.Where(l => l.TenantId == tenantId || isLicenseAdmin).Filter(filter).OrderBy(l => l.TenantId).OrderByDescending(l => l.StartDate);
            return Ok(await licenses.PaginateWithLinksAsync(this, (lics) =>
            {
                return lics.Select(l => GetLicenseDto(l)).ToList();
            }, pagingParameters.limit, pagingParameters.offset));
        }
    }
}
