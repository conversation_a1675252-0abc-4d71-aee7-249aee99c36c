import { IsEnum, IsInt, IsOptional, Min } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { EOrderDirection } from '@app/common/enums';

export class BaseFiltersDto {
    @IsOptional()
    @IsInt()
    @Min(0)
    @ApiPropertyOptional({ default: 0 })
    offset?: number = 0;

    @IsOptional()
    @IsInt()
    @Min(0)
    @ApiPropertyOptional({ default: 20 })
    limit?: number = 20;

    @IsOptional()
    @IsEnum(EOrderDirection)
    @ApiPropertyOptional({ type: 'enum', enum: EOrderDirection, default: EOrderDirection.Desc })
    orderDirection?: EOrderDirection = EOrderDirection.Desc;
}
