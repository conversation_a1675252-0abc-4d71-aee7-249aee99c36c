using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PhysicianDbo : IAuditableEntity<PhysicianDbo>
{
    public virtual ICollection<PersonNameDbo> Names { get; set; } = new List<PersonNameDbo>();
    public DemographicsDbo? Demographics { get; set; }
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    public Guid? ContactDetailId { get; set; }
    public PersonalContactDetailDbo? ContactDetail { get; set; }
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PhysicianDbo>(Register);

    public new static void Register(EntityTypeBuilder<PhysicianDbo> entity)
    {
        IAuditableEntity<PhysicianDbo>.Register(entity);
        
        entity.OwnsMany(p => p.Names, names =>
        {
            names.ToTable($"{nameof(PhysicianDbo)}_{nameof(Names)}", IEntity.DefaultSchema);
            names.WithOwner().HasForeignKey("PhysicianDboId");
            names.Property<int>("Id").ValueGeneratedOnAdd();
            names.HasKey("Id");
            PersonNameDbo.Configure(names);
        });
        
        entity.OwnsOne(p => p.Demographics, builder =>
        {
            builder.ToTable($"{nameof(PhysicianDbo)}_{nameof(Demographics)}", IEntity.DefaultSchema);
            builder.WithOwner().HasForeignKey($"{nameof(PhysicianDbo)}Id");
            DemographicsDbo.Configure(builder);
        });
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(PhysicianDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey("PhysicianDboId");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.HasOne(s => s.ContactDetail)
            .WithMany()
            .HasForeignKey("ContactDetailId")
            .IsRequired(false)
            .HasPrincipalKey(c => c.Id);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(PhysicianQ.WithId), "WithId")]
[JsonDerivedType(typeof(PhysicianQ.WithGivenName), "WithGivenName")]
[JsonDerivedType(typeof(PhysicianQ.WithLastName), "WithLastName")]
[JsonDerivedType(typeof(PhysicianQ.WithApproximateFullName), "WithApproximateFullName")]
[JsonDerivedType(typeof(PhysicianQ.WithIdentifier), "WithIdentifier")]
[JsonDerivedType(typeof(PhysicianQ.WithEmail), "WithEmail")]
[JsonDerivedType(typeof(PhysicianQ.WithPhoneNumber), "WithPhoneNumber")]
[JsonDerivedType(typeof(PhysicianQ.WithBirthSex), "WithBirthSex")]
[JsonDerivedType(typeof(PhysicianQ.WithGender), "WithGender")]
[JsonDerivedType(typeof(PhysicianQ.WithMaritalStatus), "WithMaritalStatus")]
[JsonDerivedType(typeof(PhysicianQ.WithRace), "WithRace")]
[JsonDerivedType(typeof(PhysicianQ.WithEthnicity), "WithEthnicity")]
[JsonDerivedType(typeof(PhysicianQ.WithDateOfBirth), "WithDateOfBirth")]
public abstract record PhysicianQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : PhysicianQ;
    public sealed record WithGivenName(string Value) : PhysicianQ;
    public sealed record WithLastName(string Value) : PhysicianQ;
    public sealed record WithApproximateFullName(string Value) : PhysicianQ;
    public sealed record WithIdentifier(string System, string Value) : PhysicianQ;
    public sealed record WithEmail(string Email) : PhysicianQ;
    public sealed record WithPhoneNumber(string PhoneNumber) : PhysicianQ;
    
    public sealed record WithBirthSex(long? Value) : PhysicianQ;
    public sealed record WithGender(long? Value) : PhysicianQ;
    public sealed record WithMaritalStatus(long? Value) : PhysicianQ;
    public sealed record WithRace(long? Value) : PhysicianQ;
    public sealed record WithEthnicity(long? Value) : PhysicianQ;
    public sealed record WithDateOfBirth(DateOnly? Lower, DateOnly? Upper) : PhysicianQ; // Inclusive range
    
    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(PhysicianDbo.Id)), Expression.Constant(id.Id)),
            WithGivenName       wfn => QueryExpressions.BuildFirstNamePredicate<PhysicianDbo>(wfn.Value, self),
            WithLastName        wln => QueryExpressions.BuildLastNamePredicate<PhysicianDbo>(wln.Value, self),
            WithApproximateFullName wan => QueryExpressions.BuildApproximateNamePredicate<PhysicianDbo>(wan.Value, self),
            WithIdentifier      wnp => QueryExpressions.BuildIdentifierPredicate(wnp.System, wnp.Value, self),
            WithEmail           we => QueryExpressions.HasEmailPredicate<PhysicianDbo>(self, we.Email),
            WithPhoneNumber     wpn => QueryExpressions.HasPhoneNumberPredicate<PhysicianDbo>(self, wpn.PhoneNumber),
            
            WithDateOfBirth withDob => QueryExpressions.BuildWithDateOfBirthPredicate<PhysicianDbo>(self, withDob.Lower, withDob.Upper),
            WithBirthSex        wbs => QueryExpressions.HasDemographicsFieldPredicate<PhysicianDbo>(self, nameof(DemographicsDbo.SexId), wbs.Value),
            WithGender          wg => QueryExpressions.HasDemographicsFieldPredicate<PhysicianDbo>(self, nameof(DemographicsDbo.GenderId), wg.Value),
            WithMaritalStatus   wms => QueryExpressions.HasDemographicsFieldPredicate<PhysicianDbo>(self, nameof(DemographicsDbo.MaritalStatusId), wms.Value),
            WithRace            wr => QueryExpressions.HasDemographicsFieldPredicate<PhysicianDbo>(self, nameof(DemographicsDbo.RaceId), wr.Value),
            WithEthnicity       we => QueryExpressions.HasDemographicsFieldPredicate<PhysicianDbo>(self, nameof(DemographicsDbo.EthnicityId), we.Value),
            
            _ => throw new NotImplementedException()
        };
    }
}