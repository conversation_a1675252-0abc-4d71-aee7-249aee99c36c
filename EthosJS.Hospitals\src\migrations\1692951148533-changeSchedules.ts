import moment from 'moment/moment';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { IScheduleEquipment } from '@app/modules/schedule/types';
import { getWeekDay } from '@app/common/helpers';

export class changeSchedules1692951148533 implements MigrationInterface {
    name = 'changeSchedules1692951148533'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "schedules" ADD "technician_name" character varying');
      await queryRunner.query('ALTER TABLE "schedules" ADD "patient_name" character varying');
      await queryRunner.query('ALTER TABLE "schedules" ADD "study_name" character varying');
      await queryRunner.query('ALTER TABLE "schedules" ADD "facility_name" character varying');
      await queryRunner.query('CREATE TYPE "schedules_weekday_enum" AS ENUM(\'mon\', \'tue\', \'wed\', \'thu\', \'fri\', \'sat\', \'sun\')');
      await queryRunner.query('ALTER TABLE "schedules" ADD "weekday" "schedules_weekday_enum"');
      await queryRunner.query('ALTER TABLE "schedules" ADD "credentials" jsonb NOT NULL DEFAULT \'[]\'');

      const equipments = await queryRunner.query('SELECT * FROM equipments');
      const equipmentsMap = equipments.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      const technicians = await queryRunner.query('SELECT * FROM technicians');
      const techniciansMap = technicians.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      const studies = await queryRunner.query('SELECT * FROM studies');
      const studiesMap = studies.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      const patients = await queryRunner.query('SELECT * FROM patients');
      const patientsMap = patients.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      const facilities = await queryRunner.query('SELECT * FROM facilities RIGHT JOIN cities ON facilities.city_id = cities.id');
      const facilitiesMap = facilities.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      const studyCredentials = await queryRunner.query('SELECT * FROM study_credentials');

      let offset = 0;
      const limit = 100;
      let schedules = await queryRunner.query('SELECT * FROM schedules OFFSET $1 LIMIT $2', [offset, limit]);

      while(schedules.length) {
        for (const schedule of schedules) {
          const equipments = Object.entries(schedule.equipments)
            .map(([equipmentId, item]: [string, any]) => {
              const equipmentName = equipmentsMap[equipmentId]?.name;

              return {
                ...item,
                equipmentId,
                equipmentName,
              };
            })
            .reduce((acc: Record<number, IScheduleEquipment>, item: IScheduleEquipment) => {
              acc[item.equipmentId] = item;

              return acc;
            }, {});

          const studyName = studiesMap[schedule.study_id]?.name;
          const patientName = patientsMap[schedule.patient_id]?.name;
          const technicianName = techniciansMap[schedule.technician_id]?.name;
          const facility = facilitiesMap[schedule.facility_id];
          const facilityName = facility?.name;
          const day = moment(schedule.date).weekday();
          const weekday = getWeekDay(day);

          let studyCredential = studyCredentials.find((item: any) => {
            if (item.state_id === facility.state_id && item.study_id === schedule.study_id) {
              return item;
            }
          });

          if (!studyCredential) {
            studyCredential = studyCredentials.find((item: any) => {
              if (item.study_id === schedule.study_id) {
                return item;
              }
            });
          }

          const credentials = studyCredential?.credentials || [];

          await queryRunner.query('UPDATE schedules SET equipments = $1, study_name = $3, patient_name = $4, technician_name = $5, facility_name = $6, weekday = $7, credentials = $8 WHERE id = $2', [JSON.stringify(equipments), schedule.id, studyName, patientName, technicianName, facilityName, weekday, JSON.stringify(credentials)]);
        }

        offset += limit;
        schedules = await queryRunner.query('SELECT * FROM schedules OFFSET $1 LIMIT $2', [offset, limit]);
      }


      await queryRunner.query('ALTER TABLE "schedules" ALTER COLUMN "technician_name" SET NOT NULL');
      await queryRunner.query('ALTER TABLE "schedules" ALTER COLUMN "patient_name" SET NOT NULL');
      await queryRunner.query('ALTER TABLE "schedules" ALTER COLUMN "study_name" SET NOT NULL');
      await queryRunner.query('ALTER TABLE "schedules" ALTER COLUMN "facility_name" SET NOT NULL');
      await queryRunner.query('ALTER TABLE "schedules" ALTER COLUMN "weekday" SET NOT NULL');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "credentials"');
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "weekday"');
      await queryRunner.query('DROP TYPE "schedules_weekday_enum"');
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "facility_name"');
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "study_name"');
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "patient_name"');
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "technician_name"');
    }

}
