using System.Text.Json;
using Ethos.Model;
using Ethos.Utilities.Pagination;
using Ethos.Workflows.Api;
using Ethos.Workflows.Api.Analysis;
using Ethos.Workflows.Controllers;
using Ethos.Workflows.Database;
using Ethos.Workflows.HttpClient;
using Ethos.Workflows.Workflow;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using Xunit.Abstractions;

namespace Ethos.Workflows.Tests;

public class WorkflowApiIntegrationTests 
    : IClassFixture<CustomWebApplicationFactory<Startup>> // Program is your ASP.NET Core entry point
{
    private readonly CustomWebApplicationFactory<Startup> _factory;
    private readonly ITestOutputHelper _testOutputHelper;
    
    private readonly IPatientApi _patientApi;
    private readonly IProviderApi _providerApi;
    private readonly IInsuranceApi _insuranceApi;
    private readonly IPhysicianApi _physicianApi;
    private readonly ICareLocationApi _careLocationApi;
    private readonly AuthHttpClient _authClient;

    public WorkflowApiIntegrationTests(CustomWebApplicationFactory<Startup> factory, ITestOutputHelper testOutputHelper)
    {
        _factory = factory;
        _testOutputHelper = testOutputHelper;

        // Create an HttpClient for the in-memory server
        System.Net.Http.HttpClient client = _factory.CreateClient();
        _authClient = new AuthHttpClient(client);
        
        // Build our typed client using that HttpClient
        _providerApi = new ProviderHttpClient(client);
        _patientApi = new PatientHttpClient(client);
        _insuranceApi = new InsuranceHttpClient(client);
        _physicianApi = new PhysicianHttpClient(client);
        _careLocationApi = new CareLocationHttpClient(client);
    }

    [Fact]
    public async Task PythonGen_Test()
    {
        var apiModel = ApiAnalyzer.Analyze(typeof(ProviderController).Assembly, s => _testOutputHelper.WriteLine(s));
        
        _testOutputHelper.WriteLine(JsonSerializer.Serialize(apiModel, new JsonSerializerOptions
        {
            WriteIndented = true
        }));
        // return;
        
        var pythonFiles = PythonGen.Build(apiModel);
        _testOutputHelper.WriteLine($"Generated {pythonFiles.Count} Python files.");
        foreach (var (fileName, content) in pythonFiles)
        {
            _testOutputHelper.WriteLine($"File: {fileName}");
            _testOutputHelper.WriteLine(content);
        }
        
        // save the unzipped files
        var pythonTargetDir = "D:\\ws\\persante\\repo\\python_test";
        if (!Directory.Exists(pythonTargetDir))
            Directory.CreateDirectory(pythonTargetDir);

        foreach (var (fileName, content) in pythonFiles)
        {
            var filePath = Path.Combine(pythonTargetDir, fileName);
            _testOutputHelper.WriteLine($"Saving file: {filePath}");
            await File.WriteAllTextAsync(filePath, content);
        }
        
        var typescriptFiles = TypeScriptGen.Build(apiModel);
        // save the unzipped files
        var typescriptTargetDir = "D:\\ws\\persante\\repo\\typescript_test";
        if (!Directory.Exists(typescriptTargetDir))
            Directory.CreateDirectory(typescriptTargetDir);

        foreach (var (fileName, content) in typescriptFiles)
        {
            var filePath = Path.Combine(typescriptTargetDir, fileName);
            _testOutputHelper.WriteLine($"Saving file: {filePath}");
            await File.WriteAllTextAsync(filePath, content);
        }
        
        return;

        // var file = PythonGenerator.GenerateApi(new ()
        // {
        //     typeof(IProviderApi), typeof(ICareLocationApi), typeof(INoteApi), typeof(IPatientApi)
        // });
        // _testOutputHelper.WriteLine(file);
        // var zipBytes = PythonGenerator.BuildZip();
        // _testOutputHelper.WriteLine($"Zip size: {zipBytes.Length} bytes");
        // // Save the zip file to disk for inspection into the .sln directory
        // _testOutputHelper.WriteLine(Directory.GetCurrentDirectory());
        // var zipFilePath = Path.Combine(
        //     Directory.GetCurrentDirectory(), 
        //     "pyapi.zip");
        // await File.WriteAllBytesAsync(zipFilePath, zipBytes);
    }

    [Fact]
    public async Task GetPatient_Test()
    {
        using (var context = _factory.Services.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>())
        {
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();
        }

        var token = await _authClient.LoginAsync("test", "password");
        _patientApi.SetBearerToken(token);

        // using (var testScope = _factory.Services.CreateScope())
        // {
        //     var mockDbController = testScope.ServiceProvider.GetRequiredService<MockDbController>();
        //     await mockDbController.Reset();
        // }
        await new MockDbHttpClient(_authClient.HttpClient).ResetAsync();
        
        // {
        //     "id": "59089408-572d-4b91-a68c-660e2d6b5526",
        //     "name": "Peach Hills Medical Center",
        //     "providerID": "9096f19a-9a12-43fc-a4d3-ca35673f97bc",
        //     "npi": "123456789",
        //     "address": {
        //         "line1": "1st street",
        //         "line2": "2nd",
        //         "city": "Albany",
        //         "state": 3512,
        //         "postalCode": "34333",
        //         "country": 4716
        //     }
        // }
        var provider = new CreateProviderDto
        {
            Name = "Peach Hills Medical Center"
        };
        var providerResult = (await _providerApi.CreateAsync(provider)).Dto!;
        
        var careLocation = new CreateCareLocationDto
        {
            Name = "Peach Hills Medical Center - Main Campus",
            ParentProviderId = providerResult.Id,
            ParentServiceLocationId = null,
            ContactDetail = new OrganizationContactDetailDto()
            {
                Addresses = [],
                ContactPersons = [],
                Emails = [],
                PhoneNumbers = []
            },
            SupportedEncounterTypes = [],
            SupportedStudyTypes = []
        };
        var careLocationResult = await _careLocationApi.CreateAsync(careLocation);
        careLocationResult.Dto.Should().NotBeNull();
        _testOutputHelper.WriteLine(JsonSerializer.Serialize(careLocationResult.Dto));
        
        var careLocations = await _careLocationApi.GetListAsync(new QueryDto<CareLocationQ>.Literal(new CareLocationQ.WithId(careLocationResult.Dto.Id)));
        careLocations.Should().NotBeNull();
        careLocations.Should().NotBeEmpty();
        _testOutputHelper.WriteLine(JsonSerializer.Serialize(careLocations));
        
        var physician = new CreatePhysicianDto
        {
            Names = [new PersonNameDto()
            {
                FirstName = "Bob",
                MiddleName = "The",
                LastName = "Builder",
                Prefix = null,
                Suffix = null
            }],
            Identifiers = new List<IdentifierDto>
            {
                new IdentifierDto
                {
                    System = "urn:oid:2.16.840.1.113883.4.6", // NPI
                    Value = "123456789"
                }
            },
            CareLocationIds = new List<Guid> { careLocationResult.Dto!.Id },
            Demographics = null,
            ContactInformation = null
        };
        var physicianResult = await _physicianApi.CreateAsync(physician);
        physicianResult.Dto.Should().NotBeNull();
        _testOutputHelper.WriteLine(JsonSerializer.Serialize(physicianResult.Dto));
        
        var request = new QueryDto<PatientQ>.Literal(new PatientQ.WithGivenName("John"));
        var result = await _patientApi.GetListAsync(request);
        
        result.Should().NotBeNull();
        _testOutputHelper.WriteLine(JsonSerializer.Serialize(result));
        // result.Count.Should().BeGreaterThan(0);
        
        var physicians = await _physicianApi.GetListAsync(new QueryDto<PhysicianQ>.Literal(new PhysicianQ.WithApproximateFullName("bob")));
        physicians.Should().NotBeNull();
        _testOutputHelper.WriteLine(JsonSerializer.Serialize(physicians));
        
        var insurances1 = await _insuranceApi.GetListAsync(new QueryDto<InsuranceQ>.Literal(new InsuranceQ.WithPatientId(Guid.NewGuid())));
        insurances1.Should().NotBeNull();
        _testOutputHelper.WriteLine(JsonSerializer.Serialize(insurances1));
    }

    private record TestState(int value);
    private WorkflowName<TestState> _testStateName = new WorkflowName<TestState>("MagicState");
    
    // null -(T1)-> S1 -(T2)-> S2
    
    [Fact]
    public async Task Workflow_Test()
    {
        
        using (var context = _factory.Services.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>())
        {
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();
        }
        
        using (var scope = _factory.Services.CreateScope())
        {
            var workflowEngine = scope.ServiceProvider.GetRequiredService<IWorkflowEngine>();

            var workflows = await workflowEngine.GetInstancesAsync(_testStateName);
            workflows.Should().BeEmpty();

            var patientGuid = Guid.NewGuid();
            var guid = await workflowEngine.StartNewWorkflowAsync(_testStateName, new Dictionary<string, Guid>
            {
                ["Patient"] = patientGuid
             });
            var state = await workflowEngine.GetInstanceAsync(_testStateName, guid);
            state.Should().NotBeNull();
            state.Id.Should().Be(guid);
            state.StateData.Should().BeNull();
            state.PastTransitions.Should().BeEmpty();
            state.DraftTransitions.Should().BeEmpty();
            state.ErrorJson.Should().BeNull();
            
            workflows = await workflowEngine.GetInstancesAsync(_testStateName);
            workflows.Should().NotBeEmpty();
            
            workflows = await workflowEngine.GetInstancesAsync(_testStateName, new Dictionary<string, Guid>
            {
                ["Patient"] = patientGuid
            });
            workflows.Should().NotBeEmpty();
            
            await workflowEngine.UpdateInstanceAsync<TestState>(
                name: _testStateName, 
                instanceId: guid, 
                transitionName: new WorkflowTransitionName<int>("T1"), 
                transitionData: "{}", 
                newStateName: new WorkflowStateName<object>("S1"),
                instance: new TestState(42));
            
            state = await workflowEngine.GetInstanceAsync(_testStateName, guid);
            state.Should().NotBeNull();
            state.Id.Should().Be(guid);
            state.StateData.Should().NotBeNull();
            state.StateData!.value.Should().Be(42);
            state.PastTransitions.Should().NotBeEmpty();
            state.PastTransitions.Count.Should().Be(1);
            state.PastTransitions[0].Key.Should().Be("T1");
            // state.PastTransitions[0].Data.Should().B(new JsonObject());
            state.DraftTransitions.Should().BeEmpty();
            
            await workflowEngine.UpdateInstanceAsync<TestState>(
                name: _testStateName, 
                instanceId: guid, 
                transitionName: new WorkflowTransitionName<int>("T2"), 
                transitionData: "{\"foo\": \"bar\"}", 
                newStateName: new WorkflowStateName<object>("S2"),
                instance: new TestState(43));
            
            state = await workflowEngine.GetInstanceAsync(_testStateName, guid);
            state.Should().NotBeNull();
            state.Id.Should().Be(guid);
            state.StateData.Should().NotBeNull();
            state.StateData!.value.Should().Be(43);
            state.PastTransitions.Should().NotBeEmpty();
            state.PastTransitions.Count.Should().Be(2);
            state.PastTransitions[0].Key.Should().Be("T1");
            // state.PastTransitions[0].Data.Should().Be("{}");
            state.PastTransitions[1].Key.Should().Be("T2");
            // state.PastTransitions[1].Data.Should().Be("{\"foo\": \"bar\"}");
            state.DraftTransitions.Should().BeEmpty();
            
            await workflowEngine.RewindState(_testStateName, guid, "S1");
            state = await workflowEngine.GetInstanceAsync(_testStateName, guid);
            state.Should().NotBeNull();
            state.Id.Should().Be(guid);
            state.StateData.Should().NotBeNull();
            state.StateData!.value.Should().Be(42);
            state.PastTransitions.Should().NotBeEmpty();
            state.PastTransitions.Count.Should().Be(1);
            state.PastTransitions[0].Key.Should().Be("T1");
            // state.PastTransitions[0].Data.Should().Be("{}");
            state.DraftTransitions.Should().NotBeEmpty();
            state.DraftTransitions.Count.Should().Be(1);
            state.DraftTransitions[0].Key.Should().Be("T2");
            // state.DraftTransitions[0].Data.Should().Be("{\"foo\":\"bar\"}");
            
            await workflowEngine.RewindState(_testStateName, guid, null);
            state = await workflowEngine.GetInstanceAsync(_testStateName, guid);
            state.Should().NotBeNull();
            state.Id.Should().Be(guid);
            state.StateData.Should().BeNull();
            state.PastTransitions.Should().BeEmpty();
            state.DraftTransitions.Should().NotBeEmpty();
            state.DraftTransitions.Count.Should().Be(2);
            // state.DraftTransitions[0].Key.Should().Be("T2");
            // state.DraftTransitions[0].Data.Should().Be("{\"foo\":\"bar\"}");
            // state.DraftTransitions[1].Key.Should().Be("T1");
            // state.DraftTransitions[1].Data.Should().Be("{}");
        }
    }
}