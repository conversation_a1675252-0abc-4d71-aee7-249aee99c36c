﻿// <auto-generated />
using System;
using Ethos.Workflows.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Ethos.Workflows.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("ethos")
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Ethos.Model.AddressDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<long>("CountryId")
                        .HasColumnType("bigint");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Line1")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Line2")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("StateId")
                        .HasColumnType("bigint");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("AddressDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.AuditLogDbo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<string>("NewState")
                        .HasColumnType("text");

                    b.Property<string>("OldState")
                        .HasColumnType("text");

                    b.Property<DateTime>("Timestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("TransitionName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("AuditLogs", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.CareLocationDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<string>("CareLocationPath")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("ContactDetailId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("ParentCareLocationId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ParentProviderId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.PrimitiveCollection<long[]>("SupportedEncounterTypes")
                        .IsRequired()
                        .HasColumnType("bigint[]");

                    b.PrimitiveCollection<long[]>("SupportedStudyTypes")
                        .IsRequired()
                        .HasColumnType("bigint[]");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationPath")
                        .HasDatabaseName("IX_CareLocationDbo_Path");

                    b.HasIndex("ContactDetailId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_CareLocationDbo_Name");

                    b.HasIndex("ParentCareLocationId");

                    b.HasIndex("ParentProviderId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("CareLocationDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.CareLocationShiftDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CareLocationId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<TimeOnly>("FromTime")
                        .HasColumnType("time without time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("State")
                        .HasColumnType("integer");

                    b.Property<TimeOnly>("ToTime")
                        .HasColumnType("time without time zone");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("CareLocationShiftDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.DraftDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Data")
                        .HasColumnType("jsonb");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("EntityId");

                    b.HasIndex("UpdateEventId");

                    b.HasIndex("EntityType", "EntityId")
                        .IsUnique();

                    b.ToTable("DraftDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.EditRecordDbo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("Id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ChangedColumns")
                        .HasColumnType("jsonb");

                    b.Property<string>("EditType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EntityName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("NewValues")
                        .HasColumnType("jsonb");

                    b.Property<string>("OldValues")
                        .HasColumnType("jsonb");

                    b.Property<string>("PrimaryKey")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("EntityName");

                    b.ToTable("EditRecord", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.EquipmentDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid?>("CareLocationDboId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CareLocationId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("EquipmentData")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<long>("EquipmentTypeId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("RoomDboId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RoomId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationDboId");

                    b.HasIndex("CareLocationId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("RoomDboId");

                    b.HasIndex("RoomId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("EquipmentDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.FileMetadataDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ContextEntityId")
                        .HasColumnType("uuid");

                    b.Property<int>("ContextEntityType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Failure")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MimeType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ProcessingCompletedTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ThumbnailPath")
                        .HasColumnType("text");

                    b.Property<string>("UploadFileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("UploadFileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("UploadMimeType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UploadTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("FileMetadataDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.InsuranceDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("GroupNumber")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<long?>("InsuranceCarrier")
                        .HasColumnType("bigint");

                    b.Property<string>("InsuranceId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("MemberId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid>("PatientId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("PhoneNumberId")
                        .HasColumnType("uuid");

                    b.Property<string>("PolicyId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("PatientId");

                    b.HasIndex("PhoneNumberId")
                        .IsUnique();

                    b.HasIndex("UpdateEventId");

                    b.ToTable("InsuranceDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.InsuranceHolderDataDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("InsuranceHolderDataDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.InsuranceVerificationDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CoarseStateDiscriminator")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CoarseStateMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool?>("CoarseStateSucceeded")
                        .HasColumnType("boolean");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExternalActionTimeoutTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastCoarseStateUpdate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ProcessingServiceInstanceId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ProcessingTimeoutTimestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uuid");

                    b.Property<int>("State")
                        .HasColumnType("integer");

                    b.Property<string>("StateJson")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("StateName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("StudyId")
                        .HasColumnType("uuid");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("WaitingUntil")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CoarseStateDiscriminator")
                        .HasDatabaseName("IX_InsuranceVerification_CoarseState");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("ExternalActionTimeoutTimestamp")
                        .HasDatabaseName("IX_InsuranceVerification_ExtActionTimeout");

                    b.HasIndex("StateName")
                        .HasDatabaseName("IX_InsuranceVerification_StateName");

                    b.HasIndex("StudyId")
                        .HasDatabaseName("IX_InsuranceVerification_StudyId");

                    b.HasIndex("UpdateEventId");

                    b.HasIndex("WaitingUntil")
                        .HasDatabaseName("IX_InsuranceVerification_WaitingUntil");

                    b.HasIndex("ProcessingServiceInstanceId", "ProcessingTimeoutTimestamp")
                        .HasDatabaseName("IX_InsuranceVerification_ProcessingStatus");

                    b.ToTable("InsuranceVerificationDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.NoteDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("NoteDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.OrderDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("CareLocationId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("InterpretingPhysicianId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PatientId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("PrimaryCarePhysicianId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ReferringPhysicianId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("InterpretingPhysicianId");

                    b.HasIndex("PatientId");

                    b.HasIndex("PrimaryCarePhysicianId");

                    b.HasIndex("ReferringPhysicianId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("OrderDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationAddressDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("TypeId")
                        .HasColumnType("bigint");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.Property<long>("UseId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("OrganizationAddressDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationContactDetailDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("OrganizationContactDetailDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationContactPersonDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ContactDetailId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<int>("State")
                        .HasColumnType("integer");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ContactDetailId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("OrganizationContactPersonDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationEmailDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.HasIndex("ParentId", "Email")
                        .IsUnique();

                    b.ToTable("OrganizationEmailDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationPhoneNumberDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<bool?>("AllowsSMS")
                        .HasColumnType("boolean");

                    b.Property<bool?>("AllowsVoice")
                        .HasColumnType("boolean");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Use")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("OrganizationPhoneNumberDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PatientAppointmentConfirmationDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<DateTime>("AddedDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ConfirmationDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("ConfirmationTypeId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ConfirmedById")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("PatientAppointmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("PatientAppointmentId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PatientAppointmentConfirmationDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PatientAppointmentDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("CareLocationShiftId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<Guid>("RoomId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ScheduledById")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("StudyId")
                        .HasColumnType("uuid");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationShiftId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("RoomId");

                    b.HasIndex("StudyId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PatientAppointmentDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PatientDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.PrimitiveCollection<long[]>("ClinicalConsiderations")
                        .IsRequired()
                        .HasColumnType("bigint[]");

                    b.Property<Guid?>("ContactDetailId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.PrimitiveCollection<long[]>("PreferredWeekdays")
                        .IsRequired()
                        .HasColumnType("bigint[]");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("TechnicianPreference")
                        .HasColumnType("bigint");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ContactDetailId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PatientDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PatientGuardianDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid?>("ContactDetailId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("PatientDboId")
                        .HasColumnType("uuid");

                    b.Property<long?>("RelationshipToPatient")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ContactDetailId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("PatientDboId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PatientGuardianDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PersonalAddressDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("TypeId")
                        .HasColumnType("bigint");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.Property<long>("UseId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PersonalAddressDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PersonalContactDetailDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PersonalContactDetailDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PersonalEmailDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)");

                    b.Property<bool>("IsPreferred")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.Property<long>("Use")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.HasIndex("ParentId", "Email")
                        .IsUnique();

                    b.ToTable("PersonalEmailDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PersonalEmergencyContactDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<string>("ContactInformation")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<long>("RelationshipId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PersonalEmergencyContactDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PersonalPhoneNumberDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<bool>("AllowsTextMessages")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowsVoice")
                        .HasColumnType("boolean");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsPreferred")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<long>("PreferredTimeId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Use")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PersonalPhoneNumberDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PhoneNumberWithUseDataDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<long>("PhoneNumberTypeId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PhoneNumberWithUseDataDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PhysicianCareLocationRelationDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("CareLocationId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("PhysicianId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("PhysicianId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PhysicianCareLocationRelationDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.PhysicianDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid?>("ContactDetailId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ContactDetailId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("PhysicianDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.ProviderDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid?>("ContactDetailId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("ParentProviderId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProviderPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ContactDetailId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_ProviderDbo_Name");

                    b.HasIndex("ParentProviderId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("ProviderDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.RoomDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("CareLocationId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.PrimitiveCollection<long[]>("SupportedStudyTypes")
                        .HasColumnType("bigint[]");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_RoomDbo_Name");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("RoomDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.Scheduling.SchedulingConstraintDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Expression")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsHardConstraint")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TopLevelVariables")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("SchedulingConstraintDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.StudyDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<long?>("EncounterType")
                        .HasColumnType("bigint");

                    b.Property<string>("Interpreting")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Referring")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ScoredDate")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("StudyAttributes")
                        .HasColumnType("jsonb");

                    b.Property<long?>("StudyType")
                        .HasColumnType("bigint");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("OrderId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("StudyDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianAppointmentDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("CareLocationShiftId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<Guid>("RoomId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ScheduledById")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("StudyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TechnicianId")
                        .HasColumnType("uuid");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationShiftId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("RoomId");

                    b.HasIndex("StudyId");

                    b.HasIndex("TechnicianId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("TechnicianAppointmentDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianCareLocationRelationDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid>("CareLocationId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TechnicianId")
                        .HasColumnType("uuid");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CareLocationId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("TechnicianId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("TechnicianCareLocationRelationDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<Guid?>("ContactDetailId")
                        .HasColumnType("uuid");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ContactDetailId");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("TechnicianDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianRoleDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TechnicianId")
                        .HasColumnType("uuid");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("TechnicianId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("TechnicianRoleDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianShiftPreferenceDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<long>("CreateEventId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ShiftId")
                        .HasColumnType("uuid");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TechnicianId")
                        .HasColumnType("uuid");

                    b.Property<long?>("UpdateEventId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreateEventId");

                    b.HasIndex("TechnicianId");

                    b.HasIndex("UpdateEventId");

                    b.ToTable("TechnicianShiftPreferenceDbo", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.WorkflowDraftTransitionDbo", b =>
                {
                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransitionName")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("TransitionData")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.HasKey("WorkflowInstanceId", "TransitionName");

                    b.ToTable("WorkflowDraftTransitions", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.WorkflowEntityLinkDbo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("InstanceId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("InstanceId")
                        .HasDatabaseName("IX_WorkflowEntityLinkDbo_InstanceId");

                    b.HasIndex("EntityType", "EntityId")
                        .HasDatabaseName("IX_WorkflowEntityLinkDbo_EntityType_EntityId");

                    b.ToTable("WorkflowEntityContexts", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.WorkflowInstanceDbo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AbortedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("CurrentSequence")
                        .HasColumnType("integer");

                    b.Property<string>("CurrentState")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransientErrorJson")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("WorkflowKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("WorkflowInstances", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.WorkflowTransitionDbo", b =>
                {
                    b.Property<Guid>("WorkflowInstanceId")
                        .HasColumnType("uuid");

                    b.Property<int>("SequenceId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("NewStateData")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("NewStateName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransitionData")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransitionName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.HasKey("WorkflowInstanceId", "SequenceId");

                    b.ToTable("WorkflowInstanceTransitions", "ethos");
                });

            modelBuilder.Entity("Ethos.Workflows.Database.ApplicationUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.HasKey("Id");

                    b.HasIndex("ExternalId")
                        .IsUnique();

                    b.ToTable("Users", "ethos");
                });

            modelBuilder.Entity("Ethos.Workflows.Database.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("EscalationLevel")
                        .HasColumnType("integer");

                    b.Property<bool>("IsHighUrgency")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("Timestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UniqueKey")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("UniqueKey")
                        .IsUnique();

                    b.ToTable("Notifications", "ethos");
                });

            modelBuilder.Entity("Ethos.Workflows.Database.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.HasKey("Id");

                    b.ToTable("Roles", "ethos");
                });

            modelBuilder.Entity("Ethos.Workflows.Database.UserNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<bool>("IsRead")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("NotificationId");

                    b.ToTable("UserNotifications", "ethos");
                });

            modelBuilder.Entity("InsuranceDboStudyDbo", b =>
                {
                    b.Property<Guid>("InsurancesId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("StudyDboId")
                        .HasColumnType("uuid");

                    b.HasKey("InsurancesId", "StudyDboId");

                    b.HasIndex("StudyDboId");

                    b.ToTable("InsuranceDboStudyDbo", "ethos");
                });

            modelBuilder.Entity("PatientAppointmentDboStudyDbo", b =>
                {
                    b.Property<Guid>("AppointmentsId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("StudyDboId")
                        .HasColumnType("uuid");

                    b.HasKey("AppointmentsId", "StudyDboId");

                    b.HasIndex("StudyDboId");

                    b.ToTable("PatientAppointmentDboStudyDbo", "ethos");
                });

            modelBuilder.Entity("UserRoles", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("RoleId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("UserRoles", "ethos");
                });

            modelBuilder.Entity("Ethos.Model.AddressDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.CareLocationDbo", b =>
                {
                    b.HasOne("Ethos.Model.OrganizationContactDetailDbo", "ContactDetail")
                        .WithMany()
                        .HasForeignKey("ContactDetailId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.CareLocationDbo", "ParentCareLocation")
                        .WithMany()
                        .HasForeignKey("ParentCareLocationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.ProviderDbo", "ParentProvider")
                        .WithMany()
                        .HasForeignKey("ParentProviderId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsMany("Ethos.Model.IdentifierDbo", "Identifiers", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("CareLocationDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("System")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("Id");

                            b1.HasIndex("CareLocationDboId");

                            b1.ToTable("CareLocationDbo_Identifiers", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("CareLocationDboId");
                        });

                    b.Navigation("ContactDetail");

                    b.Navigation("CreateEvent");

                    b.Navigation("Identifiers");

                    b.Navigation("ParentCareLocation");

                    b.Navigation("ParentProvider");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.CareLocationShiftDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationDbo", "CareLocation")
                        .WithMany()
                        .HasForeignKey("CareLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CareLocation");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.DraftDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.EquipmentDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationDbo", null)
                        .WithMany("Equipment")
                        .HasForeignKey("CareLocationDboId");

                    b.HasOne("Ethos.Model.CareLocationDbo", "CareLocation")
                        .WithMany()
                        .HasForeignKey("CareLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.RoomDbo", null)
                        .WithMany("Equipment")
                        .HasForeignKey("RoomDboId");

                    b.HasOne("Ethos.Model.RoomDbo", "Room")
                        .WithMany()
                        .HasForeignKey("RoomId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsMany("Ethos.Model.IdentifierDbo", "Identifiers", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("EquipmentDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("System")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("Id");

                            b1.HasIndex("EquipmentDboId");

                            b1.ToTable("EquipmentDbo_Identifiers", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("EquipmentDboId");
                        });

                    b.Navigation("CareLocation");

                    b.Navigation("CreateEvent");

                    b.Navigation("Identifiers");

                    b.Navigation("Room");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.InsuranceDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.InsuranceHolderDataDbo", "InsuranceHolder")
                        .WithOne()
                        .HasForeignKey("Ethos.Model.InsuranceDbo", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PatientDbo", "Patient")
                        .WithMany("Insurances")
                        .HasForeignKey("PatientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PhoneNumberWithUseDataDbo", "PhoneNumber")
                        .WithOne()
                        .HasForeignKey("Ethos.Model.InsuranceDbo", "PhoneNumberId");

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("InsuranceHolder");

                    b.Navigation("Patient");

                    b.Navigation("PhoneNumber");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.InsuranceHolderDataDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.InsuranceVerificationDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.StudyDbo", "Study")
                        .WithMany()
                        .HasForeignKey("StudyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Study");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.NoteDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.OrderDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationDbo", "CareLocation")
                        .WithMany()
                        .HasForeignKey("CareLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PhysicianDbo", "InterpretingPhysician")
                        .WithMany()
                        .HasForeignKey("InterpretingPhysicianId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.PatientDbo", "Patient")
                        .WithMany("Orders")
                        .HasForeignKey("PatientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PhysicianDbo", "PrimaryCarePhysician")
                        .WithMany()
                        .HasForeignKey("PrimaryCarePhysicianId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.PhysicianDbo", "ReferringPhysician")
                        .WithMany()
                        .HasForeignKey("ReferringPhysicianId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CareLocation");

                    b.Navigation("CreateEvent");

                    b.Navigation("InterpretingPhysician");

                    b.Navigation("Patient");

                    b.Navigation("PrimaryCarePhysician");

                    b.Navigation("ReferringPhysician");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationAddressDbo", b =>
                {
                    b.HasOne("Ethos.Model.AddressDbo", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.OrganizationContactDetailDbo", "Parent")
                        .WithMany("Addresses")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("Address");

                    b.Navigation("CreateEvent");

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationContactDetailDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationContactPersonDbo", b =>
                {
                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "ContactDetail")
                        .WithMany()
                        .HasForeignKey("ContactDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.OrganizationContactDetailDbo", "Parent")
                        .WithMany("ContactPersons")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsOne("Ethos.Model.PersonNameDbo", "Name", b1 =>
                        {
                            b1.Property<Guid>("OrganizationContactPersonDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("FirstName")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.Property<string>("LastName")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.Property<string>("MiddleName")
                                .HasColumnType("text");

                            b1.Property<long?>("PrefixId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SuffixId")
                                .HasColumnType("bigint");

                            b1.HasKey("OrganizationContactPersonDboId");

                            b1.ToTable("OrganizationContactPersonDbo", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("OrganizationContactPersonDboId");
                        });

                    b.Navigation("ContactDetail");

                    b.Navigation("CreateEvent");

                    b.Navigation("Name")
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationEmailDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.OrganizationContactDetailDbo", "Parent")
                        .WithMany("Emails")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationPhoneNumberDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.OrganizationContactDetailDbo", "Parent")
                        .WithMany("PhoneNumbers")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PatientAppointmentConfirmationDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PatientAppointmentDbo", "PatientAppointment")
                        .WithMany()
                        .HasForeignKey("PatientAppointmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("PatientAppointment");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PatientAppointmentDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationShiftDbo", "CareLocationShift")
                        .WithMany()
                        .HasForeignKey("CareLocationShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.RoomDbo", "Room")
                        .WithMany()
                        .HasForeignKey("RoomId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.StudyDbo", "Study")
                        .WithMany()
                        .HasForeignKey("StudyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CareLocationShift");

                    b.Navigation("CreateEvent");

                    b.Navigation("Room");

                    b.Navigation("Study");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PatientDbo", b =>
                {
                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "ContactDetail")
                        .WithMany()
                        .HasForeignKey("ContactDetailId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsOne("Ethos.Model.DemographicsDbo", "Demographics", b1 =>
                        {
                            b1.Property<Guid>("PatientDboId")
                                .HasColumnType("uuid");

                            b1.Property<DateOnly?>("DateOfBirth")
                                .HasColumnType("date");

                            b1.Property<long?>("EthnicityId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("GenderId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("MaritalStatusId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("RaceId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SexId")
                                .HasColumnType("bigint");

                            b1.HasKey("PatientDboId");

                            b1.ToTable("PatientDbo_Demographics", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PatientDboId");
                        });

                    b.OwnsMany("Ethos.Model.IdentifierDbo", "Identifiers", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("PatientDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("System")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("Id");

                            b1.HasIndex("PatientDboId");

                            b1.ToTable("PatientDbo_Identifiers", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PatientDboId");
                        });

                    b.OwnsMany("Ethos.Model.PersonNameDbo", "Names", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("FirstName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("LastName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("MiddleName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<Guid>("PatientDboId")
                                .HasColumnType("uuid");

                            b1.Property<long?>("PrefixId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SuffixId")
                                .HasColumnType("bigint");

                            b1.HasKey("Id");

                            b1.HasIndex("PatientDboId");

                            b1.ToTable("PatientDbo_Names", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PatientDboId");
                        });

                    b.OwnsOne("Ethos.Model.PhysicalMeasurementsDbo", "PhysicalMeasurements", b1 =>
                        {
                            b1.Property<Guid>("PatientDboId")
                                .HasColumnType("uuid");

                            b1.Property<decimal?>("Bmi")
                                .HasPrecision(4, 2)
                                .HasColumnType("numeric(4,2)");

                            b1.Property<decimal?>("HeightInches")
                                .HasPrecision(5, 2)
                                .HasColumnType("numeric(5,2)");

                            b1.Property<decimal?>("NeckSize")
                                .HasPrecision(4, 2)
                                .HasColumnType("numeric(4,2)");

                            b1.Property<decimal?>("WeightPounds")
                                .HasPrecision(6, 2)
                                .HasColumnType("numeric(6,2)");

                            b1.HasKey("PatientDboId");

                            b1.ToTable("PatientDbo", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PatientDboId");
                        });

                    b.Navigation("ContactDetail");

                    b.Navigation("CreateEvent");

                    b.Navigation("Demographics");

                    b.Navigation("Identifiers");

                    b.Navigation("Names");

                    b.Navigation("PhysicalMeasurements");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PatientGuardianDbo", b =>
                {
                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "ContactDetail")
                        .WithMany()
                        .HasForeignKey("ContactDetailId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PatientDbo", null)
                        .WithMany("Guardians")
                        .HasForeignKey("PatientDboId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsOne("Ethos.Model.DemographicsDbo", "Demographics", b1 =>
                        {
                            b1.Property<Guid>("PatientGuardianDboId")
                                .HasColumnType("uuid");

                            b1.Property<DateOnly?>("DateOfBirth")
                                .HasColumnType("date");

                            b1.Property<long?>("EthnicityId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("GenderId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("MaritalStatusId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("RaceId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SexId")
                                .HasColumnType("bigint");

                            b1.HasKey("PatientGuardianDboId");

                            b1.ToTable("PatientGuardianDbo_Demographics", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PatientGuardianDboId");
                        });

                    b.OwnsMany("Ethos.Model.IdentifierDbo", "Identifiers", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("PatientGuardianDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("System")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("Id");

                            b1.HasIndex("PatientGuardianDboId");

                            b1.ToTable("PatientGuardianDbo_Identifiers", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PatientGuardianDboId");
                        });

                    b.OwnsMany("Ethos.Model.PersonNameDbo", "Names", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("FirstName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("LastName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("MiddleName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<Guid>("PatientGuardianDboId")
                                .HasColumnType("uuid");

                            b1.Property<long?>("PrefixId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SuffixId")
                                .HasColumnType("bigint");

                            b1.HasKey("Id");

                            b1.HasIndex("PatientGuardianDboId");

                            b1.ToTable("PatientGuardianDbo_Names", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PatientGuardianDboId");
                        });

                    b.Navigation("ContactDetail");

                    b.Navigation("CreateEvent");

                    b.Navigation("Demographics");

                    b.Navigation("Identifiers");

                    b.Navigation("Names");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PersonalAddressDbo", b =>
                {
                    b.HasOne("Ethos.Model.AddressDbo", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "Parent")
                        .WithMany("Addresses")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("Address");

                    b.Navigation("CreateEvent");

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PersonalContactDetailDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PersonalEmailDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "Parent")
                        .WithMany("Emails")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PersonalEmergencyContactDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "Parent")
                        .WithMany("EmergencyContacts")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsOne("Ethos.Model.PersonNameDbo", "Name", b1 =>
                        {
                            b1.Property<Guid>("PersonalEmergencyContactDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("FirstName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("LastName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("MiddleName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<long?>("PrefixId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SuffixId")
                                .HasColumnType("bigint");

                            b1.HasKey("PersonalEmergencyContactDboId");

                            b1.ToTable("PersonalEmergencyContactDbo", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PersonalEmergencyContactDboId");
                        });

                    b.Navigation("CreateEvent");

                    b.Navigation("Name")
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PersonalPhoneNumberDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "Parent")
                        .WithMany("PhoneNumbers")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Parent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PhoneNumberWithUseDataDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PhysicianCareLocationRelationDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationDbo", "CareLocation")
                        .WithMany()
                        .HasForeignKey("CareLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.PhysicianDbo", "Physician")
                        .WithMany()
                        .HasForeignKey("PhysicianId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CareLocation");

                    b.Navigation("CreateEvent");

                    b.Navigation("Physician");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.PhysicianDbo", b =>
                {
                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "ContactDetail")
                        .WithMany()
                        .HasForeignKey("ContactDetailId");

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsOne("Ethos.Model.DemographicsDbo", "Demographics", b1 =>
                        {
                            b1.Property<Guid>("PhysicianDboId")
                                .HasColumnType("uuid");

                            b1.Property<DateOnly?>("DateOfBirth")
                                .HasColumnType("date");

                            b1.Property<long?>("EthnicityId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("GenderId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("MaritalStatusId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("RaceId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SexId")
                                .HasColumnType("bigint");

                            b1.HasKey("PhysicianDboId");

                            b1.ToTable("PhysicianDbo_Demographics", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PhysicianDboId");
                        });

                    b.OwnsMany("Ethos.Model.IdentifierDbo", "Identifiers", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("PhysicianDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("System")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("Id");

                            b1.HasIndex("PhysicianDboId");

                            b1.ToTable("PhysicianDbo_Identifiers", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PhysicianDboId");
                        });

                    b.OwnsMany("Ethos.Model.PersonNameDbo", "Names", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("FirstName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("LastName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("MiddleName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<Guid>("PhysicianDboId")
                                .HasColumnType("uuid");

                            b1.Property<long?>("PrefixId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SuffixId")
                                .HasColumnType("bigint");

                            b1.HasKey("Id");

                            b1.HasIndex("PhysicianDboId");

                            b1.ToTable("PhysicianDbo_Names", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("PhysicianDboId");
                        });

                    b.Navigation("ContactDetail");

                    b.Navigation("CreateEvent");

                    b.Navigation("Demographics");

                    b.Navigation("Identifiers");

                    b.Navigation("Names");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.ProviderDbo", b =>
                {
                    b.HasOne("Ethos.Model.OrganizationContactDetailDbo", "ContactDetail")
                        .WithMany()
                        .HasForeignKey("ContactDetailId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.ProviderDbo", "ParentProvider")
                        .WithMany()
                        .HasForeignKey("ParentProviderId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsMany("Ethos.Model.IdentifierDbo", "Identifiers", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("ProviderDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("System")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("Id");

                            b1.HasIndex("ProviderDboId");

                            b1.ToTable("ProviderDbo_Identifiers", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("ProviderDboId");
                        });

                    b.Navigation("ContactDetail");

                    b.Navigation("CreateEvent");

                    b.Navigation("Identifiers");

                    b.Navigation("ParentProvider");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.RoomDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationDbo", "CareLocation")
                        .WithMany()
                        .HasForeignKey("CareLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CareLocation");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.Scheduling.SchedulingConstraintDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.StudyDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.OrderDbo", "Order")
                        .WithMany("Studies")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Order");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianAppointmentDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationShiftDbo", "CareLocationShift")
                        .WithMany()
                        .HasForeignKey("CareLocationShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.RoomDbo", "Room")
                        .WithMany()
                        .HasForeignKey("RoomId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.StudyDbo", "Study")
                        .WithMany()
                        .HasForeignKey("StudyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.TechnicianDbo", "Technician")
                        .WithMany()
                        .HasForeignKey("TechnicianId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CareLocationShift");

                    b.Navigation("CreateEvent");

                    b.Navigation("Room");

                    b.Navigation("Study");

                    b.Navigation("Technician");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianCareLocationRelationDbo", b =>
                {
                    b.HasOne("Ethos.Model.CareLocationDbo", "CareLocation")
                        .WithMany()
                        .HasForeignKey("CareLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.TechnicianDbo", "Technician")
                        .WithMany()
                        .HasForeignKey("TechnicianId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CareLocation");

                    b.Navigation("CreateEvent");

                    b.Navigation("Technician");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianDbo", b =>
                {
                    b.HasOne("Ethos.Model.PersonalContactDetailDbo", "ContactDetail")
                        .WithMany()
                        .HasForeignKey("ContactDetailId");

                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.OwnsOne("Ethos.Model.DemographicsDbo", "Demographics", b1 =>
                        {
                            b1.Property<Guid>("TechnicianDboId")
                                .HasColumnType("uuid");

                            b1.Property<DateOnly?>("DateOfBirth")
                                .HasColumnType("date");

                            b1.Property<long?>("EthnicityId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("GenderId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("MaritalStatusId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("RaceId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SexId")
                                .HasColumnType("bigint");

                            b1.HasKey("TechnicianDboId");

                            b1.ToTable("TechnicianDbo_Demographics", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("TechnicianDboId");
                        });

                    b.OwnsMany("Ethos.Model.IdentifierDbo", "Identifiers", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("System")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<Guid>("TechnicianDboId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("Id");

                            b1.HasIndex("TechnicianDboId");

                            b1.ToTable("TechnicianDbo_Identifiers", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("TechnicianDboId");
                        });

                    b.OwnsMany("Ethos.Model.PersonNameDbo", "Names", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("FirstName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("LastName")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("MiddleName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<long?>("PrefixId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("SuffixId")
                                .HasColumnType("bigint");

                            b1.Property<Guid>("TechnicianDboId")
                                .HasColumnType("uuid");

                            b1.HasKey("Id");

                            b1.HasIndex("TechnicianDboId");

                            b1.ToTable("TechnicianDbo_Names", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("TechnicianDboId");
                        });

                    b.OwnsMany("Ethos.Model.TechnicianQualificationDbo", "Qualifications", b1 =>
                        {
                            b1.Property<Guid>("TechnicianDboId")
                                .HasColumnType("uuid");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<DateOnly?>("DateExpires")
                                .HasColumnType("date");

                            b1.Property<DateOnly?>("DateObtained")
                                .HasColumnType("date");

                            b1.Property<long>("QualificationId")
                                .HasColumnType("bigint");

                            b1.HasKey("TechnicianDboId", "Id");

                            b1.ToTable("TechnicianDbo_Qualifications", "ethos");

                            b1.WithOwner()
                                .HasForeignKey("TechnicianDboId");
                        });

                    b.Navigation("ContactDetail");

                    b.Navigation("CreateEvent");

                    b.Navigation("Demographics");

                    b.Navigation("Identifiers");

                    b.Navigation("Names");

                    b.Navigation("Qualifications");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianRoleDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.TechnicianDbo", "Technician")
                        .WithMany()
                        .HasForeignKey("TechnicianId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Technician");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.TechnicianShiftPreferenceDbo", b =>
                {
                    b.HasOne("Ethos.Model.EditRecordDbo", "CreateEvent")
                        .WithMany()
                        .HasForeignKey("CreateEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.TechnicianDbo", "Technician")
                        .WithMany()
                        .HasForeignKey("TechnicianId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.EditRecordDbo", "UpdateEvent")
                        .WithMany()
                        .HasForeignKey("UpdateEventId");

                    b.Navigation("CreateEvent");

                    b.Navigation("Technician");

                    b.Navigation("UpdateEvent");
                });

            modelBuilder.Entity("Ethos.Model.WorkflowEntityLinkDbo", b =>
                {
                    b.HasOne("Ethos.Model.WorkflowInstanceDbo", "WorkflowInstance")
                        .WithMany("EntityLinks")
                        .HasForeignKey("InstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkflowInstance");
                });

            modelBuilder.Entity("Ethos.Model.WorkflowTransitionDbo", b =>
                {
                    b.HasOne("Ethos.Model.WorkflowInstanceDbo", "WorkflowInstance")
                        .WithMany()
                        .HasForeignKey("WorkflowInstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkflowInstance");
                });

            modelBuilder.Entity("Ethos.Workflows.Database.UserNotification", b =>
                {
                    b.HasOne("Ethos.Workflows.Database.Notification", "Notification")
                        .WithMany()
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Notification");
                });

            modelBuilder.Entity("InsuranceDboStudyDbo", b =>
                {
                    b.HasOne("Ethos.Model.InsuranceDbo", null)
                        .WithMany()
                        .HasForeignKey("InsurancesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.StudyDbo", null)
                        .WithMany()
                        .HasForeignKey("StudyDboId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PatientAppointmentDboStudyDbo", b =>
                {
                    b.HasOne("Ethos.Model.PatientAppointmentDbo", null)
                        .WithMany()
                        .HasForeignKey("AppointmentsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Ethos.Model.StudyDbo", null)
                        .WithMany()
                        .HasForeignKey("StudyDboId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("UserRoles", b =>
                {
                    b.HasOne("Ethos.Workflows.Database.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserRoles_Roles_RoleId");

                    b.HasOne("Ethos.Workflows.Database.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserRoles_Users_UserId");
                });

            modelBuilder.Entity("Ethos.Model.CareLocationDbo", b =>
                {
                    b.Navigation("Equipment");
                });

            modelBuilder.Entity("Ethos.Model.OrderDbo", b =>
                {
                    b.Navigation("Studies");
                });

            modelBuilder.Entity("Ethos.Model.OrganizationContactDetailDbo", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("ContactPersons");

                    b.Navigation("Emails");

                    b.Navigation("PhoneNumbers");
                });

            modelBuilder.Entity("Ethos.Model.PatientDbo", b =>
                {
                    b.Navigation("Guardians");

                    b.Navigation("Insurances");

                    b.Navigation("Orders");
                });

            modelBuilder.Entity("Ethos.Model.PersonalContactDetailDbo", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("Emails");

                    b.Navigation("EmergencyContacts");

                    b.Navigation("PhoneNumbers");
                });

            modelBuilder.Entity("Ethos.Model.RoomDbo", b =>
                {
                    b.Navigation("Equipment");
                });

            modelBuilder.Entity("Ethos.Model.WorkflowInstanceDbo", b =>
                {
                    b.Navigation("EntityLinks");
                });
#pragma warning restore 612, 618
        }
    }
}
