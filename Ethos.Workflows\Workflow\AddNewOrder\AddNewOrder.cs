using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.AspNetCore.Mvc;

using Ethos.Workflows.Database;
using Ethos.Workflows.Audit;
using Ethos.Workflows.Api;
using Ethos.Workflows.Notifications;
using Ethos.Model.Types;
using Ethos.Utilities;
using Ethos.Workflows.Workflow;

namespace Ethos.Workflows.Workflow.AddNewOrder;

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(OrderSubmitted), "OrderSubmitted")]
[JsonDerivedType(typeof(AddedStudy), "AddedStudy")]
[JsonDerivedType(typeof(AddedCareLocation), "AddedCareLocation")]
[JsonDerivedType(typeof(AddedPhysicians), "AddedPhysicians")]
public abstract record AddNewOrderState : IWorkflowState
{
    public static WorkflowName<AddNewOrderState> Key = new("AddNewOrder");
    private AddNewOrderState() { }

    public sealed record OrderSubmitted(Guid OrderId) : AddNewOrderState;
    public sealed record AddedStudy(StudyPreferences StudyPreferences, IReadOnlyList<Guid> AssociatedInsurance) : AddNewOrderState;
    public sealed record AddedCareLocation(StudyPreferences StudyPreferences, IReadOnlyList<Guid> AssociatedInsurance, Guid CareLocation) : AddNewOrderState;
    public sealed record AddedPhysicians(StudyPreferences StudyPreferences, IReadOnlyList<Guid> AssociatedInsurance, Guid CareLocation, Guid OrderingPhysician, Guid InterpretingPhysician, Guid? ReferringPhysician, Guid? PrimaryCarePhysician) : AddNewOrderState;
}


public interface IAddNewOrderRequest : IWorkflowTransition
{
    public sealed record AddStudy(StudyPreferences StudyPreferences,
    IReadOnlyList<Guid> AssociatedInsurance) : IAddNewOrderRequest;
    public sealed record AddCareLocation(Guid CareLocation) : IAddNewOrderRequest;
    public sealed record AddPhysicians(Guid OrderingPhysician,
    Guid InterpretingPhysician,
    Guid? ReferringPhysician,
    Guid? PrimaryCarePhysician) : IAddNewOrderRequest;
    public sealed record ReviewAndSubmitOrder() : IAddNewOrderRequest;
}


public sealed record StartAddNewOrderRequest(Guid PatientId);
public sealed record StartAddNewOrderResponse(Guid WorkflowId, Guid OrderId);


public interface IAddNewOrderFlow : IAbstractFlow
{
    Task<AddNewOrderState.AddedStudy> AddStudy(FlowContext ctx, StudyPreferences studyPreferences, IReadOnlyList<Guid> associatedInsurance)
    {
        return Task.FromResult<AddNewOrderState.AddedStudy>(new AddNewOrderState.AddedStudy(
            StudyPreferences : studyPreferences, 
            AssociatedInsurance : associatedInsurance
        ));
    }
    Task<AddNewOrderState.AddedCareLocation> AddCareLocation(FlowContext<AddNewOrderState.AddedStudy> ctx, Guid careLocation)
    {
        return Task.FromResult<AddNewOrderState.AddedCareLocation>(new AddNewOrderState.AddedCareLocation(
            StudyPreferences : ctx.OldState.StudyPreferences, 
            AssociatedInsurance : ctx.OldState.AssociatedInsurance, 
            CareLocation : careLocation
        ));
    }
    Task<AddNewOrderState.AddedPhysicians> AddPhysicians(FlowContext<AddNewOrderState.AddedCareLocation> ctx, Guid orderingPhysician, Guid interpretingPhysician, Guid? referringPhysician, Guid? primaryCarePhysician)
    {
        return Task.FromResult<AddNewOrderState.AddedPhysicians>(new AddNewOrderState.AddedPhysicians(
            StudyPreferences : ctx.OldState.StudyPreferences, 
            AssociatedInsurance : ctx.OldState.AssociatedInsurance, 
            CareLocation : ctx.OldState.CareLocation, 
            OrderingPhysician : orderingPhysician, 
            InterpretingPhysician : interpretingPhysician, 
            ReferringPhysician : referringPhysician, 
            PrimaryCarePhysician : primaryCarePhysician
        ));
    }
    Task<AddNewOrderState.OrderSubmitted> ReviewAndSubmitOrder(FlowContext<AddNewOrderState.AddedPhysicians> ctx);
}


[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AddNewOrderController : ControllerBase
{
    private readonly AppDbContext _dbContext;
    private readonly IWorkflowEngine _engine;
    private readonly IAuditService _auditService;
    private readonly IAddNewOrderFlow _flow;
    private readonly INotificationService _notificationService;
    private readonly ILogger<AddNewOrderController> _logger;

    public AddNewOrderController(AppDbContext dbContext, IWorkflowEngine engine, INotificationService _notificationService, IAuditService auditService, ILogger<AddNewOrderController> logger, IAddNewOrderFlow flow)
    {
        _dbContext = dbContext;
        _engine = engine;
        _notificationService = _notificationService;
        _auditService = auditService;
        _logger = logger;
        _flow = flow;
    }

    [HttpGet("validation-rules")]
    public Dictionary<string, List<ValidationRule>> GetValidationRules()
    {
        var rules = new Dictionary<string, List<ValidationRule>>();
        // NonEmptyString
        var nonEmptyStringRules = new List<ValidationRule>();
        rules.Add("NonEmptyString", nonEmptyStringRules);
        nonEmptyStringRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        // SSN
        var sSNRules = new List<ValidationRule>();
        rules.Add("SSN", sSNRules);
        sSNRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        sSNRules.Add(new ValidationRule("InvalidSSN", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[0-9]{3}-[0-9]{2}-[0-9]{4}$")]), "Invalid SSN format"));
        // SomeMRN
        var someMRNRules = new List<ValidationRule>();
        rules.Add("SomeMRN", someMRNRules);
        someMRNRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        someMRNRules.Add(new ValidationRule("InvalidMRN", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z0-9]{1,20}$")]), "Invalid MRN format"));
        // Email
        var emailRules = new List<ValidationRule>();
        rules.Add("Email", emailRules);
        emailRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        emailRules.Add(new ValidationRule("Email", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")]), "Invalid email address"));
        // BirthDate
        var birthDateRules = new List<ValidationRule>();
        rules.Add("BirthDate", birthDateRules);
        birthDateRules.Add(new ValidationRule("DateOfBirthMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.Call("age", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(150)), "Date of birth is unrealistic"));
        birthDateRules.Add(new ValidationRule("DateOfBirthIsInTheFuture", new ValidationExpr.BinOp(new ValidationExpr.Variable("value"), "<", new ValidationExpr.Call("nowdate", [])), "Date of birth is in the future"));
        // NamePart
        var namePartRules = new List<ValidationRule>();
        rules.Add("NamePart", namePartRules);
        namePartRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        namePartRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(50)), "Name part is too long"));
        namePartRules.Add(new ValidationRule("TooShort", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">=", new ValidationExpr.IntegerLiteral(2)), "Name part is too short"));
        namePartRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z-]+$")]), "Name part contains non-latin characters"));
        // CityName
        var cityNameRules = new List<ValidationRule>();
        rules.Add("CityName", cityNameRules);
        cityNameRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        cityNameRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(50)), "City name is too long"));
        cityNameRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z-]+$")]), "City name contains non-latin characters"));
        // PostalCode
        var postalCodeRules = new List<ValidationRule>();
        rules.Add("PostalCode", postalCodeRules);
        postalCodeRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        postalCodeRules.Add(new ValidationRule("TooShort", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">=", new ValidationExpr.IntegerLiteral(3)), "Postal code is too short"));
        postalCodeRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(16)), "Postal code is too long"));
        postalCodeRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z0-9- ]+$")]), "Postal code contains non-latin characters"));
        // Address
        var addressRules = new List<ValidationRule>();
        rules.Add("Address", addressRules);
        addressRules.Add(new ValidationRule("NoAddressLine", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("line1")]), ">", new ValidationExpr.IntegerLiteral(0)), "At least one address line is required."));
        // PhoneNumber
        var phoneNumberRules = new List<ValidationRule>();
        rules.Add("PhoneNumber", phoneNumberRules);
        phoneNumberRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        phoneNumberRules.Add(new ValidationRule("InvalidPhoneNumber", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[0-9]{3}-[0-9]{3}-[0-9]{4}$")]), "Invalid phone number format"));
        // TimeOfDay
        var timeOfDayRules = new List<ValidationRule>();
        rules.Add("TimeOfDay", timeOfDayRules);
        timeOfDayRules.Add(new ValidationRule("InvalidHour", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("hour"), ">=", new ValidationExpr.IntegerLiteral(0)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("hour"), "<", new ValidationExpr.IntegerLiteral(24))), "Hour must be between 0 and 23"));
        timeOfDayRules.Add(new ValidationRule("InvalidMinute", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("minute"), ">=", new ValidationExpr.IntegerLiteral(0)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("minute"), "<", new ValidationExpr.IntegerLiteral(60))), "Minute must be between 0 and 59"));
        // NamePartOrInitial
        var namePartOrInitialRules = new List<ValidationRule>();
        rules.Add("NamePartOrInitial", namePartOrInitialRules);
        namePartOrInitialRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        namePartOrInitialRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(50)), "Name part is too long"));
        namePartOrInitialRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z-]+$")]), "Name part contains non-latin characters"));
        // EmergencyContactRelationship
        var emergencyContactRelationshipRules = new List<ValidationRule>();
        rules.Add("EmergencyContactRelationship", emergencyContactRelationshipRules);
        emergencyContactRelationshipRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Variable("value"), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        // PhysicalMeasurements
        var physicalMeasurementsRules = new List<ValidationRule>();
        rules.Add("PhysicalMeasurements", physicalMeasurementsRules);
        physicalMeasurementsRules.Add(new ValidationRule("HeightIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("heightInches"), ">", new ValidationExpr.IntegerLiteral(0)), "Height must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("HeightMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.Variable("heightInches"), "<", new ValidationExpr.IntegerLiteral(200)), "Height should be less than 200 inches"));
        physicalMeasurementsRules.Add(new ValidationRule("WeightIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("weightPounds"), ">", new ValidationExpr.IntegerLiteral(0)), "Weight must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("WeightMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.Variable("weightPounds"), "<", new ValidationExpr.IntegerLiteral(1000)), "Weight should be less than 1000 lbs"));
        physicalMeasurementsRules.Add(new ValidationRule("NeckSizeIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("neckSize"), ">", new ValidationExpr.IntegerLiteral(0)), "Neck size must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("NeckSizeMightBeInvalid", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("neckSize"), ">", new ValidationExpr.IntegerLiteral(5)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("neckSize"), "<", new ValidationExpr.IntegerLiteral(50))), "Neck size should be between 5 and 50 inches"));
        physicalMeasurementsRules.Add(new ValidationRule("BMIIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("bmi"), ">", new ValidationExpr.IntegerLiteral(0)), "BMI must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("BMIMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("bmi"), ">", new ValidationExpr.IntegerLiteral(10)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("bmi"), "<", new ValidationExpr.IntegerLiteral(100))), "BMI should be between 10 and 100"));
        return rules;
    }

    [HttpGet("state/{id}")]
    public async Task<ActionResult<AddNewOrderState>> Get(Guid id)
    {
        var result = await _engine.GetInstanceAsync<AddNewOrderState>(AddNewOrderState.Key, id);
        return result == null ? NotFound() : Ok(result);
    }

    [HttpPost("start")]
    public async Task<ActionResult<StartAddNewOrderResponse>> StartNewFlow([FromBody] StartAddNewOrderRequest request)
    {
        var resultId = Guid.NewGuid();
        var workflowId = await _engine.StartNewWorkflowAsync<AddNewOrderState>(AddNewOrderState.Key, new Dictionary<string, Guid> { ["Patient"] = request.PatientId, ["Order"] = resultId });
        await _dbContext.SaveChangesAsync();
        return Ok(new StartAddNewOrderResponse(workflowId, resultId));
    }

    [HttpPost("rewind")]
    public async Task<ActionResult> RewindFlow(Guid id, string stateName)
    {
        await _engine.RewindState<AddNewOrderState>(AddNewOrderState.Key, id, stateName);
        await _dbContext.SaveChangesAsync();
        return Ok();
    }

    [HttpPost("list")]
    public async Task<ActionResult<IReadOnlyList<Guid>>> ListInstances(IReadOnlyDictionary<string, Guid>? entityLinks = null)
    {
        return Ok(await _engine.GetInstancesAsync(AddNewOrderState.Key, entityLinks));
    }

    [HttpPost("add-study")]
    public async Task<ActionResult<WorkflowResponse<AddNewOrderState.AddedStudy?>>> AddStudy(WorkflowRequest<IAddNewOrderRequest.AddStudy> request)
    {
        using (_logger.BeginScope(new {Transition = "AddStudy"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewOrderState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewOrderState.AddedStudy newState;
                try
                {
                    var ctx = new FlowContext(instanceId, rawOldState.EntityLinks);
                    newState = await _flow.AddStudy(ctx, request.InputData.StudyPreferences, request.InputData.AssociatedInsurance);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    throw;
                }
                var endInstanceId = await _engine.CreateOrUpdateInstanceAsync<AddNewOrderState>(AddNewOrderState.Key, instanceId, new WorkflowTransitionName<object>("AddStudy"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewOrderState>("AddedStudy"), newState);

                await _auditService.LogAsync(endInstanceId, "None", "AddNewOrderState.AddedStudy", username, "AddStudy", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewOrderState.AddedStudy>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-study/draft")]
    public async Task<ActionResult<Guid>> AddStudyDraft(DraftRequest<IAddNewOrderRequest.AddStudy> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewOrderState.Key, request.InstanceId, "AddStudy", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-study/validate")]
    public async Task<ActionResult<ValidationResult>> AddStudyDraft(WorkflowRequest<IAddNewOrderRequest.AddStudy> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        foreach (var item1 in request.InputData.AssociatedInsurance)
        {
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("add-care-location")]
    public async Task<ActionResult<WorkflowResponse<AddNewOrderState.AddedCareLocation?>>> AddCareLocation(WorkflowRequest<IAddNewOrderRequest.AddCareLocation> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "AddCareLocation"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewOrderState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewOrderState.AddedStudy) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewOrderState.AddedStudy) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewOrderState.AddedCareLocation newState;
                try
                {
                    var ctx = new FlowContext<AddNewOrderState.AddedStudy>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.AddCareLocation(ctx, request.InputData.CareLocation);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewOrderState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewOrderState>(AddNewOrderState.Key, instanceId, new WorkflowTransitionName<object>("AddCareLocation"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewOrderState>("AddedCareLocation"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewOrderState.AddedStudy", "AddNewOrderState.AddedCareLocation", username, "AddCareLocation", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewOrderState.AddedCareLocation>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-care-location/draft")]
    public async Task<ActionResult<Guid>> AddCareLocationDraft(DraftRequest<IAddNewOrderRequest.AddCareLocation> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewOrderState.Key, request.InstanceId, "AddCareLocation", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-care-location/validate")]
    public async Task<ActionResult<ValidationResult>> AddCareLocationDraft(WorkflowRequest<IAddNewOrderRequest.AddCareLocation> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("add-physicians")]
    public async Task<ActionResult<WorkflowResponse<AddNewOrderState.AddedPhysicians?>>> AddPhysicians(WorkflowRequest<IAddNewOrderRequest.AddPhysicians> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "AddPhysicians"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewOrderState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewOrderState.AddedCareLocation) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewOrderState.AddedCareLocation) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewOrderState.AddedPhysicians newState;
                try
                {
                    var ctx = new FlowContext<AddNewOrderState.AddedCareLocation>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.AddPhysicians(ctx, request.InputData.OrderingPhysician, request.InputData.InterpretingPhysician, request.InputData.ReferringPhysician, request.InputData.PrimaryCarePhysician);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewOrderState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewOrderState>(AddNewOrderState.Key, instanceId, new WorkflowTransitionName<object>("AddPhysicians"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewOrderState>("AddedPhysicians"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewOrderState.AddedCareLocation", "AddNewOrderState.AddedPhysicians", username, "AddPhysicians", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewOrderState.AddedPhysicians>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-physicians/draft")]
    public async Task<ActionResult<Guid>> AddPhysiciansDraft(DraftRequest<IAddNewOrderRequest.AddPhysicians> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewOrderState.Key, request.InstanceId, "AddPhysicians", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-physicians/validate")]
    public async Task<ActionResult<ValidationResult>> AddPhysiciansDraft(WorkflowRequest<IAddNewOrderRequest.AddPhysicians> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        if (request.InputData.ReferringPhysician != null)
        {
        }
        if (request.InputData.PrimaryCarePhysician != null)
        {
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("review-and-submit-order")]
    public async Task<ActionResult<WorkflowResponse<AddNewOrderState.OrderSubmitted?>>> ReviewAndSubmitOrder(WorkflowRequest<IAddNewOrderRequest.ReviewAndSubmitOrder> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "ReviewAndSubmitOrder"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewOrderState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewOrderState.AddedPhysicians) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewOrderState.AddedPhysicians) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewOrderState.OrderSubmitted newState;
                try
                {
                    var ctx = new FlowContext<AddNewOrderState.AddedPhysicians>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.ReviewAndSubmitOrder(ctx);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewOrderState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewOrderState>(AddNewOrderState.Key, instanceId, new WorkflowTransitionName<object>("ReviewAndSubmitOrder"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewOrderState>("OrderSubmitted"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewOrderState.AddedPhysicians", "AddNewOrderState.OrderSubmitted", username, "ReviewAndSubmitOrder", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewOrderState.OrderSubmitted>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("review-and-submit-order/draft")]
    public async Task<ActionResult<Guid>> ReviewAndSubmitOrderDraft(DraftRequest<IAddNewOrderRequest.ReviewAndSubmitOrder> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewOrderState.Key, request.InstanceId, "ReviewAndSubmitOrder", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("review-and-submit-order/validate")]
    public async Task<ActionResult<ValidationResult>> ReviewAndSubmitOrderDraft(WorkflowRequest<IAddNewOrderRequest.ReviewAndSubmitOrder> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

}
