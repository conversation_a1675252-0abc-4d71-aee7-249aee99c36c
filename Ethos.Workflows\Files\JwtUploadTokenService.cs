using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Ethos.Model;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace Ethos.Workflows.Files;

/// <summary>
/// Represents the claims stored within an Upload Token JWT.
/// </summary>
public class UploadTokenClaims
{
    public required string UserId { get; set; } = string.Empty;
    public required EntityType ContextEntityType { get; set; }
    public required Guid ContextEntityId { get; set; }
    public required string Purpose { get; set; } = string.Empty;
    public required List<string> AllowedMimeTypes { get; set; } = new List<string>();
    public required long MaxFileSize { get; set; }
    // Standard JWT claims like 'exp', 'iat', 'iss', 'aud' will also be handled by the JWT library
}

/// <summary>
/// Interface for generating and validating upload tokens.
/// </summary>
public interface IUploadTokenService
{
    /// <summary>
    /// Generates a signed JWT upload token with specified claims.
    /// </summary>
    /// <param name="claims">The claims to include in the token.</param>
    /// <param name="expiresIn">Duration for which the token is valid.</param>
    /// <returns>The generated JWT string.</returns>
    string GenerateToken(UploadTokenClaims claims, TimeSpan expiresIn);

    /// <summary>
    /// Validates a provided JWT upload token and extracts its claims.
    /// </summary>
    /// <param name="token">The JWT string to validate.</param>
    /// <returns>The validated claims if the token is valid, otherwise null.</returns>
    UploadTokenClaims? ValidateToken(string token);
}

public class JwtTokenOptions
{
    public const string SectionName = "UploadTokenJwt";
    [Required] public string SecretKey { get; set; } = string.Empty;
    [Required] public string Issuer { get; set; } = string.Empty;
    [Required] public string Audience { get; set; } = string.Empty;
    public TimeSpan ClockSkew { get; set; } = TimeSpan.FromSeconds(30); // Default clock skew for token validation
    public int ExpiryMinutes { get; set; } = 5; // Short expiry for upload tokens
}

public class JwtUploadTokenService : IUploadTokenService
{
    private readonly JwtTokenOptions _options;
    private readonly SymmetricSecurityKey _signingKey;
    private readonly ILogger<JwtUploadTokenService> _logger;

    public JwtUploadTokenService(IOptions<JwtTokenOptions> tokenOptions, ILogger<JwtUploadTokenService> logger)
    {
        _options = tokenOptions.Value;
        _logger = logger;

        if (string.IsNullOrEmpty(_options.SecretKey) || _options.SecretKey.Length < 32) // Require minimum length for security
        {
            throw new InvalidOperationException("JWT Secret Key is missing or too short in configuration.");
        }
        _signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_options.SecretKey));
    }

    private const string ContextTypeClaim = "ctx_type";
    private const string ContextEntityIdClaim = "ctx_id";
    private const string PurposeClaim = "purpose";
    private const string MaxSizeClaim = "max_size";
    private const string AllowedMimTypesClaim = "allowed_types";
    private const string UserdId = "user_id";
    
    public string GenerateToken(UploadTokenClaims claims, TimeSpan expiresIn)
    {
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity([
                new Claim(UserdId, claims.UserId), // Subject is the User ID
                new Claim(ContextTypeClaim, claims.ContextEntityType.ToString()),
                new Claim(ContextEntityIdClaim, claims.ContextEntityId.ToString()),
                new Claim(PurposeClaim, claims.Purpose),
                new Claim(MaxSizeClaim, claims.MaxFileSize.ToString()),
                // Store allowed types as a single comma-separated string or multiple claims
                new Claim(AllowedMimTypesClaim, string.Join(",", claims.AllowedMimeTypes))
            ]),
            Expires = DateTime.UtcNow.Add(expiresIn),
            Issuer = _options.Issuer,
            Audience = _options.Audience,
            SigningCredentials = new SigningCredentials(_signingKey, SecurityAlgorithms.HmacSha256Signature)
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public UploadTokenClaims? ValidateToken(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        try
        {
            var principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = _signingKey,
                ValidateIssuer = true,
                ValidIssuer = _options.Issuer,
                ValidateAudience = true,
                ValidAudience = _options.Audience,
                ValidateLifetime = true, // Checks expiration
                ClockSkew = _options.ClockSkew // Allow small clock differences
            }, out SecurityToken validatedToken);

            // Extract claims
            var rawEntityType = principal.FindFirstValue(ContextTypeClaim) ?? string.Empty;
            var rawEntityId = principal.FindFirstValue(ContextEntityIdClaim) ?? string.Empty;
            
            if (!Enum.TryParse<EntityType>(rawEntityType, out var contextEntityType))
            {
                _logger.LogWarning("Upload token validation failed: Invalid context entity type '{RawEntityType}'", rawEntityType);
                return null;
            }
            
            if (!Guid.TryParse(rawEntityId, out var contextEntityId))
            {
                _logger.LogWarning("Upload token validation failed: Invalid context entity ID '{RawEntityId}'", rawEntityId);
                return null;
            }
            
            var claims = new UploadTokenClaims
            {
                UserId = principal.FindFirstValue(UserdId) ?? string.Empty,
                ContextEntityType = contextEntityType,
                ContextEntityId = contextEntityId,
                Purpose = principal.FindFirstValue(PurposeClaim) ?? string.Empty,
                MaxFileSize = long.Parse(principal.FindFirstValue(MaxSizeClaim) ?? "0"),
                AllowedMimeTypes = (principal.FindFirstValue(AllowedMimTypesClaim) ?? string.Empty)
                                    .Split(',', StringSplitOptions.RemoveEmptyEntries).ToList()
            };

            if (string.IsNullOrEmpty(claims.UserId) || claims.MaxFileSize <= 0)
            {
                _logger.LogWarning("Upload token validation failed: Missing or invalid essential claims (sub, max_size).");
                return null;
            }

            return claims;
        }
        catch (SecurityTokenExpiredException ex)
        {
            _logger.LogWarning("Upload token validation failed: Token expired. {ExceptionMessage}", ex.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Upload token validation failed.");
            return null;
        }
    }
}