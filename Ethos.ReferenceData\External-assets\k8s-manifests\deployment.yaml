apiVersion: apps/v1
kind: Deployment
metadata:
  name: ethos-referencedata
  namespace: ethos-ns-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ethos-referencedata
  template:
    metadata:
      labels:
        app: ethos-referencedata
    spec:
      containers:
      - name: ethos-referencedata
        image: ethoscrdev.azurecr.io/ethos-referencedata:2025.05.1
        imagePullPolicy: Always
        ports:
        - containerPort: 8080 
        env:
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              key: connection-string
              name: db-connection-secret
              optional: false
        - name: ASPNETCORE_URLS
          value: "http://*:8080"
        - name: VITE_API_URL
          value: "http://localhost:4000"
        - name: Auth__Authorization__DebugScopes_JSON
          value: "[ \"ReferenceData.Set.*\", \"ReferenceData.SetValue.*\", \"*.*.Admin\" ]"
      restartPolicy: Always