import { Repository, Raw, MoreThan<PERSON>rEqual, LessThanOrEqual, Between } from 'typeorm';
import { IListResult } from '@app/common/types';
import { DEFAULT_LIMIT, DEFAULT_OFFSET } from '@app/common/constants';
import { EOrderDirection, EOrderField } from '@app/common/enums';
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions';

export abstract class BaseRepository<T> extends Repository<T> {
  collectionDto?: any;

  async list(filters: any, relations: string[] = []): Promise<IListResult<T>> {
    const {
      limit = DEFAULT_LIMIT,
      offset = DEFAULT_OFFSET,
      orderDirection = EOrderDirection.Desc,
      orderField = EOrderField.CreatedAt,
      name,
      dateFrom,
      dateTo,
      ...where
    } = filters;

    if (name) {
      where.name = Raw(() => `name ILIKE '%${name}%'`);
    }

    if (dateFrom) {
      where.date = MoreThanOrEqual(dateFrom);
    }

    if (dateTo) {
      where.date = LessThanOrEqual(dateTo);
    }

    if (dateFrom && dateTo) {
      where.date = Between(dateFrom, dateTo);
    }

    const [data, count] = await this.findAndCount({
      where,
      order: {
        [orderField]: orderDirection,
      },
      take: limit,
      skip: offset,
      relations,
    } as FindManyOptions);

    if (this.collectionDto) {
      const collection = new this.collectionDto();
      collection.data = data;
      collection.count = count;

      return collection;
    }

    return { data, count };
  }
}