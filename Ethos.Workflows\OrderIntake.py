import aiohttp
import logging
from dataclasses import dataclass, field, asdict
from typing import Optional, List, Dict, Any
from uuid import UUID
import asyncio
from FlowTypes import *

class OrderIntakeState:
    """Parent base class for all states in OrderIntake."""

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'OrderIntakeState':
        tpe = obj.get("$type")
        match tpe:
            case "FaxReceived":
                return FaxReceived.from_dict(obj)
            case "EmailReceived":
                return EmailReceived.from_dict(obj)
            case "OrderEntry":
                return OrderEntry.from_dict(obj)
            case "OrderCompleted":
                return OrderCompleted.from_dict(obj)
            case _:
                raise ValueError(f"Unknown $type for this flow: {tpe}")

@dataclass
class FaxReceived(OrderIntakeState):
    faxId: int

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'FaxReceived':
        return FaxReceived(
                    faxId=obj["faxId"]
                )

@dataclass
class EmailReceived(OrderIntakeState):
    emailId: int

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'EmailReceived':
        return EmailReceived(
                    emailId=obj["emailId"]
                )

@dataclass
class OrderEntry(OrderIntakeState):

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'OrderEntry':
        return OrderEntry(
                    
                )

@dataclass
class OrderCompleted(OrderIntakeState):
    faxId: Optional[str]
    emailId: Optional[str]
    orderId: str

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'OrderCompleted':
        return OrderCompleted(
                    faxId=obj["faxId"],
                    emailId=obj["emailId"],
                    orderId=obj["orderId"]
                )

@dataclass
class OnFaxReceivedRequest:
    data: str

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'OnFaxReceivedRequest':
        return OnFaxReceivedRequest(
                    data=obj["data"]
                )

@dataclass
class OnEmailReceivedRequest:
    data: str

    @staticmethod
    def from_dict(obj: Dict[str, Any]) -> 'OnEmailReceivedRequest':
        return OnEmailReceivedRequest(
                    data=obj["data"]
                )

@dataclass
class ManualOrderEntryRequest:
    pass  # No fields required for this step

@dataclass
class SubmitOrderRequest:
    pass  # No fields required for this step

class OrderIntakeClient:
    def __init__(self, base_url: str, session: aiohttp.ClientSession, logger: logging.Logger = None):
        self.base_url = base_url
        self.session = session
        self.logger = logger if logger else logging.getLogger(__name__)

    async def get_state(self, instance_id: UUID) -> 'OrderIntakeState':
        url = f"{self.base_url}/api/OrderIntake/state/{instance_id}"
        self.logger.debug(f"GET OrderIntake state: {instance_id}")
        async with self.session.get(url) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            data = await resp.json()
        return OrderIntakeState.from_dict(data)

    async def onfaxreceived(self, instance_id: Optional[UUID], request: OnFaxReceivedRequest) -> Tuple[UUID, 'OrderIntakeState']:
        url = f"{self.base_url}/api/OrderIntake/on-fax-received"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST OrderIntake.OnFaxReceived: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new OrderIntakeState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, FaxReceived.from_dict(outputData)
    async def onemailreceived(self, instance_id: Optional[UUID], request: OnEmailReceivedRequest) -> Tuple[UUID, 'OrderIntakeState']:
        url = f"{self.base_url}/api/OrderIntake/on-email-received"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST OrderIntake.OnEmailReceived: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new OrderIntakeState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, EmailReceived.from_dict(outputData)
    async def manualorderentry(self, instance_id: Optional[UUID], request: ManualOrderEntryRequest) -> Tuple[UUID, 'OrderIntakeState']:
        url = f"{self.base_url}/api/OrderIntake/manual-order-entry"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST OrderIntake.ManualOrderEntry: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new OrderIntakeState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, OrderEntry.from_dict(outputData)
    async def submitorder(self, instance_id: Optional[UUID], request: SubmitOrderRequest) -> Tuple[UUID, 'OrderIntakeState']:
        url = f"{self.base_url}/api/OrderIntake/submit-order"
        body = {
            'instanceId': str(instance_id) if instance_id else None,
            'inputData': asdict(request)
        }
        self.logger.debug(f"POST OrderIntake.SubmitOrder: {body}")
        async with self.session.post(url, json=body) as resp:
            if resp.status >= 400:
                error_body = await resp.text()
                self.logger.error(f"HTTP {resp.status} error: {error_body}")
                raise Exception(f"HTTP {resp.status} error: {error_body}")
            # non-final => returns new OrderIntakeState
            data = await resp.json()
            instanceId = data.get("instanceId")
            outputData = data.get("outputData")
            return instanceId, OrderCompleted.from_dict(outputData)

