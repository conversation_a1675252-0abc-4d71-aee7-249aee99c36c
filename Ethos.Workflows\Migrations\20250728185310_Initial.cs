﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Ethos.Workflows.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "ethos");

            migrationBuilder.CreateTable(
                name: "AuditLogs",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    WorkflowInstanceId = table.Column<Guid>(type: "uuid", nullable: false),
                    TransitionName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    OldState = table.Column<string>(type: "text", nullable: true),
                    NewState = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    Metadata = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EditRecord",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EditType = table.Column<string>(type: "text", nullable: false),
                    EntityName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrimaryKey = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OldValues = table.Column<string>(type: "jsonb", nullable: true),
                    NewValues = table.Column<string>(type: "jsonb", nullable: true),
                    ChangedColumns = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EditRecord", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FileMetadataDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    StoragePath = table.Column<string>(type: "text", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    ContextEntityType = table.Column<int>(type: "integer", nullable: false),
                    ContextEntityId = table.Column<Guid>(type: "uuid", nullable: false),
                    Purpose = table.Column<string>(type: "text", nullable: false),
                    UploadTimestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UploadFileName = table.Column<string>(type: "text", nullable: false),
                    UploadMimeType = table.Column<string>(type: "text", nullable: false),
                    UploadFileSize = table.Column<long>(type: "bigint", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Failure = table.Column<string>(type: "jsonb", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastUpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    MimeType = table.Column<string>(type: "text", nullable: true),
                    ThumbnailPath = table.Column<string>(type: "text", nullable: true),
                    ProcessingCompletedTimestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileMetadataDbo", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Notifications",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UniqueKey = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Severity = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Message = table.Column<string>(type: "text", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    IsHighUrgency = table.Column<bool>(type: "boolean", nullable: false),
                    EscalationLevel = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Notifications", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ExternalId = table.Column<string>(type: "text", nullable: false),
                    Email = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "WorkflowDraftTransitions",
                schema: "ethos",
                columns: table => new
                {
                    WorkflowInstanceId = table.Column<Guid>(type: "uuid", nullable: false),
                    TransitionName = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    TransitionData = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkflowDraftTransitions", x => new { x.WorkflowInstanceId, x.TransitionName });
                });

            migrationBuilder.CreateTable(
                name: "WorkflowInstances",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    WorkflowKey = table.Column<string>(type: "text", nullable: false),
                    CompletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AbortedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CurrentState = table.Column<string>(type: "text", nullable: false),
                    CurrentSequence = table.Column<int>(type: "integer", nullable: false),
                    TransientErrorJson = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkflowInstances", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AddressDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Line1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Line2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    CountryId = table.Column<long>(type: "bigint", nullable: false),
                    StateId = table.Column<long>(type: "bigint", nullable: true),
                    City = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PostalCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AddressDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AddressDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AddressDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DraftDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EntityType = table.Column<string>(type: "text", nullable: false),
                    EntityId = table.Column<Guid>(type: "uuid", nullable: false),
                    Data = table.Column<string>(type: "jsonb", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DraftDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DraftDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DraftDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "InsuranceHolderDataDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InsuranceHolderDataDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InsuranceHolderDataDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InsuranceHolderDataDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "NoteDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EntityType = table.Column<string>(type: "text", nullable: false),
                    EntityId = table.Column<Guid>(type: "uuid", nullable: false),
                    Content = table.Column<string>(type: "text", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NoteDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NoteDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NoteDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "OrganizationContactDetailDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrganizationContactDetailDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrganizationContactDetailDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrganizationContactDetailDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PersonalContactDetailDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonalContactDetailDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonalContactDetailDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonalContactDetailDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PhoneNumberWithUseDataDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PhoneNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    PhoneNumberTypeId = table.Column<long>(type: "bigint", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhoneNumberWithUseDataDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PhoneNumberWithUseDataDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PhoneNumberWithUseDataDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SchedulingConstraintDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    IsHardConstraint = table.Column<bool>(type: "boolean", nullable: false),
                    TopLevelVariables = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Expression = table.Column<string>(type: "jsonb", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SchedulingConstraintDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SchedulingConstraintDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SchedulingConstraintDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserNotifications",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    NotificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsRead = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserNotifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserNotifications_Notifications_NotificationId",
                        column: x => x.NotificationId,
                        principalSchema: "ethos",
                        principalTable: "Notifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                schema: "ethos",
                columns: table => new
                {
                    RoleId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => new { x.RoleId, x.UserId });
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "ethos",
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "ethos",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkflowEntityContexts",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    InstanceId = table.Column<Guid>(type: "uuid", nullable: false),
                    EntityType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    EntityId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkflowEntityContexts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkflowEntityContexts_WorkflowInstances_InstanceId",
                        column: x => x.InstanceId,
                        principalSchema: "ethos",
                        principalTable: "WorkflowInstances",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkflowInstanceTransitions",
                schema: "ethos",
                columns: table => new
                {
                    WorkflowInstanceId = table.Column<Guid>(type: "uuid", nullable: false),
                    SequenceId = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    TransitionName = table.Column<string>(type: "text", nullable: false),
                    TransitionData = table.Column<string>(type: "text", nullable: false),
                    NewStateName = table.Column<string>(type: "text", nullable: false),
                    NewStateData = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkflowInstanceTransitions", x => new { x.WorkflowInstanceId, x.SequenceId });
                    table.ForeignKey(
                        name: "FK_WorkflowInstanceTransitions_WorkflowInstances_WorkflowInsta~",
                        column: x => x.WorkflowInstanceId,
                        principalSchema: "ethos",
                        principalTable: "WorkflowInstances",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrganizationAddressDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    AddressId = table.Column<Guid>(type: "uuid", nullable: false),
                    UseId = table.Column<long>(type: "bigint", nullable: false),
                    TypeId = table.Column<long>(type: "bigint", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrganizationAddressDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrganizationAddressDbo_AddressDbo_AddressId",
                        column: x => x.AddressId,
                        principalSchema: "ethos",
                        principalTable: "AddressDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_OrganizationAddressDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrganizationAddressDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OrganizationAddressDbo_OrganizationContactDetailDbo_ParentId",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "OrganizationContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrganizationEmailDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    Email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrganizationEmailDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrganizationEmailDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrganizationEmailDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OrganizationEmailDbo_OrganizationContactDetailDbo_ParentId",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "OrganizationContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrganizationPhoneNumberDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PhoneNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Use = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AllowsSMS = table.Column<bool>(type: "boolean", nullable: true),
                    AllowsVoice = table.Column<bool>(type: "boolean", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrganizationPhoneNumberDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrganizationPhoneNumberDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrganizationPhoneNumberDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OrganizationPhoneNumberDbo_OrganizationContactDetailDbo_Par~",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "OrganizationContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProviderDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProviderPath = table.Column<string>(type: "text", nullable: false),
                    ParentProviderId = table.Column<Guid>(type: "uuid", nullable: true),
                    ContactDetailId = table.Column<Guid>(type: "uuid", nullable: true),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProviderDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProviderDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ProviderDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ProviderDbo_OrganizationContactDetailDbo_ContactDetailId",
                        column: x => x.ContactDetailId,
                        principalSchema: "ethos",
                        principalTable: "OrganizationContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_ProviderDbo_ProviderDbo_ParentProviderId",
                        column: x => x.ParentProviderId,
                        principalSchema: "ethos",
                        principalTable: "ProviderDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrganizationContactPersonDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name_FirstName = table.Column<string>(type: "text", nullable: false),
                    Name_MiddleName = table.Column<string>(type: "text", nullable: true),
                    Name_LastName = table.Column<string>(type: "text", nullable: false),
                    Name_PrefixId = table.Column<long>(type: "bigint", nullable: true),
                    Name_SuffixId = table.Column<long>(type: "bigint", nullable: true),
                    ContactDetailId = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<int>(type: "integer", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrganizationContactPersonDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrganizationContactPersonDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrganizationContactPersonDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OrganizationContactPersonDbo_OrganizationContactDetailDbo_P~",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "OrganizationContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrganizationContactPersonDbo_PersonalContactDetailDbo_Conta~",
                        column: x => x.ContactDetailId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactDetailId = table.Column<Guid>(type: "uuid", nullable: true),
                    PhysicalMeasurements_HeightInches = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    PhysicalMeasurements_WeightPounds = table.Column<decimal>(type: "numeric(6,2)", precision: 6, scale: 2, nullable: true),
                    PhysicalMeasurements_NeckSize = table.Column<decimal>(type: "numeric(4,2)", precision: 4, scale: 2, nullable: true),
                    PhysicalMeasurements_Bmi = table.Column<decimal>(type: "numeric(4,2)", precision: 4, scale: 2, nullable: true),
                    ClinicalConsiderations = table.Column<long[]>(type: "bigint[]", nullable: false),
                    PreferredWeekdays = table.Column<long[]>(type: "bigint[]", nullable: false),
                    TechnicianPreference = table.Column<long>(type: "bigint", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PatientDbo_PersonalContactDetailDbo_ContactDetailId",
                        column: x => x.ContactDetailId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonalAddressDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    AddressId = table.Column<Guid>(type: "uuid", nullable: false),
                    UseId = table.Column<long>(type: "bigint", nullable: false),
                    TypeId = table.Column<long>(type: "bigint", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonalAddressDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonalAddressDbo_AddressDbo_AddressId",
                        column: x => x.AddressId,
                        principalSchema: "ethos",
                        principalTable: "AddressDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PersonalAddressDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonalAddressDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PersonalAddressDbo_PersonalContactDetailDbo_ParentId",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonalEmailDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    Email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    Use = table.Column<long>(type: "bigint", nullable: false),
                    IsPreferred = table.Column<bool>(type: "boolean", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonalEmailDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonalEmailDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonalEmailDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PersonalEmailDbo_PersonalContactDetailDbo_ParentId",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonalEmergencyContactDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name_FirstName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name_MiddleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Name_LastName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name_PrefixId = table.Column<long>(type: "bigint", nullable: true),
                    Name_SuffixId = table.Column<long>(type: "bigint", nullable: true),
                    RelationshipId = table.Column<long>(type: "bigint", nullable: false),
                    ContactInformation = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonalEmergencyContactDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonalEmergencyContactDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonalEmergencyContactDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PersonalEmergencyContactDbo_PersonalContactDetailDbo_Parent~",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PersonalPhoneNumberDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentId = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PhoneNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Use = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AllowsTextMessages = table.Column<bool>(type: "boolean", nullable: false),
                    AllowsVoice = table.Column<bool>(type: "boolean", nullable: false),
                    IsPreferred = table.Column<bool>(type: "boolean", nullable: false),
                    PreferredTimeId = table.Column<long>(type: "bigint", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PersonalPhoneNumberDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PersonalPhoneNumberDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PersonalPhoneNumberDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PersonalPhoneNumberDbo_PersonalContactDetailDbo_ParentId",
                        column: x => x.ParentId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PhysicianDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactDetailId = table.Column<Guid>(type: "uuid", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhysicianDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PhysicianDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PhysicianDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PhysicianDbo_PersonalContactDetailDbo_ContactDetailId",
                        column: x => x.ContactDetailId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TechnicianDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactDetailId = table.Column<Guid>(type: "uuid", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TechnicianDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TechnicianDbo_PersonalContactDetailDbo_ContactDetailId",
                        column: x => x.ContactDetailId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CareLocationDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ParentCareLocationId = table.Column<Guid>(type: "uuid", nullable: true),
                    CareLocationPath = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ParentProviderId = table.Column<Guid>(type: "uuid", nullable: true),
                    ContactDetailId = table.Column<Guid>(type: "uuid", nullable: true),
                    SupportedEncounterTypes = table.Column<long[]>(type: "bigint[]", nullable: false),
                    SupportedStudyTypes = table.Column<long[]>(type: "bigint[]", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CareLocationDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CareLocationDbo_CareLocationDbo_ParentCareLocationId",
                        column: x => x.ParentCareLocationId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CareLocationDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CareLocationDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CareLocationDbo_OrganizationContactDetailDbo_ContactDetailId",
                        column: x => x.ContactDetailId,
                        principalSchema: "ethos",
                        principalTable: "OrganizationContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CareLocationDbo_ProviderDbo_ParentProviderId",
                        column: x => x.ParentProviderId,
                        principalSchema: "ethos",
                        principalTable: "ProviderDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProviderDbo_Identifiers",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    System = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProviderDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProviderDbo_Identifiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProviderDbo_Identifiers_ProviderDbo_ProviderDboId",
                        column: x => x.ProviderDboId,
                        principalSchema: "ethos",
                        principalTable: "ProviderDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "InsuranceDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    InsuranceCarrier = table.Column<long>(type: "bigint", nullable: true),
                    InsuranceId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    PolicyId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    GroupNumber = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    MemberId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    PhoneNumberId = table.Column<Guid>(type: "uuid", nullable: true),
                    PatientId = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InsuranceDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InsuranceDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InsuranceDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InsuranceDbo_InsuranceHolderDataDbo_Id",
                        column: x => x.Id,
                        principalSchema: "ethos",
                        principalTable: "InsuranceHolderDataDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InsuranceDbo_PatientDbo_PatientId",
                        column: x => x.PatientId,
                        principalSchema: "ethos",
                        principalTable: "PatientDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InsuranceDbo_PhoneNumberWithUseDataDbo_PhoneNumberId",
                        column: x => x.PhoneNumberId,
                        principalSchema: "ethos",
                        principalTable: "PhoneNumberWithUseDataDbo",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PatientDbo_Demographics",
                schema: "ethos",
                columns: table => new
                {
                    PatientDboId = table.Column<Guid>(type: "uuid", nullable: false),
                    DateOfBirth = table.Column<DateOnly>(type: "date", nullable: true),
                    GenderId = table.Column<long>(type: "bigint", nullable: true),
                    SexId = table.Column<long>(type: "bigint", nullable: true),
                    MaritalStatusId = table.Column<long>(type: "bigint", nullable: true),
                    RaceId = table.Column<long>(type: "bigint", nullable: true),
                    EthnicityId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientDbo_Demographics", x => x.PatientDboId);
                    table.ForeignKey(
                        name: "FK_PatientDbo_Demographics_PatientDbo_PatientDboId",
                        column: x => x.PatientDboId,
                        principalSchema: "ethos",
                        principalTable: "PatientDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientDbo_Identifiers",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    System = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PatientDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientDbo_Identifiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientDbo_Identifiers_PatientDbo_PatientDboId",
                        column: x => x.PatientDboId,
                        principalSchema: "ethos",
                        principalTable: "PatientDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientDbo_Names",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FirstName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    MiddleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrefixId = table.Column<long>(type: "bigint", nullable: true),
                    SuffixId = table.Column<long>(type: "bigint", nullable: true),
                    PatientDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientDbo_Names", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientDbo_Names_PatientDbo_PatientDboId",
                        column: x => x.PatientDboId,
                        principalSchema: "ethos",
                        principalTable: "PatientDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientGuardianDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactDetailId = table.Column<Guid>(type: "uuid", nullable: true),
                    RelationshipToPatient = table.Column<long>(type: "bigint", nullable: true),
                    PatientDboId = table.Column<Guid>(type: "uuid", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientGuardianDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientGuardianDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientGuardianDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PatientGuardianDbo_PatientDbo_PatientDboId",
                        column: x => x.PatientDboId,
                        principalSchema: "ethos",
                        principalTable: "PatientDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientGuardianDbo_PersonalContactDetailDbo_ContactDetailId",
                        column: x => x.ContactDetailId,
                        principalSchema: "ethos",
                        principalTable: "PersonalContactDetailDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PhysicianDbo_Demographics",
                schema: "ethos",
                columns: table => new
                {
                    PhysicianDboId = table.Column<Guid>(type: "uuid", nullable: false),
                    DateOfBirth = table.Column<DateOnly>(type: "date", nullable: true),
                    GenderId = table.Column<long>(type: "bigint", nullable: true),
                    SexId = table.Column<long>(type: "bigint", nullable: true),
                    MaritalStatusId = table.Column<long>(type: "bigint", nullable: true),
                    RaceId = table.Column<long>(type: "bigint", nullable: true),
                    EthnicityId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhysicianDbo_Demographics", x => x.PhysicianDboId);
                    table.ForeignKey(
                        name: "FK_PhysicianDbo_Demographics_PhysicianDbo_PhysicianDboId",
                        column: x => x.PhysicianDboId,
                        principalSchema: "ethos",
                        principalTable: "PhysicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PhysicianDbo_Identifiers",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    System = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PhysicianDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhysicianDbo_Identifiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PhysicianDbo_Identifiers_PhysicianDbo_PhysicianDboId",
                        column: x => x.PhysicianDboId,
                        principalSchema: "ethos",
                        principalTable: "PhysicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PhysicianDbo_Names",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FirstName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    MiddleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrefixId = table.Column<long>(type: "bigint", nullable: true),
                    SuffixId = table.Column<long>(type: "bigint", nullable: true),
                    PhysicianDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhysicianDbo_Names", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PhysicianDbo_Names_PhysicianDbo_PhysicianDboId",
                        column: x => x.PhysicianDboId,
                        principalSchema: "ethos",
                        principalTable: "PhysicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TechnicianDbo_Demographics",
                schema: "ethos",
                columns: table => new
                {
                    TechnicianDboId = table.Column<Guid>(type: "uuid", nullable: false),
                    DateOfBirth = table.Column<DateOnly>(type: "date", nullable: true),
                    GenderId = table.Column<long>(type: "bigint", nullable: true),
                    SexId = table.Column<long>(type: "bigint", nullable: true),
                    MaritalStatusId = table.Column<long>(type: "bigint", nullable: true),
                    RaceId = table.Column<long>(type: "bigint", nullable: true),
                    EthnicityId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianDbo_Demographics", x => x.TechnicianDboId);
                    table.ForeignKey(
                        name: "FK_TechnicianDbo_Demographics_TechnicianDbo_TechnicianDboId",
                        column: x => x.TechnicianDboId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TechnicianDbo_Identifiers",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    System = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    TechnicianDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianDbo_Identifiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TechnicianDbo_Identifiers_TechnicianDbo_TechnicianDboId",
                        column: x => x.TechnicianDboId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TechnicianDbo_Names",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FirstName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    MiddleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrefixId = table.Column<long>(type: "bigint", nullable: true),
                    SuffixId = table.Column<long>(type: "bigint", nullable: true),
                    TechnicianDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianDbo_Names", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TechnicianDbo_Names_TechnicianDbo_TechnicianDboId",
                        column: x => x.TechnicianDboId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TechnicianDbo_Qualifications",
                schema: "ethos",
                columns: table => new
                {
                    TechnicianDboId = table.Column<Guid>(type: "uuid", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    QualificationId = table.Column<long>(type: "bigint", nullable: false),
                    DateObtained = table.Column<DateOnly>(type: "date", nullable: true),
                    DateExpires = table.Column<DateOnly>(type: "date", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianDbo_Qualifications", x => new { x.TechnicianDboId, x.Id });
                    table.ForeignKey(
                        name: "FK_TechnicianDbo_Qualifications_TechnicianDbo_TechnicianDboId",
                        column: x => x.TechnicianDboId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TechnicianRoleDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TechnicianId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<long>(type: "bigint", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianRoleDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TechnicianRoleDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianRoleDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TechnicianRoleDbo_TechnicianDbo_TechnicianId",
                        column: x => x.TechnicianId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TechnicianShiftPreferenceDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TechnicianId = table.Column<Guid>(type: "uuid", nullable: false),
                    ShiftId = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianShiftPreferenceDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TechnicianShiftPreferenceDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianShiftPreferenceDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TechnicianShiftPreferenceDbo_TechnicianDbo_TechnicianId",
                        column: x => x.TechnicianId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CareLocationDbo_Identifiers",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    System = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CareLocationDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CareLocationDbo_Identifiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CareLocationDbo_Identifiers_CareLocationDbo_CareLocationDbo~",
                        column: x => x.CareLocationDboId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CareLocationShiftDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CareLocationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    FromTime = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    ToTime = table.Column<TimeOnly>(type: "time without time zone", nullable: false),
                    State = table.Column<int>(type: "integer", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CareLocationShiftDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CareLocationShiftDbo_CareLocationDbo_CareLocationId",
                        column: x => x.CareLocationId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CareLocationShiftDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CareLocationShiftDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "OrderDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PatientId = table.Column<Guid>(type: "uuid", nullable: false),
                    PrimaryCarePhysicianId = table.Column<Guid>(type: "uuid", nullable: true),
                    ReferringPhysicianId = table.Column<Guid>(type: "uuid", nullable: true),
                    InterpretingPhysicianId = table.Column<Guid>(type: "uuid", nullable: true),
                    CareLocationId = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderDbo_CareLocationDbo_CareLocationId",
                        column: x => x.CareLocationId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrderDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrderDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OrderDbo_PatientDbo_PatientId",
                        column: x => x.PatientId,
                        principalSchema: "ethos",
                        principalTable: "PatientDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrderDbo_PhysicianDbo_InterpretingPhysicianId",
                        column: x => x.InterpretingPhysicianId,
                        principalSchema: "ethos",
                        principalTable: "PhysicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrderDbo_PhysicianDbo_PrimaryCarePhysicianId",
                        column: x => x.PrimaryCarePhysicianId,
                        principalSchema: "ethos",
                        principalTable: "PhysicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrderDbo_PhysicianDbo_ReferringPhysicianId",
                        column: x => x.ReferringPhysicianId,
                        principalSchema: "ethos",
                        principalTable: "PhysicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PhysicianCareLocationRelationDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PhysicianId = table.Column<Guid>(type: "uuid", nullable: false),
                    CareLocationId = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhysicianCareLocationRelationDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PhysicianCareLocationRelationDbo_CareLocationDbo_CareLocati~",
                        column: x => x.CareLocationId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PhysicianCareLocationRelationDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PhysicianCareLocationRelationDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PhysicianCareLocationRelationDbo_PhysicianDbo_PhysicianId",
                        column: x => x.PhysicianId,
                        principalSchema: "ethos",
                        principalTable: "PhysicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RoomDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CareLocationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    SupportedStudyTypes = table.Column<long[]>(type: "bigint[]", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoomDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RoomDbo_CareLocationDbo_CareLocationId",
                        column: x => x.CareLocationId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoomDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoomDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TechnicianCareLocationRelationDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TechnicianId = table.Column<Guid>(type: "uuid", nullable: false),
                    CareLocationId = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianCareLocationRelationDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TechnicianCareLocationRelationDbo_CareLocationDbo_CareLocat~",
                        column: x => x.CareLocationId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianCareLocationRelationDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianCareLocationRelationDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TechnicianCareLocationRelationDbo_TechnicianDbo_TechnicianId",
                        column: x => x.TechnicianId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientGuardianDbo_Demographics",
                schema: "ethos",
                columns: table => new
                {
                    PatientGuardianDboId = table.Column<Guid>(type: "uuid", nullable: false),
                    DateOfBirth = table.Column<DateOnly>(type: "date", nullable: true),
                    GenderId = table.Column<long>(type: "bigint", nullable: true),
                    SexId = table.Column<long>(type: "bigint", nullable: true),
                    MaritalStatusId = table.Column<long>(type: "bigint", nullable: true),
                    RaceId = table.Column<long>(type: "bigint", nullable: true),
                    EthnicityId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientGuardianDbo_Demographics", x => x.PatientGuardianDboId);
                    table.ForeignKey(
                        name: "FK_PatientGuardianDbo_Demographics_PatientGuardianDbo_PatientG~",
                        column: x => x.PatientGuardianDboId,
                        principalSchema: "ethos",
                        principalTable: "PatientGuardianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientGuardianDbo_Identifiers",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    System = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PatientGuardianDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientGuardianDbo_Identifiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientGuardianDbo_Identifiers_PatientGuardianDbo_PatientGu~",
                        column: x => x.PatientGuardianDboId,
                        principalSchema: "ethos",
                        principalTable: "PatientGuardianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientGuardianDbo_Names",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FirstName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    MiddleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrefixId = table.Column<long>(type: "bigint", nullable: true),
                    SuffixId = table.Column<long>(type: "bigint", nullable: true),
                    PatientGuardianDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientGuardianDbo_Names", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientGuardianDbo_Names_PatientGuardianDbo_PatientGuardian~",
                        column: x => x.PatientGuardianDboId,
                        principalSchema: "ethos",
                        principalTable: "PatientGuardianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "StudyDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EncounterType = table.Column<long>(type: "bigint", nullable: true),
                    StudyType = table.Column<long>(type: "bigint", nullable: true),
                    StudyAttributes = table.Column<string>(type: "jsonb", nullable: true),
                    Location = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Interpreting = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Referring = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ScoredDate = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StudyDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StudyDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StudyDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StudyDbo_OrderDbo_OrderId",
                        column: x => x.OrderId,
                        principalSchema: "ethos",
                        principalTable: "OrderDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EquipmentDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CareLocationId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoomId = table.Column<Guid>(type: "uuid", nullable: true),
                    EquipmentTypeId = table.Column<long>(type: "bigint", nullable: false),
                    EquipmentData = table.Column<string>(type: "jsonb", nullable: false),
                    CareLocationDboId = table.Column<Guid>(type: "uuid", nullable: true),
                    RoomDboId = table.Column<Guid>(type: "uuid", nullable: true),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EquipmentDbo_CareLocationDbo_CareLocationDboId",
                        column: x => x.CareLocationDboId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_EquipmentDbo_CareLocationDbo_CareLocationId",
                        column: x => x.CareLocationId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EquipmentDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EquipmentDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_EquipmentDbo_RoomDbo_RoomDboId",
                        column: x => x.RoomDboId,
                        principalSchema: "ethos",
                        principalTable: "RoomDbo",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_EquipmentDbo_RoomDbo_RoomId",
                        column: x => x.RoomId,
                        principalSchema: "ethos",
                        principalTable: "RoomDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "InsuranceDboStudyDbo",
                schema: "ethos",
                columns: table => new
                {
                    InsurancesId = table.Column<Guid>(type: "uuid", nullable: false),
                    StudyDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InsuranceDboStudyDbo", x => new { x.InsurancesId, x.StudyDboId });
                    table.ForeignKey(
                        name: "FK_InsuranceDboStudyDbo_InsuranceDbo_InsurancesId",
                        column: x => x.InsurancesId,
                        principalSchema: "ethos",
                        principalTable: "InsuranceDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InsuranceDboStudyDbo_StudyDbo_StudyDboId",
                        column: x => x.StudyDboId,
                        principalSchema: "ethos",
                        principalTable: "StudyDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "InsuranceVerificationDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    StudyId = table.Column<Guid>(type: "uuid", nullable: false),
                    ServiceId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    StateName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    StateJson = table.Column<string>(type: "jsonb", nullable: false),
                    CoarseStateDiscriminator = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    LastCoarseStateUpdate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ProcessingServiceInstanceId = table.Column<Guid>(type: "uuid", nullable: true),
                    ProcessingTimeoutTimestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    WaitingUntil = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExternalActionTimeoutTimestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CoarseStateMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CoarseStateSucceeded = table.Column<bool>(type: "boolean", nullable: true),
                    State = table.Column<int>(type: "integer", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InsuranceVerificationDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InsuranceVerificationDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InsuranceVerificationDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InsuranceVerificationDbo_StudyDbo_StudyId",
                        column: x => x.StudyId,
                        principalSchema: "ethos",
                        principalTable: "StudyDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PatientAppointmentDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    StudyId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoomId = table.Column<Guid>(type: "uuid", nullable: false),
                    CareLocationShiftId = table.Column<Guid>(type: "uuid", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false),
                    ScheduledById = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientAppointmentDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientAppointmentDbo_CareLocationShiftDbo_CareLocationShif~",
                        column: x => x.CareLocationShiftId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationShiftDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientAppointmentDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientAppointmentDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PatientAppointmentDbo_RoomDbo_RoomId",
                        column: x => x.RoomId,
                        principalSchema: "ethos",
                        principalTable: "RoomDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientAppointmentDbo_StudyDbo_StudyId",
                        column: x => x.StudyId,
                        principalSchema: "ethos",
                        principalTable: "StudyDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TechnicianAppointmentDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TechnicianId = table.Column<Guid>(type: "uuid", nullable: false),
                    StudyId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoomId = table.Column<Guid>(type: "uuid", nullable: false),
                    CareLocationShiftId = table.Column<Guid>(type: "uuid", nullable: false),
                    Date = table.Column<DateOnly>(type: "date", nullable: false),
                    ScheduledById = table.Column<Guid>(type: "uuid", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TechnicianAppointmentDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TechnicianAppointmentDbo_CareLocationShiftDbo_CareLocationS~",
                        column: x => x.CareLocationShiftId,
                        principalSchema: "ethos",
                        principalTable: "CareLocationShiftDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianAppointmentDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianAppointmentDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TechnicianAppointmentDbo_RoomDbo_RoomId",
                        column: x => x.RoomId,
                        principalSchema: "ethos",
                        principalTable: "RoomDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianAppointmentDbo_StudyDbo_StudyId",
                        column: x => x.StudyId,
                        principalSchema: "ethos",
                        principalTable: "StudyDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TechnicianAppointmentDbo_TechnicianDbo_TechnicianId",
                        column: x => x.TechnicianId,
                        principalSchema: "ethos",
                        principalTable: "TechnicianDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EquipmentDbo_Identifiers",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    System = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    EquipmentDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentDbo_Identifiers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EquipmentDbo_Identifiers_EquipmentDbo_EquipmentDboId",
                        column: x => x.EquipmentDboId,
                        principalSchema: "ethos",
                        principalTable: "EquipmentDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientAppointmentConfirmationDbo",
                schema: "ethos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PatientAppointmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    ConfirmationTypeId = table.Column<long>(type: "bigint", nullable: false),
                    ConfirmationDateTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ConfirmedById = table.Column<Guid>(type: "uuid", nullable: false),
                    AddedDateTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    State = table.Column<string>(type: "text", nullable: false),
                    CreateEventId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateEventId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientAppointmentConfirmationDbo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PatientAppointmentConfirmationDbo_EditRecord_CreateEventId",
                        column: x => x.CreateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientAppointmentConfirmationDbo_EditRecord_UpdateEventId",
                        column: x => x.UpdateEventId,
                        principalSchema: "ethos",
                        principalTable: "EditRecord",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PatientAppointmentConfirmationDbo_PatientAppointmentDbo_Pat~",
                        column: x => x.PatientAppointmentId,
                        principalSchema: "ethos",
                        principalTable: "PatientAppointmentDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PatientAppointmentDboStudyDbo",
                schema: "ethos",
                columns: table => new
                {
                    AppointmentsId = table.Column<Guid>(type: "uuid", nullable: false),
                    StudyDboId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PatientAppointmentDboStudyDbo", x => new { x.AppointmentsId, x.StudyDboId });
                    table.ForeignKey(
                        name: "FK_PatientAppointmentDboStudyDbo_PatientAppointmentDbo_Appoint~",
                        column: x => x.AppointmentsId,
                        principalSchema: "ethos",
                        principalTable: "PatientAppointmentDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PatientAppointmentDboStudyDbo_StudyDbo_StudyDboId",
                        column: x => x.StudyDboId,
                        principalSchema: "ethos",
                        principalTable: "StudyDbo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AddressDbo_CreateEventId",
                schema: "ethos",
                table: "AddressDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_AddressDbo_UpdateEventId",
                schema: "ethos",
                table: "AddressDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_ContactDetailId",
                schema: "ethos",
                table: "CareLocationDbo",
                column: "ContactDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_CreateEventId",
                schema: "ethos",
                table: "CareLocationDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_Name",
                schema: "ethos",
                table: "CareLocationDbo",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_ParentCareLocationId",
                schema: "ethos",
                table: "CareLocationDbo",
                column: "ParentCareLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_ParentProviderId",
                schema: "ethos",
                table: "CareLocationDbo",
                column: "ParentProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_Path",
                schema: "ethos",
                table: "CareLocationDbo",
                column: "CareLocationPath");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_UpdateEventId",
                schema: "ethos",
                table: "CareLocationDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationDbo_Identifiers_CareLocationDboId",
                schema: "ethos",
                table: "CareLocationDbo_Identifiers",
                column: "CareLocationDboId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationShiftDbo_CareLocationId",
                schema: "ethos",
                table: "CareLocationShiftDbo",
                column: "CareLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationShiftDbo_CreateEventId",
                schema: "ethos",
                table: "CareLocationShiftDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_CareLocationShiftDbo_UpdateEventId",
                schema: "ethos",
                table: "CareLocationShiftDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftDbo_CreateEventId",
                schema: "ethos",
                table: "DraftDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftDbo_EntityId",
                schema: "ethos",
                table: "DraftDbo",
                column: "EntityId");

            migrationBuilder.CreateIndex(
                name: "IX_DraftDbo_EntityType_EntityId",
                schema: "ethos",
                table: "DraftDbo",
                columns: new[] { "EntityType", "EntityId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DraftDbo_UpdateEventId",
                schema: "ethos",
                table: "DraftDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_EditRecord_EntityName",
                schema: "ethos",
                table: "EditRecord",
                column: "EntityName");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentDbo_CareLocationDboId",
                schema: "ethos",
                table: "EquipmentDbo",
                column: "CareLocationDboId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentDbo_CareLocationId",
                schema: "ethos",
                table: "EquipmentDbo",
                column: "CareLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentDbo_CreateEventId",
                schema: "ethos",
                table: "EquipmentDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentDbo_RoomDboId",
                schema: "ethos",
                table: "EquipmentDbo",
                column: "RoomDboId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentDbo_RoomId",
                schema: "ethos",
                table: "EquipmentDbo",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentDbo_UpdateEventId",
                schema: "ethos",
                table: "EquipmentDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentDbo_Identifiers_EquipmentDboId",
                schema: "ethos",
                table: "EquipmentDbo_Identifiers",
                column: "EquipmentDboId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceDbo_CreateEventId",
                schema: "ethos",
                table: "InsuranceDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceDbo_PatientId",
                schema: "ethos",
                table: "InsuranceDbo",
                column: "PatientId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceDbo_PhoneNumberId",
                schema: "ethos",
                table: "InsuranceDbo",
                column: "PhoneNumberId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceDbo_UpdateEventId",
                schema: "ethos",
                table: "InsuranceDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceDboStudyDbo_StudyDboId",
                schema: "ethos",
                table: "InsuranceDboStudyDbo",
                column: "StudyDboId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceHolderDataDbo_CreateEventId",
                schema: "ethos",
                table: "InsuranceHolderDataDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceHolderDataDbo_UpdateEventId",
                schema: "ethos",
                table: "InsuranceHolderDataDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerification_CoarseState",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                column: "CoarseStateDiscriminator");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerification_ExtActionTimeout",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                column: "ExternalActionTimeoutTimestamp");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerification_ProcessingStatus",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                columns: new[] { "ProcessingServiceInstanceId", "ProcessingTimeoutTimestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerification_StateName",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                column: "StateName");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerification_StudyId",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                column: "StudyId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerification_WaitingUntil",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                column: "WaitingUntil");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerificationDbo_CreateEventId",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_InsuranceVerificationDbo_UpdateEventId",
                schema: "ethos",
                table: "InsuranceVerificationDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_NoteDbo_CreateEventId",
                schema: "ethos",
                table: "NoteDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_NoteDbo_UpdateEventId",
                schema: "ethos",
                table: "NoteDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_Notifications_UniqueKey",
                schema: "ethos",
                table: "Notifications",
                column: "UniqueKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OrderDbo_CareLocationId",
                schema: "ethos",
                table: "OrderDbo",
                column: "CareLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDbo_CreateEventId",
                schema: "ethos",
                table: "OrderDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDbo_InterpretingPhysicianId",
                schema: "ethos",
                table: "OrderDbo",
                column: "InterpretingPhysicianId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDbo_PatientId",
                schema: "ethos",
                table: "OrderDbo",
                column: "PatientId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDbo_PrimaryCarePhysicianId",
                schema: "ethos",
                table: "OrderDbo",
                column: "PrimaryCarePhysicianId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDbo_ReferringPhysicianId",
                schema: "ethos",
                table: "OrderDbo",
                column: "ReferringPhysicianId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDbo_UpdateEventId",
                schema: "ethos",
                table: "OrderDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationAddressDbo_AddressId",
                schema: "ethos",
                table: "OrganizationAddressDbo",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationAddressDbo_CreateEventId",
                schema: "ethos",
                table: "OrganizationAddressDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationAddressDbo_ParentId",
                schema: "ethos",
                table: "OrganizationAddressDbo",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationAddressDbo_UpdateEventId",
                schema: "ethos",
                table: "OrganizationAddressDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationContactDetailDbo_CreateEventId",
                schema: "ethos",
                table: "OrganizationContactDetailDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationContactDetailDbo_UpdateEventId",
                schema: "ethos",
                table: "OrganizationContactDetailDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationContactPersonDbo_ContactDetailId",
                schema: "ethos",
                table: "OrganizationContactPersonDbo",
                column: "ContactDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationContactPersonDbo_CreateEventId",
                schema: "ethos",
                table: "OrganizationContactPersonDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationContactPersonDbo_ParentId",
                schema: "ethos",
                table: "OrganizationContactPersonDbo",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationContactPersonDbo_UpdateEventId",
                schema: "ethos",
                table: "OrganizationContactPersonDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationEmailDbo_CreateEventId",
                schema: "ethos",
                table: "OrganizationEmailDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationEmailDbo_ParentId_Email",
                schema: "ethos",
                table: "OrganizationEmailDbo",
                columns: new[] { "ParentId", "Email" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationEmailDbo_UpdateEventId",
                schema: "ethos",
                table: "OrganizationEmailDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationPhoneNumberDbo_CreateEventId",
                schema: "ethos",
                table: "OrganizationPhoneNumberDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationPhoneNumberDbo_ParentId",
                schema: "ethos",
                table: "OrganizationPhoneNumberDbo",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizationPhoneNumberDbo_UpdateEventId",
                schema: "ethos",
                table: "OrganizationPhoneNumberDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentConfirmationDbo_CreateEventId",
                schema: "ethos",
                table: "PatientAppointmentConfirmationDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentConfirmationDbo_PatientAppointmentId",
                schema: "ethos",
                table: "PatientAppointmentConfirmationDbo",
                column: "PatientAppointmentId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentConfirmationDbo_UpdateEventId",
                schema: "ethos",
                table: "PatientAppointmentConfirmationDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentDbo_CareLocationShiftId",
                schema: "ethos",
                table: "PatientAppointmentDbo",
                column: "CareLocationShiftId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentDbo_CreateEventId",
                schema: "ethos",
                table: "PatientAppointmentDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentDbo_RoomId",
                schema: "ethos",
                table: "PatientAppointmentDbo",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentDbo_StudyId",
                schema: "ethos",
                table: "PatientAppointmentDbo",
                column: "StudyId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentDbo_UpdateEventId",
                schema: "ethos",
                table: "PatientAppointmentDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientAppointmentDboStudyDbo_StudyDboId",
                schema: "ethos",
                table: "PatientAppointmentDboStudyDbo",
                column: "StudyDboId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientDbo_ContactDetailId",
                schema: "ethos",
                table: "PatientDbo",
                column: "ContactDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientDbo_CreateEventId",
                schema: "ethos",
                table: "PatientDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientDbo_UpdateEventId",
                schema: "ethos",
                table: "PatientDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientDbo_Identifiers_PatientDboId",
                schema: "ethos",
                table: "PatientDbo_Identifiers",
                column: "PatientDboId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientDbo_Names_PatientDboId",
                schema: "ethos",
                table: "PatientDbo_Names",
                column: "PatientDboId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientGuardianDbo_ContactDetailId",
                schema: "ethos",
                table: "PatientGuardianDbo",
                column: "ContactDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientGuardianDbo_CreateEventId",
                schema: "ethos",
                table: "PatientGuardianDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientGuardianDbo_PatientDboId",
                schema: "ethos",
                table: "PatientGuardianDbo",
                column: "PatientDboId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientGuardianDbo_UpdateEventId",
                schema: "ethos",
                table: "PatientGuardianDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientGuardianDbo_Identifiers_PatientGuardianDboId",
                schema: "ethos",
                table: "PatientGuardianDbo_Identifiers",
                column: "PatientGuardianDboId");

            migrationBuilder.CreateIndex(
                name: "IX_PatientGuardianDbo_Names_PatientGuardianDboId",
                schema: "ethos",
                table: "PatientGuardianDbo_Names",
                column: "PatientGuardianDboId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalAddressDbo_AddressId",
                schema: "ethos",
                table: "PersonalAddressDbo",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalAddressDbo_CreateEventId",
                schema: "ethos",
                table: "PersonalAddressDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalAddressDbo_ParentId",
                schema: "ethos",
                table: "PersonalAddressDbo",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalAddressDbo_UpdateEventId",
                schema: "ethos",
                table: "PersonalAddressDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalContactDetailDbo_CreateEventId",
                schema: "ethos",
                table: "PersonalContactDetailDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalContactDetailDbo_UpdateEventId",
                schema: "ethos",
                table: "PersonalContactDetailDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalEmailDbo_CreateEventId",
                schema: "ethos",
                table: "PersonalEmailDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalEmailDbo_ParentId_Email",
                schema: "ethos",
                table: "PersonalEmailDbo",
                columns: new[] { "ParentId", "Email" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PersonalEmailDbo_UpdateEventId",
                schema: "ethos",
                table: "PersonalEmailDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalEmergencyContactDbo_CreateEventId",
                schema: "ethos",
                table: "PersonalEmergencyContactDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalEmergencyContactDbo_ParentId",
                schema: "ethos",
                table: "PersonalEmergencyContactDbo",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalEmergencyContactDbo_UpdateEventId",
                schema: "ethos",
                table: "PersonalEmergencyContactDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalPhoneNumberDbo_CreateEventId",
                schema: "ethos",
                table: "PersonalPhoneNumberDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalPhoneNumberDbo_ParentId",
                schema: "ethos",
                table: "PersonalPhoneNumberDbo",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_PersonalPhoneNumberDbo_UpdateEventId",
                schema: "ethos",
                table: "PersonalPhoneNumberDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PhoneNumberWithUseDataDbo_CreateEventId",
                schema: "ethos",
                table: "PhoneNumberWithUseDataDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PhoneNumberWithUseDataDbo_UpdateEventId",
                schema: "ethos",
                table: "PhoneNumberWithUseDataDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianCareLocationRelationDbo_CareLocationId",
                schema: "ethos",
                table: "PhysicianCareLocationRelationDbo",
                column: "CareLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianCareLocationRelationDbo_CreateEventId",
                schema: "ethos",
                table: "PhysicianCareLocationRelationDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianCareLocationRelationDbo_PhysicianId",
                schema: "ethos",
                table: "PhysicianCareLocationRelationDbo",
                column: "PhysicianId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianCareLocationRelationDbo_UpdateEventId",
                schema: "ethos",
                table: "PhysicianCareLocationRelationDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianDbo_ContactDetailId",
                schema: "ethos",
                table: "PhysicianDbo",
                column: "ContactDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianDbo_CreateEventId",
                schema: "ethos",
                table: "PhysicianDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianDbo_UpdateEventId",
                schema: "ethos",
                table: "PhysicianDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianDbo_Identifiers_PhysicianDboId",
                schema: "ethos",
                table: "PhysicianDbo_Identifiers",
                column: "PhysicianDboId");

            migrationBuilder.CreateIndex(
                name: "IX_PhysicianDbo_Names_PhysicianDboId",
                schema: "ethos",
                table: "PhysicianDbo_Names",
                column: "PhysicianDboId");

            migrationBuilder.CreateIndex(
                name: "IX_ProviderDbo_ContactDetailId",
                schema: "ethos",
                table: "ProviderDbo",
                column: "ContactDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_ProviderDbo_CreateEventId",
                schema: "ethos",
                table: "ProviderDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_ProviderDbo_Name",
                schema: "ethos",
                table: "ProviderDbo",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_ProviderDbo_ParentProviderId",
                schema: "ethos",
                table: "ProviderDbo",
                column: "ParentProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_ProviderDbo_UpdateEventId",
                schema: "ethos",
                table: "ProviderDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_ProviderDbo_Identifiers_ProviderDboId",
                schema: "ethos",
                table: "ProviderDbo_Identifiers",
                column: "ProviderDboId");

            migrationBuilder.CreateIndex(
                name: "IX_RoomDbo_CareLocationId",
                schema: "ethos",
                table: "RoomDbo",
                column: "CareLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_RoomDbo_CreateEventId",
                schema: "ethos",
                table: "RoomDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_RoomDbo_Name",
                schema: "ethos",
                table: "RoomDbo",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_RoomDbo_UpdateEventId",
                schema: "ethos",
                table: "RoomDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_SchedulingConstraintDbo_CreateEventId",
                schema: "ethos",
                table: "SchedulingConstraintDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_SchedulingConstraintDbo_UpdateEventId",
                schema: "ethos",
                table: "SchedulingConstraintDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_StudyDbo_CreateEventId",
                schema: "ethos",
                table: "StudyDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_StudyDbo_OrderId",
                schema: "ethos",
                table: "StudyDbo",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_StudyDbo_UpdateEventId",
                schema: "ethos",
                table: "StudyDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianAppointmentDbo_CareLocationShiftId",
                schema: "ethos",
                table: "TechnicianAppointmentDbo",
                column: "CareLocationShiftId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianAppointmentDbo_CreateEventId",
                schema: "ethos",
                table: "TechnicianAppointmentDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianAppointmentDbo_RoomId",
                schema: "ethos",
                table: "TechnicianAppointmentDbo",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianAppointmentDbo_StudyId",
                schema: "ethos",
                table: "TechnicianAppointmentDbo",
                column: "StudyId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianAppointmentDbo_TechnicianId",
                schema: "ethos",
                table: "TechnicianAppointmentDbo",
                column: "TechnicianId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianAppointmentDbo_UpdateEventId",
                schema: "ethos",
                table: "TechnicianAppointmentDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianCareLocationRelationDbo_CareLocationId",
                schema: "ethos",
                table: "TechnicianCareLocationRelationDbo",
                column: "CareLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianCareLocationRelationDbo_CreateEventId",
                schema: "ethos",
                table: "TechnicianCareLocationRelationDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianCareLocationRelationDbo_TechnicianId",
                schema: "ethos",
                table: "TechnicianCareLocationRelationDbo",
                column: "TechnicianId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianCareLocationRelationDbo_UpdateEventId",
                schema: "ethos",
                table: "TechnicianCareLocationRelationDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianDbo_ContactDetailId",
                schema: "ethos",
                table: "TechnicianDbo",
                column: "ContactDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianDbo_CreateEventId",
                schema: "ethos",
                table: "TechnicianDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianDbo_UpdateEventId",
                schema: "ethos",
                table: "TechnicianDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianDbo_Identifiers_TechnicianDboId",
                schema: "ethos",
                table: "TechnicianDbo_Identifiers",
                column: "TechnicianDboId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianDbo_Names_TechnicianDboId",
                schema: "ethos",
                table: "TechnicianDbo_Names",
                column: "TechnicianDboId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianRoleDbo_CreateEventId",
                schema: "ethos",
                table: "TechnicianRoleDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianRoleDbo_TechnicianId",
                schema: "ethos",
                table: "TechnicianRoleDbo",
                column: "TechnicianId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianRoleDbo_UpdateEventId",
                schema: "ethos",
                table: "TechnicianRoleDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianShiftPreferenceDbo_CreateEventId",
                schema: "ethos",
                table: "TechnicianShiftPreferenceDbo",
                column: "CreateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianShiftPreferenceDbo_TechnicianId",
                schema: "ethos",
                table: "TechnicianShiftPreferenceDbo",
                column: "TechnicianId");

            migrationBuilder.CreateIndex(
                name: "IX_TechnicianShiftPreferenceDbo_UpdateEventId",
                schema: "ethos",
                table: "TechnicianShiftPreferenceDbo",
                column: "UpdateEventId");

            migrationBuilder.CreateIndex(
                name: "IX_UserNotifications_NotificationId",
                schema: "ethos",
                table: "UserNotifications",
                column: "NotificationId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId",
                schema: "ethos",
                table: "UserRoles",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_ExternalId",
                schema: "ethos",
                table: "Users",
                column: "ExternalId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkflowEntityLinkDbo_EntityType_EntityId",
                schema: "ethos",
                table: "WorkflowEntityContexts",
                columns: new[] { "EntityType", "EntityId" });

            migrationBuilder.CreateIndex(
                name: "IX_WorkflowEntityLinkDbo_InstanceId",
                schema: "ethos",
                table: "WorkflowEntityContexts",
                column: "InstanceId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuditLogs",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "CareLocationDbo_Identifiers",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "DraftDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "EquipmentDbo_Identifiers",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "FileMetadataDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "InsuranceDboStudyDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "InsuranceVerificationDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "NoteDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "OrganizationAddressDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "OrganizationContactPersonDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "OrganizationEmailDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "OrganizationPhoneNumberDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientAppointmentConfirmationDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientAppointmentDboStudyDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientDbo_Demographics",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientDbo_Identifiers",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientDbo_Names",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientGuardianDbo_Demographics",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientGuardianDbo_Identifiers",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientGuardianDbo_Names",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PersonalAddressDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PersonalEmailDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PersonalEmergencyContactDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PersonalPhoneNumberDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PhysicianCareLocationRelationDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PhysicianDbo_Demographics",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PhysicianDbo_Identifiers",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PhysicianDbo_Names",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "ProviderDbo_Identifiers",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "SchedulingConstraintDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianAppointmentDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianCareLocationRelationDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianDbo_Demographics",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianDbo_Identifiers",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianDbo_Names",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianDbo_Qualifications",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianRoleDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianShiftPreferenceDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "UserNotifications",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "UserRoles",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "WorkflowDraftTransitions",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "WorkflowEntityContexts",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "WorkflowInstanceTransitions",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "EquipmentDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "InsuranceDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientAppointmentDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientGuardianDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "AddressDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "TechnicianDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "Notifications",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "Roles",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "Users",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "WorkflowInstances",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "InsuranceHolderDataDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PhoneNumberWithUseDataDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "CareLocationShiftDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "RoomDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "StudyDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "OrderDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "CareLocationDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PatientDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PhysicianDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "ProviderDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "PersonalContactDetailDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "OrganizationContactDetailDbo",
                schema: "ethos");

            migrationBuilder.DropTable(
                name: "EditRecord",
                schema: "ethos");
        }
    }
}
