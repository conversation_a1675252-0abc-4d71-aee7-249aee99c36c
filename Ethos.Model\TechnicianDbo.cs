using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class TechnicianQualificationDbo // Suitable for OwnsOne
{
    public required long QualificationId { get; set; }
    public required DateOnly? DateObtained { get; set; }
    public required DateOnly? DateExpires { get; set; }

    // Configure method used by OwnsOne
    public static void Configure(OwnedNavigationBuilder<TechnicianDbo, TechnicianQualificationDbo> builder)
    {
        builder.Property(p => p.QualificationId).IsRequired();
        builder.Property(p => p.DateObtained).IsRequired(false);
        builder.Property(p => p.DateExpires).IsRequired(false);
        // Validation > 0 etc., in application layer
    }
}

public class TechnicianDbo : IAuditableEntity<TechnicianDbo>
{
    public virtual ICollection<PersonNameDbo> Names { get; set; } = new List<PersonNameDbo>();
    public DemographicsDbo? Demographics { get; set; }
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    public Guid? ContactDetailId { get; set; }
    public PersonalContactDetailDbo? ContactDetail { get; set; } = null!;
    
    public virtual ICollection<TechnicianQualificationDbo> Qualifications { get; set; } = new List<TechnicianQualificationDbo>();
    
    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<TechnicianDbo>(Register);

    public new static void Register(EntityTypeBuilder<TechnicianDbo> entity)
    {
        IAuditableEntity<TechnicianDbo>.Register(entity);
        
        entity.OwnsMany(p => p.Names, names =>
        {
            names.ToTable($"{nameof(TechnicianDbo)}_{nameof(Names)}", IEntity.DefaultSchema);
            names.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            names.Property<int>("Id").ValueGeneratedOnAdd();
            names.HasKey("Id");
            PersonNameDbo.Configure(names);
        });
        
        entity.OwnsOne(p => p.Demographics, builder =>
        {
            builder.ToTable($"{nameof(TechnicianDbo)}_{nameof(Demographics)}", IEntity.DefaultSchema);
            builder.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            DemographicsDbo.Configure(builder);
        });
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(TechnicianDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.OwnsMany(p => p.Qualifications, qualifications =>
        {
            qualifications.ToTable($"{nameof(TechnicianDbo)}_{nameof(Qualifications)}", IEntity.DefaultSchema);
            qualifications.WithOwner().HasForeignKey($"{nameof(TechnicianDbo)}Id");
            TechnicianQualificationDbo.Configure(qualifications);
        });
        
        entity.HasOne(s => s.ContactDetail)
            .WithMany()
            .HasForeignKey("ContactDetailId")
            .IsRequired(false)
            .HasPrincipalKey(c => c.Id);
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(TechnicianQ.WithId), "WithId")]
[JsonDerivedType(typeof(TechnicianQ.WithGivenName), "WithGivenName")]
[JsonDerivedType(typeof(TechnicianQ.WithLastName), "WithLastName")]
[JsonDerivedType(typeof(TechnicianQ.WithApproximateFullName), "WithApproximateFullName")]
[JsonDerivedType(typeof(TechnicianQ.WithIdentifier), "WithIdentifier")]
[JsonDerivedType(typeof(TechnicianQ.WithEmail), "WithEmail")]
[JsonDerivedType(typeof(TechnicianQ.WithPhoneNumber), "WithPhoneNumber")]
[JsonDerivedType(typeof(TechnicianQ.WithBirthSex), "WithBirthSex")]
[JsonDerivedType(typeof(TechnicianQ.WithGender), "WithGender")]
[JsonDerivedType(typeof(TechnicianQ.WithMaritalStatus), "WithMaritalStatus")]
[JsonDerivedType(typeof(TechnicianQ.WithRace), "WithRace")]
[JsonDerivedType(typeof(TechnicianQ.WithEthnicity), "WithEthnicity")]
[JsonDerivedType(typeof(TechnicianQ.WithDateOfBirth), "WithDateOfBirth")]
// Add other technician specific searches if needed (e.g., WithCredentials?)
public abstract record TechnicianQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : TechnicianQ;
    public sealed record WithGivenName(string Value) : TechnicianQ;
    public sealed record WithLastName(string Value) : TechnicianQ;
    public sealed record WithApproximateFullName(string Value) : TechnicianQ;
    
    public sealed record WithIdentifier(string System, string Value) : TechnicianQ;
    public sealed record WithEmail(string Email) : TechnicianQ;
    public sealed record WithPhoneNumber(string PhoneNumber) : TechnicianQ;
    
    public sealed record WithBirthSex(long? Value) : TechnicianQ;
    public sealed record WithGender(long? Value) : TechnicianQ;
    public sealed record WithMaritalStatus(long? Value) : TechnicianQ;
    public sealed record WithRace(long? Value) : TechnicianQ;
    public sealed record WithEthnicity(long? Value) : TechnicianQ;
    public sealed record WithDateOfBirth(DateOnly? Lower, DateOnly? Upper) : TechnicianQ; // Inclusive range

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId wid => Expression.Equal(Expression.Property(self, nameof(TechnicianDbo.Id)), Expression.Constant(wid.Id)),
            WithGivenName       wfn => QueryExpressions.BuildFirstNamePredicate<TechnicianDbo>(wfn.Value, self),
            WithLastName        wln => QueryExpressions.BuildLastNamePredicate<TechnicianDbo>(wln.Value, self),
            WithApproximateFullName wan => QueryExpressions.BuildApproximateNamePredicate<TechnicianDbo>(wan.Value, self),
            
            WithIdentifier      wnp => QueryExpressions.BuildIdentifierPredicate(wnp.System, wnp.Value, self),
            WithEmail           we => QueryExpressions.HasEmailPredicate<TechnicianDbo>(self, we.Email),
            WithPhoneNumber     wpn => QueryExpressions.HasPhoneNumberPredicate<TechnicianDbo>(self, wpn.PhoneNumber),
            
            WithDateOfBirth withDob => QueryExpressions.BuildWithDateOfBirthPredicate<TechnicianDbo>(self, withDob.Lower, withDob.Upper),
            WithBirthSex        wbs => QueryExpressions.HasDemographicsFieldPredicate<TechnicianDbo>(self, nameof(DemographicsDbo.SexId), wbs.Value),
            WithGender          wg => QueryExpressions.HasDemographicsFieldPredicate<TechnicianDbo>(self, nameof(DemographicsDbo.GenderId), wg.Value),
            WithMaritalStatus   wms => QueryExpressions.HasDemographicsFieldPredicate<TechnicianDbo>(self, nameof(DemographicsDbo.MaritalStatusId), wms.Value),
            WithRace            wr => QueryExpressions.HasDemographicsFieldPredicate<TechnicianDbo>(self, nameof(DemographicsDbo.RaceId), wr.Value),
            WithEthnicity       we => QueryExpressions.HasDemographicsFieldPredicate<TechnicianDbo>(self, nameof(DemographicsDbo.EthnicityId), we.Value),
            
            _ => throw new NotSupportedException($"Unsupported TechnicianQ literal type: {this.GetType().Name}")
        };
    }
}