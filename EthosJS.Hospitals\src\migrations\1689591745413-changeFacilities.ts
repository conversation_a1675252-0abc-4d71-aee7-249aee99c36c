import { MigrationInterface, QueryRunner } from 'typeorm';

export class changeFacilities1689591745413 implements MigrationInterface {
    name = 'changeFacilities1689591745413'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technicians" RENAME COLUMN "number_of_beds" TO "capacity"');
      await queryRunner.query('ALTER TABLE "facilities" RENAME COLUMN "number_of_beds" TO "capacity"');
      await queryRunner.query('ALTER TABLE "schedules" ADD "facility_id" integer NOT NULL');
      await queryRunner.query('ALTER TABLE "technicians" ALTER COLUMN "capacity" SET NOT NULL');
      await queryRunner.query('ALTER TABLE "schedules" ADD CONSTRAINT "FK_3cf09e9a62a7f3f0bcb61705640" FOREIGN KEY ("facility_id") REFERENCES "facilities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "schedules" DROP CONSTRAINT "FK_3cf09e9a62a7f3f0bcb61705640"');
      await queryRunner.query('ALTER TABLE "technicians" ALTER COLUMN "capacity" DROP NOT NULL');
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "facility_id"');
      await queryRunner.query('ALTER TABLE "facilities" RENAME COLUMN "capacity" TO "number_of_beds"');
      await queryRunner.query('ALTER TABLE "technicians" RENAME COLUMN "capacity" TO "number_of_beds"');
    }

}
