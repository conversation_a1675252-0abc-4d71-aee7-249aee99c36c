using Ethos.Workflows.Api.Analysis;
using Xunit.Abstractions;
using Xunit.Sdk;

namespace Ethos.Workflows.Tests;

public class RuleExtraction(ITestOutputHelper outputHelper)
{
    [Fact]
    public void Extract()
    {
        using var file = new StreamWriter("D:\\ws\\persante\\repo\\validation_rules.csv");
        file.WriteLine("\"Entity\",\"Json Path\",\"Data Type\",\"Rule\",\"Is Hard Constraint\"");
        foreach (var controller in Helpers.GetAllEntityControllers())
        {
            var rules = ValidationRuleExtractor.ExtractValidationRules(controller.InputDtoType, s => outputHelper.WriteLine(s));
            
            // Print rules
            foreach (var rule in rules)
            {
                var controllerName = controller.Name[..^"Controller".Length];
                var isHard = rule.IsHard ? 1 : 0;
                file.WriteLine($"\"{controllerName}\", \"{rule.JsonPath}\",\"{rule.DataType}\",\"{rule.Rule}\",\"{isHard}\"");
            }
        }
    }
}