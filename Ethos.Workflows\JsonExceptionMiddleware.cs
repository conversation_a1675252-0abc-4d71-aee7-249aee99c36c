using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Http;

namespace Ethos.Workflows;

public sealed record ExceptionDto
{
    public required string Type { get; set; }
    public required string Message { get; set; }
    public required string? StackTrace { get; set; }
    public ExceptionDto? InnerException { get; set; }
}

public class JsonExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IHostEnvironment _env;

    public JsonExceptionMiddleware(RequestDelegate next, IHostEnvironment env)
    {
        _next = next;
        _env = env;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private static ExceptionDto? FormatException(Exception? exception)
    {
        if (exception == null) return null;
        
        return new ExceptionDto
        {
            Type = exception.GetType().Name,
            Message = exception.Message,
            StackTrace = exception.StackTrace,
            InnerException = FormatException(exception.InnerException)
        };
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";
        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

        var response = FormatException(exception);

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        });

        await context.Response.WriteAsync(json);
    }
}