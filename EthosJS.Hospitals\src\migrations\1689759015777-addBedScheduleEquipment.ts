import { MigrationInterface, QueryRunner } from 'typeorm';

export class addBedScheduleEquipment1689759015777 implements MigrationInterface {
    name = 'addBedScheduleEquipment1689759015777'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('CREATE TABLE "bed_schedule_equipments" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "equipment_id" integer NOT NULL, "bed_schedule_id" integer NOT NULL, "count" integer NOT NULL, CONSTRAINT "PK_9dece7a7e38a1bf046a5cf232bd" PRIMARY KEY ("id"))');
      await queryRunner.query('ALTER TABLE "bed_schedule_equipments" ADD CONSTRAINT "FK_6dc80d849b5d08149e8a8a254cb" FOREIGN KEY ("equipment_id") REFERENCES "equipments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "bed_schedule_equipments" ADD CONSTRAINT "FK_67b07d6d90e3beb889d3a65856c" FOREIGN KEY ("bed_schedule_id") REFERENCES "bed_schedules"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "bed_schedule_equipments" DROP CONSTRAINT "FK_67b07d6d90e3beb889d3a65856c"');
      await queryRunner.query('ALTER TABLE "bed_schedule_equipments" DROP CONSTRAINT "FK_6dc80d849b5d08149e8a8a254cb"');
      await queryRunner.query('DROP TABLE "bed_schedule_equipments"');
    }

}
