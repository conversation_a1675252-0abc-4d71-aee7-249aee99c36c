namespace Ethos.Workflows.Files;

public static class FileUploadServiceExtensions
{
    public static IServiceCollection AddFileUploadServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<StorageOptions>(configuration.GetSection(StorageOptions.SectionName));
        services.Configure<JwtTokenOptions>(configuration.GetSection(JwtTokenOptions.SectionName));
        services.Configure<FileUploadOptions>(configuration.GetSection(FileUploadOptions.SectionName));

        services.AddHttpClient();
        services.AddHttpContextAccessor();

        var storageOptions = configuration.GetSection(StorageOptions.SectionName).Get<StorageOptions>();
        if (storageOptions?.Provider == StorageProvider.AzureBlob)
        {
            services.AddSingleton<IFileStorageService, AzureBlobStorageService>();
            Console.WriteLine("Registered AzureBlobStorageService");
        }
        else
        {
            services.AddSingleton<IFileStorageService, LocalFileStorageService>();
            Console.WriteLine("Registered LocalFileStorageService");
        }

        services.AddSingleton<IUploadTokenService, JwtUploadTokenService>();
        services.AddScoped<IFileValidationService, FileValidationService>();

        return services;
    }
}