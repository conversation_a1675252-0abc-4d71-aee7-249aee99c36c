using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Ethos.Auth;
using Ethos.Model;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Database;

public class ApplicationUser
{
    // Primary key in your local DB
    public Guid Id { get; set; }

    // OID or unique ID from external identity provider (Azure AD B2C)
    public string ExternalId { get; set; } = null!;

    public string Email { get; set; } = null!;
    public string? PhoneNumber { get; set; }
        
    // You might store the user's workflow roles here
    // or in a separate linking table
    public ICollection<Role> Roles { get; set; } = new List<Role>();
        
    // Timestamps, etc.
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class Role
{
    public int Id { get; set; }
    public string Name { get; set; } = default!;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public ICollection<ApplicationUser> Users { get; set; } = new List<ApplicationUser>();
}

public class Notification
{
    public Guid Id { get; set; }
    public string UniqueKey { get; set; } = default!;
    public string Severity { get; set; } = default!;
    public string Category { get; set; } = default!;
    public string Message { get; set; } = default!;
    public DateTime Timestamp { get; set; }
    public bool IsHighUrgency { get; set; }
    public int EscalationLevel { get; set; }
}

public class UserNotification
{
    public int Id { get; set; }
    public Guid NotificationId { get; set; }
    public Notification Notification { get; set; } = default!;
    public Guid UserId { get; set; }            // from external identity
    public bool IsRead { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class AppDbContext : DbContextBase
{
    public DbSet<EditRecordDbo> EditRecords { get; set; } = default!;
    
    public DbSet<ApplicationUser> Users { get; set; } = default!;
    public DbSet<WorkflowInstanceDbo> WorkflowInstances { get; set; } = default!;
    public DbSet<WorkflowTransitionDbo> WorkflowInstanceTransitions { get; set; } = default!;
    public DbSet<WorkflowDraftTransitionDbo> WorkflowDraftTransitions { get; set; } = default!;
    public DbSet<WorkflowEntityLinkDbo> WorkflowEntityLinks { get; set; } = default!;
    
    public DbSet<Role> Roles { get; set; } = default!;
    
    public DbSet<AddressDbo> AddressDetails { get; set; } = default!;
    public DbSet<PersonalContactDetailDbo> ContactDetails { get; set; } = default!;
    public DbSet<PatientGuardianDbo> Guardians { get; set; } = default!;
    public DbSet<PatientDbo> Patients { get; set; } = default!;
    public DbSet<StudyDbo> Studies { get; set; } = default!;
    
    public DbSet<Notification> Notifications { get; set; } = default!;
    public DbSet<UserNotification> UserNotifications { get; set; } = default!;
    public DbSet<AuditLogDbo> AuditLogs { get; set; } = default!;

    public AppDbContext(DbContextOptions<AppDbContext> options, IEthosUserContextProvider contextProvider)
        : base(options, contextProvider) { }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // -----------------------------------------------------------
        // 1. ApplicationUser
        // -----------------------------------------------------------
        modelBuilder.Entity<ApplicationUser>(entity =>
        {
            // Optional: specify DB table name (if you don't want EF's default "ApplicationUsers")
            entity.ToTable("Users"); 

            // Primary Key (if not using the default of 'Id')
            entity.HasKey(u => u.Id);

            // Unique constraint or index on ExternalId (the OID from Azure AD B2C)
            entity.HasIndex(u => u.ExternalId).IsUnique();

            // Make Email required
            entity.Property(u => u.Email)
                .HasMaxLength(200)
                .IsRequired();

            // Timestamps
            entity.Property(u => u.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");  // optional
            entity.Property(u => u.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");  // optional

            // Many-to-many relationship with Role (EF Core 5+ approach)
            // If your ApplicationUser has: public ICollection<Role> Roles { get; set; }
            // and Role has: public ICollection<ApplicationUser> Users { get; set; }
            entity.HasMany(u => u.Roles)
                .WithMany(r => r.Users)  // see config in Roles block below
                .UsingEntity<Dictionary<string, object>>(
                    "UserRoles",    // name of the join table
                    right => right
                        .HasOne<Role>()
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .HasConstraintName("FK_UserRoles_Roles_RoleId")
                        .OnDelete(DeleteBehavior.Cascade),
                    left => left
                        .HasOne<ApplicationUser>()
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_UserRoles_Users_UserId")
                        .OnDelete(DeleteBehavior.Cascade));
        });


        // -----------------------------------------------------------
        // 2. Role
        // -----------------------------------------------------------
        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("Roles");
            entity.HasKey(r => r.Id);

            entity.Property(r => r.Name)
                .HasMaxLength(200)
                .IsRequired();

            entity.Property(r => r.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(r => r.UpdatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");

            // If you want to define the other side of the many-to-many:
            // public ICollection<ApplicationUser> Users { get; set; } 
            // you can also configure it here:
            // entity.HasMany(r => r.Users)
            //       .WithMany(u => u.Roles)
            //       .UsingEntity(...); // usually EF won't require repeating, but it can be symmetrical
        });

        // -----------------------------------------------------------
        // 5. Notification
        // -----------------------------------------------------------
        modelBuilder.Entity<Notification>(entity =>
        {
            entity.ToTable("Notifications");
            entity.HasKey(n => n.Id);

            // Unique key for adjusting priorities or lookups
            entity.Property(n => n.UniqueKey)
                .HasMaxLength(255)
                .IsRequired();

            entity.HasIndex(n => n.UniqueKey)
                .IsUnique();

            entity.Property(n => n.Severity)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(n => n.Category)
                .HasMaxLength(100)
                .IsRequired();

            entity.Property(n => n.Message)
                .IsRequired();

            entity.Property(n => n.Timestamp)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");

            // isHighUrgency, escalation level, etc. can be left as defaults
        });


        // -----------------------------------------------------------
        // 6. UserNotification
        // -----------------------------------------------------------
        modelBuilder.Entity<UserNotification>(entity =>
        {
            entity.ToTable("UserNotifications");
            entity.HasKey(un => un.Id);

            // Relationship to Notification
            entity.HasOne(un => un.Notification)
                .WithMany() // or define a collection in Notification
                .HasForeignKey(un => un.NotificationId)
                .OnDelete(DeleteBehavior.Cascade);

            // If your ApplicationUser is the user reference:
            // userId is a Guid referencing the local user
            entity.Property(un => un.UserId)
                .IsRequired();

            // Possibly store read/unread status
            entity.Property(un => un.IsRead)
                .IsRequired()
                .HasDefaultValue(false);

            entity.Property(un => un.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");
        });


        // -----------------------------------------------------------
        // 7. AuditLog
        // -----------------------------------------------------------
        modelBuilder.Entity<AuditLogDbo>(entity =>
        {
            entity.ToTable("AuditLogs");
            entity.HasKey(a => a.Id);

            entity.Property(a => a.TransitionName)
                .HasMaxLength(200)
                .IsRequired();

            entity.Property(a => a.Timestamp)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");

            // store metadata as JSON if you wish
            entity.Property(a => a.Metadata)
                .HasColumnType("jsonb"); // if you prefer JSONB in Postgres
        });
    }
    
    public async Task<List<ApplicationUser>> GetUsersByRolesAsync(IEnumerable<string> roleNames)
    {
        return await Users
            .Include(u => u.Roles)
            .Where(u => u.Roles.Any(r => roleNames.Contains(r.Name)))
            .ToListAsync();
    }
}