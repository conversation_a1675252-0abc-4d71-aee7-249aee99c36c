// THIS FILE IS AUTO‑GENERATED
// DO NOT EDIT

using System.Net;
using System.Net.Http.Json;
using Ethos.Model.Types;
using Ethos.Workflows.Api;
using Ethos.Workflows.Workflow.AddNewOrder;

namespace Ethos.Workflows.Workflow.AddNewOrder;

public sealed class AddNewOrderHttpClient
{
    private readonly System.Net.Http.HttpClient _http;
    public AddNewOrderHttpClient(System.Net.Http.HttpClient http) => _http = http;

    private static async Task<T> ReadOrThrowAsync<T>(HttpResponseMessage rsp)
    {
        if (rsp.IsSuccessStatusCode)
            return (await rsp.Content.ReadFromJsonAsync<T>())!;
        var txt = await rsp.Content.ReadAsStringAsync();
        throw new WorkflowApiException(rsp.StatusCode, txt);
    }
    
    private static async Task ThrowIfNotSuccessAsync(HttpResponseMessage rsp)
    {
        if (!rsp.IsSuccessStatusCode)
            throw new WorkflowApiException(rsp.StatusCode,
                                        await rsp.Content.ReadAsStringAsync());
    }

    public async Task<StartAddNewOrderResponse> StartAsync(StartAddNewOrderRequest body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<StartAddNewOrderResponse>(await _http.PostAsJsonAsync("api/add-new-order/start", body, ct));
    }

    public async Task<AddNewOrderState?> GetStateAsync(Guid id, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<AddNewOrderState?>(await _http.GetAsync($"api/add-new-order/state/{id}", ct));
    }

    public async Task RewindAsync(Guid id, string state, CancellationToken ct = default)
    {
        await ThrowIfNotSuccessAsync(await _http.PostAsync($"api/add-new-order/rewind?id={id}&stateName={state}", null, ct));
    }

    public async Task<IReadOnlyList<Guid>> ListAsync(IReadOnlyDictionary<string, Guid>? links = null, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<IReadOnlyList<Guid>>(await _http.PostAsJsonAsync($"api/add-new-order/list", links, ct));
    }

    public async Task<WorkflowResponse<AddNewOrderState.AddedStudy>> AddStudyAsync(WorkflowRequest<IAddNewOrderRequest.AddStudy> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewOrderState.AddedStudy>>(await _http.PostAsJsonAsync($"api/add-new-order/add-study", body, ct));
    }

    public async Task<Guid> AddStudyDraftAsync(DraftRequest<IAddNewOrderRequest.AddStudy> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-order/add-study/draft", body, ct));
    }

    public async Task<ValidationResult> AddStudyValidateAsync(WorkflowRequest<IAddNewOrderRequest.AddStudy> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-order/add-study/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewOrderState.AddedCareLocation>> AddCareLocationAsync(WorkflowRequest<IAddNewOrderRequest.AddCareLocation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewOrderState.AddedCareLocation>>(await _http.PostAsJsonAsync($"api/add-new-order/add-care-location", body, ct));
    }

    public async Task<Guid> AddCareLocationDraftAsync(DraftRequest<IAddNewOrderRequest.AddCareLocation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-order/add-care-location/draft", body, ct));
    }

    public async Task<ValidationResult> AddCareLocationValidateAsync(WorkflowRequest<IAddNewOrderRequest.AddCareLocation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-order/add-care-location/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewOrderState.AddedPhysicians>> AddPhysiciansAsync(WorkflowRequest<IAddNewOrderRequest.AddPhysicians> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewOrderState.AddedPhysicians>>(await _http.PostAsJsonAsync($"api/add-new-order/add-physicians", body, ct));
    }

    public async Task<Guid> AddPhysiciansDraftAsync(DraftRequest<IAddNewOrderRequest.AddPhysicians> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-order/add-physicians/draft", body, ct));
    }

    public async Task<ValidationResult> AddPhysiciansValidateAsync(WorkflowRequest<IAddNewOrderRequest.AddPhysicians> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-order/add-physicians/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewOrderState.OrderSubmitted>> ReviewAndSubmitOrderAsync(WorkflowRequest<IAddNewOrderRequest.ReviewAndSubmitOrder> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewOrderState.OrderSubmitted>>(await _http.PostAsJsonAsync($"api/add-new-order/review-and-submit-order", body, ct));
    }

    public async Task<Guid> ReviewAndSubmitOrderDraftAsync(DraftRequest<IAddNewOrderRequest.ReviewAndSubmitOrder> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-order/review-and-submit-order/draft", body, ct));
    }

    public async Task<ValidationResult> ReviewAndSubmitOrderValidateAsync(WorkflowRequest<IAddNewOrderRequest.ReviewAndSubmitOrder> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-order/review-and-submit-order/validate", body, ct));
    }

}
