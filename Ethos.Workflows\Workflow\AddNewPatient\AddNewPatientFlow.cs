using Ethos.Model;
using Ethos.Workflows.Database;
using Ethos.ReferenceData.Client;

namespace Ethos.Workflows.Workflow.AddNewPatient;

public class AddNewPatientFlow : IAddNewPatientFlow
{
    private readonly AppDbContext _dbContext;

    public AddNewPatientFlow(AppDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<AddNewPatientState.Committed> AddClinicalInformation(
        FlowContext<AddNewPatientState.AddedGuardians> ctx,
        IReadOnlyList<int> clinicalConsiderations,
        SchedulingPreferences schedulingPreferences,
        string? additionalPatientNotes, 
        string? caregiverInformation)
    {
        var targetId = ctx.EntityLinks["Patient"];

        List<IdentifierDbo> identifiers = new();
        
        if (ctx.OldState.PatientInformation.Ssn != null)
            identifiers.Add(new IdentifierDbo {
                System = "SSN", 
                Value = ctx.OldState.PatientInformation.Ssn
            });
        
        if (ctx.OldState.PatientInformation.Mrn != null) 
            identifiers.Add(new IdentifierDbo
            {
                System = "MRN", 
                Value = ctx.OldState.PatientInformation.Mrn
            });

        IList<PersonNameDbo> personNames = [new PersonNameDbo()
        {
            LastName = ctx.OldState.PatientInformation.LastName,
            FirstName = ctx.OldState.PatientInformation.FirstName,
            MiddleName = ctx.OldState.PatientInformation.MiddleName,
            PrefixId = ctx.OldState.PatientInformation.Prefix,
            SuffixId = ctx.OldState.PatientInformation.Suffix,
        }];

        var physicalMeasurements = new PhysicalMeasurementsDbo()
        {
            Bmi = (decimal) ctx.OldState.PhysicalMeasurements.Bmi,
            HeightInches = (decimal) ctx.OldState.PhysicalMeasurements.HeightInches,
            NeckSize = (decimal) ctx.OldState.PhysicalMeasurements.NeckSize,
            WeightPounds = (decimal) ctx.OldState.PhysicalMeasurements.WeightPounds,
        };
        
        var patientPhoneNumbers = ctx.OldState.ContactInformation.PhoneNumbers.Select(pn => new PersonalPhoneNumberDbo
        {
            PhoneNumber = pn.Value,
            Type = pn.Type,
            AllowsVoice = pn.AllowsVoice,
            AllowsTextMessages = pn.AllowsSMS,
            IsPreferred = pn.IsPreferred,
            PreferredTimeId = pn.PreferredTime
        }).ToList();
        
        var patientEmails = ctx.OldState.ContactInformation.Emails.Select(e => new PersonalEmailDbo
        {
            Email = e.Value,
            Use = e.Use,
            IsPreferred = e.IsPreferred
        }).ToList();
        
        var patientEmergencyContacts = ctx.OldState.ContactInformation.EmergencyContacts.Select(ec => new PersonalEmergencyContactDbo()
        {
            Name = new PersonNameDbo()
            {
                FirstName = ec.FirstName,
                LastName = ec.LastName,
                MiddleName = ec.MiddleName,
                SuffixId = ec.Suffix,
                PrefixId = ec.Prefix,
            },
            RelationshipId = ec.Relationship,
            ContactInformation = ec.ContactInformation
        }).ToList();

        // TODO: Check
        var insuranceEntities = ctx.OldState.Insurances.Select(i =>
        {
            PhoneNumberWithUseDataDbo? phoneNumber;

            if (i.PhoneNumber != null)
            {
                phoneNumber = new PhoneNumberWithUseDataDbo()
                {
                    PhoneNumber = i.PhoneNumber?.PhoneNumber,
                    PhoneNumberTypeId = 0,
                };
            }
            else
            {
                phoneNumber = null;
            }

            return new InsuranceDbo
            {
                GroupNumber = i.GroupNumber,
                PolicyId = i.PolicyId,
                InsuranceId = i.InsuranceId,
                MemberId = i.MemberId,
                InsuranceCarrier = i.InsuranceCarrier,
                InsuranceHolder = new InsuranceHolderDataDbo()
                {

                },
                PhoneNumber = phoneNumber,
            };
        }).ToList();
        
        // TODO:
        // IReadOnlyList<AddressWithUseType> PhysicalAddresses,
        // IReadOnlyList<AddressWithUseType> BillingAddresses,
        // IReadOnlyList<AddressWithUseType> DeliveryAddresses,
        // IReadOnlyList<Guardian> Guardians
        
        _dbContext.Set<PatientDbo>().Add(new PatientDbo
        {
            Id = targetId,
            Names = personNames,
            Demographics = new DemographicsDbo() {
                DateOfBirth = ctx.OldState.Demographics.DateOfBirth,
                GenderId = ctx.OldState.Demographics.Gender,
                SexId = ctx.OldState.Demographics.BirthSex,
                MaritalStatusId = ctx.OldState.Demographics.MaritalStatus,
                RaceId = ctx.OldState.Demographics.Race,
                EthnicityId = ctx.OldState.Demographics.Ethnicity,
            },
            Identifiers = identifiers,
            PhysicalMeasurements = physicalMeasurements,
            ContactDetail = new PersonalContactDetailDbo() {
                PhoneNumbers = patientPhoneNumbers,
                Emails = patientEmails,
                EmergencyContacts = patientEmergencyContacts,
            },
            Insurances = insuranceEntities,
            ClinicalConsiderations = clinicalConsiderations.Select(i => (long) i).ToList(),
            PreferredWeekdays = schedulingPreferences.PreferredDaysOfWeek.Select(d => (long) d).ToList(),
            TechnicianPreference = schedulingPreferences.TechnicianPreference,
        });

        if (additionalPatientNotes != null)
            _dbContext.Set<NoteDbo>().Add(new NoteDbo()
            {
                Content = additionalPatientNotes,
                EntityId = targetId,
                EntityType = EntityType.Patient,
            });
        
        // TODO: Caregiver information
        
        await _dbContext.SaveChangesAsync();
        
        return new AddNewPatientState.Committed(targetId);
    }
}
