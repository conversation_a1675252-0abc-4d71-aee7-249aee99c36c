{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=postgres;Database=postgres;Username=postgres;Password=yourpassword"}, "ApiUsageLogging": {"Enabled": false, "TrackDataUsage": true, "TenantIdRetrievalMethod": "Custom", "EventServiceUrl": "http://************:4006/api/events", "MaxUsageRecordAgeMins": 1440, "TimeBetweenUsageRecordCleanupMins": 30, "MaxOpenTransactionAgeMins": 90, "BillOpenTransactionsOnExpiration": true}, "Events": {"BaseUri": "https://goodspace.org"}, "Auth": {"Basic": {"azure": "10000.OvkwqHKf8gY2xjmCK2xMlg==.k4dHorktFIbIW1pf+MAI1/I6FfgCMFuZIyzuHQthGeg="}, "Local": {"Enabled": true, "Key": "VerySecureJWTKeyMustBeAtLeast32Chars!", "Issuer": "our-app-name", "Audience": "our-app-users"}, "Authorization": {"DebugScopes": ["Core.Role.*", "Core.Role.Read", "Core.RoleAssignment.Write", "*.*.<PERSON><PERSON>"], "DebugScopes_JSON": "[\"Core.Role.*\",\"Core.Role.Read\", \"Core.RoleAssignment.Write\", \"*.*.Admin\" ]", "DefaultAllow": false, "UseProblemDetails": true, "ProblemDetails": {"Title": "Forbidden", "Detail": "You do not have permission to access this resource."}, "ClaimNames": {"Scope": "extension_ethosScopes", "GivenName": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname", "Surname": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname", "Email": "emails", "Uuid": "http://schemas.microsoft.com/identity/claims/objectidentifier", "Products": "extension_products", "TenantId": "extension_ethosTenantId"}}, "AzureAD": {"Enabled": true, "ClientId": "a8d641a8-7112-4c1f-ad32-cf156f81459c", "TenantId": "5d067e44-b2ee-4df6-9bdb-839a7d05c039", "Instance": "https://ethoshbcustnonprod.b2clogin.com/", "Issuer": "https://ethoshbcustnonprod.b2clogin.com/5d067e44-b2ee-4df6-9bdb-839a7d05c039/v2.0/", "Policy": "B2C_1_Sign_Up_and_Sign_On"}}}