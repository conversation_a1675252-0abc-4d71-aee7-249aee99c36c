# Basic Types: String, Integer, Floating, Real (precise), Date, DateTime, Boolean, Lists (non-empty + possibly empty), Optional values (i.e. Maybe)
# Basic Functions:
#  len :: List a -> Integer
#  len :: String -> Integer
#  now :: DateTime
#  age :: Date -> Integer
#  matches :: String -> String -> Boolean
# Syntax:
#  if <condition> then <expression> else <expression>
#  *else* case can be omitted for validation rules.

- type: NonEmptyString
  description: Non-empty string
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"

- type: Sex
  description: Person's Birth Sex
  alias: Integer

- type: Gender
  description: Person's Gender
  alias: Integer

- type: MaritalStatus
  description: Marital status
  alias: Integer

- type: Race
  description: User Race
  alias: Integer

- type: Ethnicity
  description: User Ethnicity
  alias: Integer

- type: CountryName
  description: Country name
  alias: Integer

- type: StateName
  description: State name
  alias: Integer

- type: EmailUse
  description: Email use
  alias: Integer

- type: AddressUse
  description: Address use
  alias: Integer

- type: AddressType
  description: Address type
  alias: Integer

- type: NamePrefix
  description: Name prefix
  alias: Integer

- type: NameSuffix
  description: Name suffix
  alias: Integer

- type: SSN
  description: Social Security Number
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - error: InvalidSSN
      require: "matches(value, '^[0-9]{3}-[0-9]{2}-[0-9]{4}$')"
      message: "Invalid SSN format"

- type: SomeMRN
  description: Medical Record Number
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - error: InvalidMRN
      require: "matches(value, '^[a-zA-Z0-9]{1,20}$')"
      message: "Invalid MRN format"

- type: Email
  description: Email address
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - error: Email
      require: "matches(value, '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$')"
      message: "Invalid email address"

- type: BirthDate
  alias: Date
  description: Date of birth
  validation:
    - warning: DateOfBirthMightBeUnrealistic
      require: "age(value) <= 150"
      message: "Date of birth is unrealistic"
    - error: DateOfBirthIsInTheFuture
      require: "value < nowdate()"
      message: "Date of birth is in the future"

- type: NamePart
  description: Name part
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - warning: TooLong
      require: "len(value) <= 50"
      message: "Name part is too long"
    - warning: TooShort
      require: "len(value) >= 2"
      message: "Name part is too short"
    - warning: ContainsNonLatinCharacters
      require: "matches(value, '^[a-zA-Z-]+$')"
      message: "Name part contains non-latin characters"

- type: CityName
  description: City name
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - warning: TooLong
      require: "len(value) <= 50"
      message: "City name is too long"
    - warning: ContainsNonLatinCharacters
      require: "matches(value, '^[a-zA-Z-]+$')"
      message: "City name contains non-latin characters"

- type: PostalCode
  description: Postal code
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - warning: TooShort
      require: "len(value) >= 3"
      message: "Postal code is too short"
    - warning: TooLong
      require: "len(value) <= 16"
      message: "Postal code is too long"
    - warning: ContainsNonLatinCharacters
      require: "matches(value, '^[a-zA-Z0-9- ]+$')"
      message: "Postal code contains non-latin characters"

- type: Address
  description: Address
  fields:
    line1: String
    line2: String?
    city: CityName
    state: StateName
    postalCode: PostalCode
    country: CountryName
  validation:
    - error: NoAddressLine
      require: "len(line1) > 0"
      message: "At least one address line is required."
#    - error: PostalCodeUSA
#      require: 'country != "USA" || (matches(postalCode, "^[0-9]{5}$") || matches(postalCode, "^[0-9]{5}-[0-9]{4}$"))'
#      message: "Postal code must be in the format XXXXX or XXXXX-XXXX for USA addresses."
#    - error: PostalCodeCanada
#      require: 'country != "Canada" || matches(postalCode, "^[A-Za-z][0-9][A-Za-z] [0-9][A-Za-z][0-9]$")'
#      message: "Postal code must be in the format A1A 1A1 for Canadian addresses."

- type: PhoneType
  description: Phone type
  alias: Integer

- type: PhoneNumber
  description: Phone number
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - error: InvalidPhoneNumber
      require: "matches(value, '^[0-9]{3}-[0-9]{3}-[0-9]{4}$')"
      message: "Invalid phone number format"

- type: PhoneNumberWithUse
  description: Phone number with purpose
  fields:
    type: Integer # phone | fax | pager | mobile | other
    phoneNumber: PhoneNumber
    use: Integer # home | work | mobile | other
    allowsSMS: Boolean?
    allowsVoice: Boolean?
    allowsCommunication: Boolean? # whether patient/guardian consents
    extension: NonEmptyString? # phone extension, if applicable

- type: AddressWithUse
  description: Address with use
  fields:
    address: Address
    use: AddressUse

- type: TimeOfDay
  description: Time of day
  fields:
    hour: Integer
    minute: Integer
  validation:
    - error: InvalidHour
      require: "hour >= 0 && hour < 24"
      message: "Hour must be between 0 and 23"
    - error: InvalidMinute
      require: "minute >= 0 && minute < 60"
      message: "Minute must be between 0 and 59"

- type: TimeRange
  description: Time range
  fields:
    start: TimeOfDay
    end: TimeOfDay

- type: NamePartOrInitial
  description: Name part or initial
  alias: String
  validation:
    - error: ValueIsEmpty
      require: "len(value) > 0"
      message: "Value is required"
    - warning: TooLong
      require: "len(value) <= 50"
      message: "Name part is too long"
    - warning: ContainsNonLatinCharacters
      require: "matches(value, '^[a-zA-Z-]+$')"
      message: "Name part contains non-latin characters"

- type: EmergencyContactRelationship
  description: Emergency contact relationship
  alias: Integer
  validation:
    - error: ValueIsEmpty
      require: "value > 0"
      message: "Value is required"

- type: EmailWithUse
  description: Email with purpose
  fields:
    email: Email
    use: EmailUse

# - type: ContactInformation
#   description: Contact information
#   fields:
#     phoneNumbers: PhoneNumberWithUse*
#     emails: EmailWithUse*
#   validation:
#     - warning: NoPhoneNumbers
#       require: "len(phoneNumbers) > 0"
#       message: "At least one phone number is required"
#     - warning: NoEmails
#       require: "len(emails) > 0"
#       message: "At least one email is required"

- type: PersonName
  description: Person name
  fields:
    firstNames: NonEmptyString+
    lastNames: NonEmptyString+
    middleInitials: NonEmptyString*
    middleNames: NonEmptyString*
    prefixes: NonEmptyString*
    suffixes: NonEmptyString*

# - type: Guardian
#   description: Guardian information
#   fields:
#     name: PersonName
#     relationship: String
#     contactInformation: ContactInformation

- type: InsuranceHolder
  fields:
    name: NamePart
    dateOfBirth: BirthDate
    relationship: Integer

- type: Insurance
  fields:
    insuranceCarrier: Integer
    planType: Integer
    insuranceId: String?
    policyId: NonEmptyString
    groupNumber: NonEmptyString
    memberId: NonEmptyString
    insuranceHolder: InsuranceHolder?
    phoneNumber: PhoneNumberWithUse?
    email: EmailWithUse?
    address: AddressWithUse?

- type: AddressWithUseType
  fields:
    use: AddressUse
    type: AddressType
    address: Address

- type: Demographics
  fields:
    dateOfBirth: BirthDate
    gender: Gender
    birthSex: Gender
    maritalStatus: MaritalStatus
    race: Race
    ethnicity: Ethnicity

- type: Identifier
  fields:
    system: String
    value: String

- type: PhysicalMeasurements
  fields:
    heightInches: Real
    weightPounds: Real
    neckSize: Real
    bmi: Real
  validation:
    - error: HeightIsInvalid
      require: "heightInches > 0"
      message: "Height must be greater than 0"
    - warning: HeightMightBeUnrealistic
      require: "heightInches < 200"
      message: "Height should be less than 200 inches"
    - error: WeightIsInvalid
      require: "weightPounds > 0"
      message: "Weight must be greater than 0"
    - warning: WeightMightBeUnrealistic
      require: "weightPounds < 1000"
      message: "Weight should be less than 1000 lbs"
    - error: NeckSizeIsInvalid
      require: "neckSize > 0"
      message: "Neck size must be greater than 0"
    - warning: NeckSizeMightBeInvalid
      require: "neckSize > 5 && neckSize < 50"
      message: "Neck size should be between 5 and 50 inches"
    - error: BMIIsInvalid
      require: "bmi > 0"
      message: "BMI must be greater than 0"
    - warning: BMIMightBeUnrealistic
      require: "bmi > 10 && bmi < 100"
      message: "BMI should be between 10 and 100"

- entity: Patient
  description: Patient information
  fields:
    names: PersonName+
    identifiers: Identifier*
    demographics: Demographics
    physicalMeasurements: PhysicalMeasurements?

- type: GuardianType
  description: Guardian type
  alias: Integer

- type: GuardianBasicInformation
  description: Guardian information
  fields:
    guardianType: Integer
    prefix: NamePrefix?
    firstName: NamePart
    middleName: NamePartOrInitial?
    lastName: NamePart
    suffix: NameSuffix?

- type: GuardianDemographics
  description: Guardian demographics
  fields:
    gender: Gender
    dateOfBirth: BirthDate
    ssn: SSN
    relationShipToPatient: String
    idType: String
    idNumber: String
    addresses: Address*
    phoneNumbers: PhoneNumberWithUse*

- type: TechnicianPreference
  alias: Integer
  description: Technician preference

- flow: AddNewPatient
  description: Add a new patient
  inputEntities: []
  outputEntity: Patient
  steps:
    - step: AddBasicInformation
      to: AddedBasicInformation
      description: Add basic information
      data:
        patientInformation:
          fields:
            prefix: NamePrefix?
            firstName: NamePart
            middleName: NamePartOrInitial?
            lastName: NamePart
            suffix: NameSuffix?
            ssn: SSN
            mrn: SomeMRN?
        demographics: Demographics
        physicalMeasurements: PhysicalMeasurements
      trivial: true

    - step: AddContacts
      from: AddedBasicInformation
      to: AddedContacts
      description: Add contact information
      data:
        contactInformation:
          fields:
            phoneNumbers:
              list:
                type: PhoneNumberContact
                fields:
                  type: PhoneType
                  value: PhoneNumber
                  preferredTime: Integer
                  allowsSMS: Boolean
                  allowsVoice: Boolean
                  isPreferred: Boolean
            emails:
              list:
                type: EmailContact
                fields:
                  use: EmailUse
                  value: Email
                  isPreferred: Boolean
            emergencyContacts:
              list:
                type: EmergencyContact
                fields:
                  prefix: NamePrefix?
                  firstName: NamePart
                  middleName: NamePartOrInitial?
                  lastName: NamePart
                  suffix: NameSuffix?
                  relationship: EmergencyContactRelationship
                  contactInformation: PhoneNumber
      trivial: true

    - step: AddAddresses
      from: AddedContacts
      to: AddedAddresses
      description: Add demographics
      data:
        physicalAddresses: AddressWithUseType*
        billingAddresses: AddressWithUseType*
        deliveryAddresses: AddressWithUseType*

      trivial: true

    - step: AddInsurances
      from: AddedAddresses
      to: AddedInsurances
      description: Add insurance information
      data:
        insurances: Insurance+
      trivial: true

    - step: AddGuardians
      from: AddedInsurances
      to: AddedGuardians
      description: Add guardian information
      data:
        guardians:
          list:
            type: Guardian
            fields:
              guardianBasicInformation: GuardianBasicInformation
      trivial: true

    - step: AddClinicalInformation
      from: AddedGuardians
      to: Committed
      description: Add clinical information
      data:
        clinicalConsiderations:
          list: Integer
        schedulingPreferences:
          fields:
            technicianPreference: TechnicianPreference?
            preferredDaysOfWeek:
              list: Integer
        additionalPatientNotes: String?
        caregiverInformation: String?

    - state: Committed
      final: true
      description: Patient added successfully
      data:
        patientId: Guid

- flow: AddNewOrder
  description: Add a new order
  inputEntities: [Patient]
  outputEntity: Order
  steps:
    - step: AddStudy
      to: AddedStudy
      description: Add new study
      data:
        studyPreferences:
          fields:
            encounterType: Integer
            studyType: Integer
            studyAttributes: Json
        associatedInsurance: Guid*
      trivial: true

    - step: AddCareLocation
      from: AddedStudy
      to: AddedCareLocation
      data:
        careLocation: Guid
      trivial: true

    - step: AddPhysicians
      from: AddedCareLocation
      to: AddedPhysicians
      data:
        orderingPhysician: Guid
        interpretingPhysician: Guid
        referringPhysician: Guid?
        primaryCarePhysician: Guid?
      trivial: true

    - step: ReviewAndSubmitOrder
      from: AddedPhysicians
      to: OrderSubmitted

    - state: OrderSubmitted
      final: true
      description: Order submitted successfully
      data:
        orderId: Guid
# - flow: OrderIntake
#   description: Order intake workflow
#   steps:
#     - step: OnFaxReceived
#       to: FaxReceived
#       description: Fax received
#       data:
#         data: String

#     - state: FaxReceived
#       data:
#         faxId: Integer

#     - step: OnEmailReceived
#       to: EmailReceivedV2
#       description: Email received
#       data:
#         data: String

#     - state: EmailReceivedV2
#       data:
#         emailId: Integer

#     - step: ManualOrderEntry
#       to: OrderEntry
#       description: Manual order entry
#       trivial: true

#     - state: OrderEntry

#     - step: ProcessFax
#       from: FaxReceived
#       to: OrderCompleted
#       auto: true

#     - step: ProcessEmail
#       from: EmailReceivedV2
#       to: OrderCompleted
#       auto: true

#     - step: SubmitOrder
#       from: OrderEntry
#       to: OrderCompleted

#     - state: OrderCompleted
#       description: Order completed
#       data:
#         faxId: String?
#         emailId: String?
#         orderId: String
