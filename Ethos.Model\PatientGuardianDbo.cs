﻿using System.Linq.Expressions;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class PatientGuardianDbo : IAuditableEntity<PatientGuardianDbo>
{
    public virtual ICollection<PersonNameDbo> Names { get; set; } = new List<PersonNameDbo>();
    public DemographicsDbo? Demographics { get; set; }
    public ICollection<IdentifierDbo> Identifiers { get; set; } = new List<IdentifierDbo>();
    public Guid? ContactDetailId { get; set; }
    public PersonalContactDetailDbo? ContactDetail { get; set; }
    
    public long? RelationshipToPatient { get; set; }

    public new static void Register(ModelBuilder modelBuilder) => 
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<PatientGuardianDbo>(Register);
    public new static void Register(EntityTypeBuilder<PatientGuardianDbo> entity)
    {
        IAuditableEntity<PatientGuardianDbo>.Register(entity);

        // Configure Owned Types
        entity.OwnsOne(p => p.Demographics, builder =>
        {
            builder.ToTable($"{nameof(PatientGuardianDbo)}_{nameof(Demographics)}", IEntity.DefaultSchema);
            builder.WithOwner().HasForeignKey($"{nameof(PatientGuardianDbo)}Id");
            DemographicsDbo.Configure(builder);
        });
        
        entity.OwnsMany(p => p.Names, names =>
        {
            names.ToTable($"{nameof(PatientGuardianDbo)}_{nameof(Names)}", IEntity.DefaultSchema);
            names.WithOwner().HasForeignKey($"{nameof(PatientGuardianDbo)}Id");
            names.Property<int>("Id").ValueGeneratedOnAdd();
            names.HasKey("Id");
            PersonNameDbo.Configure(names);
        });
        
        entity.OwnsMany(p => p.Identifiers, identifiers =>
        {
            identifiers.ToTable($"{nameof(PatientGuardianDbo)}_{nameof(Identifiers)}", IEntity.DefaultSchema);
            identifiers.WithOwner().HasForeignKey($"{nameof(PatientGuardianDbo)}Id");
            identifiers.Property<int>("Id").ValueGeneratedOnAdd();
            identifiers.HasKey("Id");
            IdentifierDbo.Configure(identifiers);
        });
        
        entity.HasOne(p => p.ContactDetail)
            .WithMany()
            .HasForeignKey(p => p.ContactDetailId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.Property(p => p.RelationshipToPatient)
            .IsRequired(false);
    }
}