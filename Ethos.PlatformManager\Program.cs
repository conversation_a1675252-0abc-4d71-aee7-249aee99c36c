using Ethos.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Microsoft.OpenApi.Writers;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using Npgsql;
using Swashbuckle.AspNetCore.Swagger;
using Ethos.Events.Client;
using Ethos.PlatformManager;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddEthosEventClient(builder.Configuration);

builder.Services.AddControllers(options =>
{
})
    .AddNewtonsoftJson(options =>
    {
        options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
        options.SerializerSettings.Formatting = Formatting.Indented;
        options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
    });

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v3", new OpenApiInfo
    {
        Title = "Ethos Platform Manager API",
        Version = "v3",
        Description = "Ethos Platform Manager API Documentation",
        Contact = new OpenApiContact
        {
            Name = "Ethos Team",
            Email = "<EMAIL>"
        }
    });

    // Force Swagger to use OpenAPI 3.0
    c.UseAllOfToExtendReferenceSchemas();

    // Add JWT Authentication support in Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        Description = "JWT Authorization header using the Bearer scheme."
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
});

builder.Services.AddSwaggerGen();

builder.Services.AddProblemDetails();

builder.Services.AddHttpClient();

builder.Services.AddScoped<IBasicAuthUserProvider, BasicAuthUserProvider>();
builder.Services.AddAuthentication().AddScheme<BasicAuthenticationOptions, BasicAuthenticationHandler>("Basic", (options) =>
{
    options.Realm = "Ethos";
});
builder.Services.ConfigureEthosAuthorization(builder.Configuration);
//builder.Services.ConfigureApiUsageLogging(builder.Configuration);

// CORS is good
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowLocalhost",
        builder => builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

builder.Services.AddEthosAuthorization();

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory());
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true);
builder.Configuration.AddEnvironmentVariables();

builder.Services.AddSingleton(builder.Configuration);

builder.Services.AddDbContext<PlatformManagerDbContext>(options =>
{
    var dataSourceBuilder = new NpgsqlDataSourceBuilder(builder.Configuration.GetConnectionString("DefaultConnection"));
    dataSourceBuilder.EnableDynamicJson();
    var dataSource = dataSourceBuilder.Build();
    options.UseNpgsql(dataSource, builder => builder.MigrationsHistoryTable("__EFMigrationsHistory", PlatformManagerDbContext.PlatformDbSchema));
}, contextLifetime: ServiceLifetime.Scoped, optionsLifetime: ServiceLifetime.Singleton);

builder.Services.AddScoped<IClaimsTransformation, DebugClaimsTransformer>();

var app = builder.Build();

app.UseCors("AllowLocalhost");

// Do EF migrations
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<PlatformManagerDbContext>();

    //Console.WriteLine("\nAdding entities without specifying OID IDs...");

    //var entity1 = new TestEntity { Name = "First OID Item" };
    //var entity2 = new TestEntity { Name = "Second OID Item" };
    //var entity3 = new TestEntity { Name = "Third OID Item" };

    //using var tx = context.Database.BeginTransaction();

    //await context.Tests.AddAsync(entity1);
    //context.Tests.Add(entity2);
    //context.Tests.Add(entity3);

    //Console.WriteLine($"Before SaveChanges - Entity1 ID: {entity1.Id} (expected null/empty)");
    //Console.WriteLine($"Before SaveChanges - Entity2 ID: {entity2.Id} (expected null/empty)");

    //await context.SaveChangesAsync();
    //await tx.CommitAsync();

    //Console.WriteLine("\nAfter SaveChanges - IDs generated by custom OID generator:");
    //Console.WriteLine(entity1);
    //Console.WriteLine(entity2);
    //Console.WriteLine(entity3);

    //// Add another entity to show sequential generation continues
    //var entity4 = new TestEntity { Name = "Fourth OID Item" };
    //context.Tests.Add(entity4);
    //await context.SaveChangesAsync();
    //Console.WriteLine(entity4);

    //Console.WriteLine("\nRetrieving all entities from the database:");
    //foreach (var entity in context.Tests)
    //{
    //    Console.WriteLine(entity);
    //}

    //// Verify the OidSequence table content
    //var oidSequenceRecord = await context.OidSequences.FirstOrDefaultAsync(s => s.Name == "1.3.6.1.4.1.62892.1");
    //if (oidSequenceRecord != null)
    //{
    //    Console.WriteLine($"\nOidSequence record for '1.3.6.1.4.1.62892.1': CurrentOidValue = {oidSequenceRecord.CurrentOidValue}");
    //}

    if (app.Environment.IsDevelopment())
    {
        //var pendingMigrations = context.Database.GetPendingMigrations();

        //if (context.Database.HasPendingModelChanges())
        //{
        //    var migrationName = "DynamicMigration_" + DateTime.Now.Ticks;
        //    var projectDir = Directory.GetCurrentDirectory();
        //    var efTool = "dotnet";
        //    var arguments = $"ef migrations add {migrationName} --project \"{projectDir}\"";

        //    ProcessStartInfo processInfo = new(efTool, arguments)
        //    {
        //        RedirectStandardOutput = true,
        //        RedirectStandardError = true,
        //        UseShellExecute = false,
        //        CreateNoWindow = true
        //    };

        //    Process process = new()
        //    {
        //        StartInfo = processInfo
        //    };
        //    process.Start();

        //    process.WaitForExit();

        //    var output = process.StandardOutput.ReadToEnd();
        //    var error = process.StandardError.ReadToEnd();
        //    Console.WriteLine($"Migration created: {output}");
        //    if (!string.IsNullOrEmpty(error))
        //        Console.WriteLine($"Migration Errors: {error}");

        //    if (process.ExitCode != 0)
        //        throw new Exception("Migration Creation Failed");

        //    // long enough to do the migration?
        //    Thread.Sleep(10000);
        //}
    }
    context.Database.EnsureCreated();
    context.Database.Migrate();
    //context.Database.EnsureCreated();
}


// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    //app.UseSwagger();

    app.UseSwagger(c =>
    {
        c.SerializeAsV2 = false; // Ensure OpenAPI 3.0 output
    });
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v3/swagger.json", "Ethos Platform Manager API v3");
        c.RoutePrefix = "swagger";
    });
    //app.UseSwaggerUI();
}

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();
app.UseEthosAuthorization();
//app.UseApiUsageLogging();

app.UseEndpoints(endpoints =>
{
    // Updated custom endpoint to use proper serialization
    endpoints.MapGet("/api/openapi.json", async context =>
    {
        var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
        var swagger = swaggerProvider.GetSwagger("v3");

        // Use Swagger's built-in serializer
        var jsonWriter = new StringWriter();
        swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(jsonWriter.ToString());
    }).AllowAnonymous();

    endpoints.MapControllers();
});


//app.UseRouting().UseEndpoints(endpoints =>
//{
//    // Updated custom endpoint to use proper serialization
//    endpoints.MapGet("/api/openapi.json", async context =>
//    {
//        var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
//        var swagger = swaggerProvider.GetSwagger("v3");

//        // Use Swagger's built-in serializer
//        var jsonWriter = new StringWriter();
//        swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

//        context.Response.ContentType = "application/json";
//        await context.Response.WriteAsync(jsonWriter.ToString());
//    }).AllowAnonymous();
//    app.UseAuthentication();
//    app.UseAuthorization();
//    endpoints.MapControllers();
//    // Updated custom endpoint to use proper serialization
//    //endpoints.MapGet("/api/openapi.json", async context =>
//    //{
//    //    var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
//    //    var swagger = swaggerProvider.GetSwagger("v3");

//    //    // Use Swagger's built-in serializer
//    //    var jsonWriter = new StringWriter();
//    //    swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

//    //    context.Response.ContentType = "application/json";
//    //    await context.Response.WriteAsync(jsonWriter.ToString());
//    //}).WithMetadata(new { AllowAnonymous = true }).AllowAnonymous();
//});

app.UseExceptionHandler();
app.UseStatusCodePages();

app.Run();
