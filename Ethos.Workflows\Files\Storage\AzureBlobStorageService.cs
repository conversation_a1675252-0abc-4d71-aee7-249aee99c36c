using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Options;

namespace Ethos.Workflows.Files;

public class AzureBlobStorageService : IFileStorageService
{
    private readonly BlobContainerClient _containerClient;
    private readonly ILogger<AzureBlobStorageService> _logger;

    public AzureBlobStorageService(IOptions<StorageOptions> storageOptions, ILogger<AzureBlobStorageService> logger)
    {
        _logger = logger;
        var options = storageOptions.Value;
        if (options.Provider != StorageProvider.AzureBlob ||
            string.IsNullOrEmpty(options.AzureBlobConnectionString) ||
            string.IsNullOrEmpty(options.AzureBlobContainerName))
        {
            // This service should only be registered if configured for Azure Blob
            throw new InvalidOperationException("AzureBlobStorageService is not configured correctly.");
        }

        try
        {
            var blobServiceClient = new BlobServiceClient(options.AzureBlobConnectionString);
            _containerClient = blobServiceClient.GetBlobContainerClient(options.AzureBlobContainerName);
            _containerClient.CreateIfNotExists(PublicAccessType.None); // Ensure container exists, private access
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Failed to initialize Azure Blob Container Client for container '{ContainerName}'", options.AzureBlobContainerName);
            throw; // Re-throw to prevent application startup if storage is critical
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        // Check if the container exists and is accessible
        try
        {
            return _containerClient.Exists(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking Azure Blob Storage health");
            return false; // Return false if any error occurs
        }
    }

    public async Task DeleteFileAsync(string storagePath, CancellationToken cancellationToken = default)
    {
        try
        {
            BlobClient blobClient = _containerClient.GetBlobClient(storagePath);
            await blobClient.DeleteIfExistsAsync(DeleteSnapshotsOption.IncludeSnapshots, cancellationToken: cancellationToken);
            _logger.LogInformation("Deleted blob: {StoragePath}", storagePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting blob: {StoragePath}", storagePath);
            // Decide if you want to re-throw or just log
        }
    }

    public async Task<Stream?> GetFileStreamAsync(string storagePath, CancellationToken cancellationToken = default)
    {
        try
        {
            BlobClient blobClient = _containerClient.GetBlobClient(storagePath);
            if (await blobClient.ExistsAsync(cancellationToken))
            {
                BlobDownloadInfo downloadInfo = await blobClient.DownloadAsync(cancellationToken);
                return downloadInfo.Content; // Returns a stream
            }
            _logger.LogWarning("Blob not found: {StoragePath}", storagePath);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving blob stream: {StoragePath}", storagePath);
            return null; // Or re-throw depending on desired behavior
        }
    }

    public Task<string?> GetFileUrlAsync(string storagePath, TimeSpan? expiryTime = null, CancellationToken cancellationToken = default)
    {
        // For private containers, you'd typically generate a SAS token URL here.
        // This requires account key permissions or user delegation SAS.
        // Returning null for simplicity, as direct URLs might not be needed or secured differently.
        _logger.LogWarning("GetFileUrlAsync not implemented for direct URL generation (requires SAS token logic). StoragePath: {StoragePath}", storagePath);
        return Task.FromResult<string?>(null);
    }

    public async Task SaveFileAsync(Stream stream, string storagePath, string contentType, CancellationToken cancellationToken = default)
    {
        try
        {
            BlobClient blobClient = _containerClient.GetBlobClient(storagePath);
            await blobClient.UploadAsync(stream, new BlobHttpHeaders { ContentType = contentType }, cancellationToken: cancellationToken);
            _logger.LogInformation("Uploaded blob: {StoragePath} with ContentType: {ContentType}", storagePath, contentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading blob: {StoragePath}", storagePath);
            throw new IOException($"Failed to upload to Azure Blob Storage at path {storagePath}", ex);
        }
    }
}