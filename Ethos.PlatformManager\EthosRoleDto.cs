﻿
namespace Ethos.PlatformManager
{
    /// <summary>
    /// 
    /// </summary>
    public class EthosRoleDto
    {
        public string Name { get; set; } = null!;
        public Guid? Id { get; set; }
        public string[] Scopes { get; set; } = [];
        public string[] Filters { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRoleSummaryDto
    {
        public string Name { get; set; } = null!;
        public Guid Id { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRoleAssignmentSummaryDto : EthosRoleSummaryDto
    {
        public Guid TenantId { get; set; }
    }

    public class EthosScopeAssignmentSummaryDto
    {
        public Guid TenantId { get; set; }
        public string[] EffectiveScopes { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosBuiltinRoleReferenceData
    {
        public string Name { get; set; } = null!;
        public Guid Uuid { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosScopeDto
    {
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public bool Privileged { get; set; }
        public bool Assignable { get; set; }
    }
}