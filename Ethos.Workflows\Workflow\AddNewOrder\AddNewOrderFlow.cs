using Ethos.Model;
using Ethos.Workflows.Database;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Workflow.AddNewOrder;

public class AddNewOrderFlow : IAddNewOrderFlow
{
    private readonly AppDbContext dbContext;

    public AddNewOrderFlow(AppDbContext dbContext)
    {
        this.dbContext = dbContext;
    }

    public async Task<AddNewOrderState.OrderSubmitted> ReviewAndSubmitOrder(FlowContext<AddNewOrderState.AddedPhysicians> ctx)
    {
        var patientId = ctx.EntityLinks["Patient"];
        var patient = await dbContext.Set<PatientDbo>()
            .FindAsync(patientId)
            ?? throw new InvalidOperationException($"Patient with ID {patientId} not found.");

        var orderId = ctx.EntityLinks["Order"];

        // Check if IDs exist before trying to find entities
        PhysicianDbo? primaryCarePhysician = null;
        if (ctx.OldState.PrimaryCarePhysician.HasValue)
        {
            primaryCarePhysician = await dbContext.Set<PhysicianDbo>()
                .FindAsync(ctx.OldState.PrimaryCarePhysician.Value);
        }

        PhysicianDbo? referringPhysician = null;
        if (ctx.OldState.ReferringPhysician.HasValue)
        {
            referringPhysician = await dbContext.Set<PhysicianDbo>()
                .FindAsync(ctx.OldState.ReferringPhysician.Value);
        }

        PhysicianDbo? interpretingPhysician = null;
        if (ctx.OldState.InterpretingPhysician != Guid.Empty)
        {
            interpretingPhysician = await dbContext.Set<PhysicianDbo>()
                .FindAsync(ctx.OldState.InterpretingPhysician);
        }
        else
        {
            throw new InvalidOperationException("InterpretingPhysician is required");
        }

        // Get care location
        var careLocation = await dbContext.Set<CareLocationDbo>()
            .FindAsync(ctx.OldState.CareLocation)
            ?? throw new InvalidOperationException($"CareLocation with ID {ctx.OldState.CareLocation} not found.");

        // Get insurances
        var insurances = new List<InsuranceDbo>();
        foreach (var insurance in ctx.OldState.AssociatedInsurance)
        {
            var insuranceEntity = await dbContext.Set<InsuranceDbo>()
                .FindAsync(insurance);
            if (insuranceEntity != null)
            {
                insurances.Add(insuranceEntity);
            }
            else
            {
                throw new InvalidOperationException($"Insurance with ID {insurance} not found.");
            }
        }

        // IMPORTANT: First create or update the order
        OrderDbo order;
        var existingOrder = await dbContext.Set<OrderDbo>().FindAsync(orderId);

        if (existingOrder != null)
        {
            // Update existing order
            order = existingOrder;
            order.Patient = patient;
            order.PatientId = patient.Id;

            if (primaryCarePhysician != null)
            {
                order.PrimaryCarePhysician = primaryCarePhysician;
                order.PrimaryCarePhysicianId = primaryCarePhysician.Id;
            }

            if (referringPhysician != null)
            {
                order.ReferringPhysician = referringPhysician;
                order.ReferringPhysicianId = referringPhysician.Id;
            }

            if (interpretingPhysician != null)
            {
                order.InterpretingPhysician = interpretingPhysician;
                order.InterpretingPhysicianId = interpretingPhysician.Id;
            }

            order.CareLocation = careLocation;
            order.CareLocationId = careLocation.Id;
        }
        else
        {
            // Create new order
            order = new OrderDbo
            {
                Id = orderId,
                Patient = patient,
                PatientId = patient.Id,
                CareLocation = careLocation,
                CareLocationId = careLocation.Id,
                Studies = new List<StudyDbo>()
            };

            if (primaryCarePhysician != null)
            {
                order.PrimaryCarePhysician = primaryCarePhysician;
                order.PrimaryCarePhysicianId = primaryCarePhysician.Id;
            }

            if (referringPhysician != null)
            {
                order.ReferringPhysician = referringPhysician;
                order.ReferringPhysicianId = referringPhysician.Id;
            }

            if (interpretingPhysician != null)
            {
                order.InterpretingPhysician = interpretingPhysician;
                order.InterpretingPhysicianId = interpretingPhysician.Id;
            }

            dbContext.Set<OrderDbo>().Add(order);
        }

        // Save the order first to ensure it exists in the database
        await dbContext.SaveChangesAsync();

        // Now create the study and associate it with the order
        var study = new StudyDbo
        {
            Id = Guid.NewGuid(),
            Order = order,
            OrderId = order.Id,
            Insurances = insurances
        };

        dbContext.Set<StudyDbo>().Add(study);

        // Add the study to the order's collection
        if (order.Studies == null)
        {
            order.Studies = new List<StudyDbo>();
        }
        order.Studies.Add(study);

        return new AddNewOrderState.OrderSubmitted(orderId);
    }
}
