﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Text.Json.Serialization;
using Swashbuckle.AspNetCore.Annotations;

namespace Ethos.ReferenceData
{
    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataList
    {
        public long Id { get; set; }
        public string Name { get; set; } = null!;
        public ReferenceDataListType Type { get; set; }
        public ICollection<ReferenceDataListValue> Values { get; set; } = [];
        public ICollection<ReferenceDataSet> Sets { get; set; } = [];

        [JsonIgnore]
        [NotMapped]
        public Type ListType
        {
            get
            {
                return Type switch
                {
                    ReferenceDataListType.String => typeof(string),
                    ReferenceDataListType.DateTime => typeof(DateTimeOffset),
                    ReferenceDataListType.Float => typeof(double),
                    ReferenceDataListType.Integer => typeof(long),
                    ReferenceDataListType.Boolean => typeof(bool),
                    _ => throw new Exception($"Invalid list type: {Type}"),
                };
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static ReferenceDataListType GuessListType(object? value)
        {
            if (value is DateTime || value is DateTimeOffset)
                return ReferenceDataListType.DateTime;
            else if (value is long || value is int || value is short || value is uint || value is ushort)
                return ReferenceDataListType.Integer;
            else if (value is double || value is float)
                return ReferenceDataListType.Float;
            else if (value is bool)
                return ReferenceDataListType.Boolean;
            else
                return ReferenceDataListType.String;
        }
    }

    /// <summary>
    /// 
    /// 
    /// </summary>
    public struct KeySetValueSchemaMember
    {
        public string DisplayName { get; set; }
        public string Type { get; set; }
        public string Source { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataListValue
    {
        public long Id { get; set; }
        public long ListId { get; set; }

        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        [NotMapped]
        public object? Value
        {
            get
            {
                return (List?.Type) switch
                {
                    ReferenceDataListType.String => StringValue,
                    ReferenceDataListType.Boolean => BooleanValue,
                    ReferenceDataListType.DateTime => DateTimeValue,
                    ReferenceDataListType.Integer => IntegerValue,
                    ReferenceDataListType.Float => FloatValue,
                    _ => null,
                };
            }

            set
            {
                var convertedVal = ConvertValue(List?.Type ?? ReferenceDataListType.String, value);
                switch (List?.Type)
                {
                    case ReferenceDataListType.String:
                        StringValue = convertedVal is string strVal ? strVal ?? string.Empty : string.Empty;
                        break;

                    case ReferenceDataListType.Boolean:
                        BooleanValue = convertedVal is bool boolVal && boolVal;
                        break;

                    case ReferenceDataListType.DateTime:
                        DateTimeValue = convertedVal is DateTimeOffset dtoVal ? dtoVal : DefaultDateTime;
                        break;

                    case ReferenceDataListType.Integer:
                        IntegerValue = convertedVal is long longVal ? longVal : default;
                        break;

                    case ReferenceDataListType.Float:
                        FloatValue = convertedVal is double dblVal ? dblVal : default;
                        break;
                }
            }
        }

        

        /// <summary>
        /// 
        /// </summary>
        [JsonIgnore]
        public static readonly DateTimeOffset DefaultDateTime = new(new DateTime(1904, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc));

        /// <summary>
        /// 
        /// </summary>
        [JsonIgnore]
        public static readonly long DefaultInteger = default;

        /// <summary>
        /// 
        /// </summary>
        [JsonIgnore]
        public static readonly double DefaultFloat = default;

        /// <summary>
        /// 
        /// </summary>
        [JsonIgnore]
        public static readonly bool DefaultBoolean = default;

        /// <summary>
        /// 
        /// </summary>
        [JsonIgnore]
        public static readonly string DefaultString = string.Empty;

        static readonly string[] DefaultDateTimeFormats = [ "yyyy-MM-ddTHH:mm:ss.fffffffK", "yyyy-MM-ddTHH:mm:ssK", "yyyy-MM-ddTHH:mm:ss.ffffffK",
         "yyyy-MM-ddTHH:mm:ss.fffffK", "yyyy-MM-ddTHH:mm:ss.ffffK", "yyyy-MM-ddTHH:mm:ss.fffK", "yyyy-MM-ddTHH:mm:ss.ffK", "yyyy-MM-ddTHH:mm:ss.fK" ]; 
            
        [SwaggerIgnore]
        [JsonIgnore]
        public double FloatValue { get; set; }
        [SwaggerIgnore]
        [JsonIgnore]
        public long IntegerValue { get; set; }
        [SwaggerIgnore]
        [JsonIgnore]
        public bool BooleanValue { get; set; }
        [JsonIgnore]
        [SwaggerIgnore]
        public string StringValue { get; set; } = string.Empty;
        [JsonIgnore]
        [SwaggerIgnore]
        public DateTimeOffset DateTimeValue { get; set; } = DefaultDateTime;
        public ReferenceDataList List { get; set; } = null!;
        public ICollection<ReferenceDataSetValue> SetValues { get; set; } = [];
        public ICollection<ReferenceDataSetKeyValue> SetKeyValues { get; set; } = [];

        public static object? ConvertValue(ReferenceDataListType type, object? value)
        {
            switch (type)
            {
                case ReferenceDataListType.String:
                    return value is string strVal ? strVal : value?.ToString() ?? string.Empty;

                case ReferenceDataListType.Boolean:
                    return value is bool boolVal ? boolVal : Convert.ToBoolean(value);

                case ReferenceDataListType.DateTime:
                    if (value is DateTimeOffset dtoVal)
                        return dtoVal;
                    else if (value is DateTime dtVal)
                    {
                        if (dtVal.Kind != DateTimeKind.Utc)
                            return new DateTimeOffset(dtVal.ToUniversalTime());
                        else
                            return new DateTimeOffset(dtVal);
                    }
                    else
                    {
                        if (value is string dtStrVal)
                        {
                            if (DateTime.TryParseExact(dtStrVal, DefaultDateTimeFormats, CultureInfo.CurrentCulture,
                                DateTimeStyles.None, out dtVal))
                                return new DateTimeOffset(dtVal.ToUniversalTime());
                            else if (DateTime.TryParse(dtStrVal, out dtVal))
                                return new DateTimeOffset(dtVal);
                            else
                                throw new Exception($"Invalid date string: {dtStrVal}");
                        }
                        else
                        {
                            return new DateTimeOffset(Convert.ToDateTime(value));
                        }
                    }

                case ReferenceDataListType.Integer:
                    if (value is long longVal)
                        return longVal;
                    else
                    {
                        longVal = Convert.ToInt64(value);
                        return longVal;
                    }

                case ReferenceDataListType.Float:
                    if (value is double dblVal)
                        return dblVal;
                    else
                    {
                        dblVal = Convert.ToDouble(value);
                        return dblVal;
                    }
            }
            return null;
        }
    }

    public class ReferenceDataImportJob
    {
        public Guid JobId { get; set; }
        public DateTimeOffset StartTime {  get; set; }
        public DateTimeOffset? EndTime { get; set; }
        public Guid? TenantId { get; set; }
        public long? SetId {  get; set; }
        public ReferenceDataSet? Set {  get; set; } 
        public string? Error { get; set; }
    }

    public class ReferenceDataSet
    {
        public long Id { get; set; }
        public string? Source { get; set; } = null!;
        public string? Authority { get; set; } = null!;
        public string Version { get; set; } = null!;

        public string Name { get; set; } = null!;

        /// <summary>
        ///  ID of the list that is the key for this set.
        /// </summary>
        public long KeyListId { get; set; }

        public ReferenceDataList KeyList { get; set; } = null!;

        public Guid TenantId { get; set; } = Guid.Empty!;

        public ICollection<ReferenceDataSetKeyValue> SetKeyValues { get; set; } = [];
        public ICollection<ReferenceDataSetKeyValueAlternate> AlternateKeyValues { get; set; } = [];
        public ICollection<ReferenceDataSetValue> SetValues { get; set; } = [];

        public ReferenceDataImportJob? ImportJob { get; set; }
    }

    public class ReferenceDataSetKeyValueAlternate
    {
        public long SetId { get; set; }
        public Guid TenantId { get; set; } = Guid.Empty!;
        public ReferenceDataSetKeyValue KeyValue { get; set; } = null!;
        public long KeyValueId { get; set; }
        public KeyTriggerBehavior TriggerBehavior { get; set; } = KeyTriggerBehavior.None;
        public SchemaBehavior SchemaBehavior { get; set; } = SchemaBehavior.All;
        public ReferenceDataSet Set { get; set; } = null!;
    }

    public class ReferenceDataSetKeyValue
    {
        public long Id { get; set; }
        public long SetId { get; set; }
        public long ValueId { get; set; }
        public bool Enabled { get; set; } = true;
        public ReferenceDataListValue Value { get; set; } = null!;
        public Guid TenantId { get; set; } = Guid.Empty!;
        public ICollection<ReferenceDataSetValue> SetValues { get; set; } = [];
        public ReferenceDataSet Set { get; set; } = null!;
        public ICollection<ReferenceDataSetValue> MappedSetValues { get; set; } = [];
        public CreationContext CreationContext { get; set; } = CreationContext.System;
        public ReferenceDataSetKeyValue? KeyTrigger { get; set; }
        public ICollection<ReferenceDataSetKeyValue> TriggeredKeys { get; set; } = [];
        public ICollection<ReferenceDataSetKeyValueAlternate> AlternateKeys { get; set; } = [];
        public long? KeyTriggerId { get; set; }
    }

    public class ReferenceDataSetValue
    {
        public long ValueId { get; set; }
        public long SetId { get; set; }
        public Guid TenantId { get; set; } = Guid.Empty!;

        /// <summary>
        /// The key within the current set to which the value belongs.
        /// </summary>
        public long KeyValueId { get; set; }
        public string Alias { get; set; } = null!;

        /// <summary>
        /// The key to which this value is mapped within another set (allows heirarchical definitions that retain mapped properties from another set)
        /// </summary>
        public long? MappedKeyValueId { get; set; }

        /// <summary>
        /// Navigation property associated with ValueId
        /// </summary>
        public ReferenceDataListValue Value { get; set; } = null!;

        /// <summary>
        /// Navigation property associated with KeyValueId
        /// </summary>
        public ReferenceDataSetKeyValue KeyValue { get; set; } = null!;

        /// <summary>
        /// 
        /// </summary>
        public ReferenceDataSetKeyValue? MappedKeyValue { get; set; }

        /// <summary>
        /// Navigation property to SetId
        /// </summary>
        public ReferenceDataSet Set {  get; set; } = null!;
    }

    public class ReferenceDataSetKeyDefinitionDto
    {
        public string Name { get; set; } = null!;
        [JsonConverter(typeof(NewtonsoftJsonDynamicObjectConverter))]
        public object? Value { get; set; }
    }

    public class ReferenceDataSetKeyValueDto
    {
        public long Id { get; set; }
        public ReferenceDataSetKeyDefinitionDto Key { get; set; } = null!;
        public Dictionary<string, object?> Values { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataResultSet
    {
        /// <summary>
        /// 
        /// </summary>
        public ReferenceDataResultSet() { }

        public static ReferenceDataSetKeyValueDto GetSetKeyValueDto(ReferenceDataSetKeyValue value)
        {
            var keyValVal = value.Value.List.Name;

            var dto = new ReferenceDataSetKeyValueDto()
            {
                Id = value.Id,
                Key = new ReferenceDataSetKeyDefinitionDto()
                {
                    Name = keyValVal,
                    Value = value.Value.Value
                }
            };

            var valuesDict = new Dictionary<string, object?>();

            foreach (var val in value.SetValues)
            {
                var name = string.IsNullOrEmpty(val.Alias) ? val.Value.List.Name : val.Alias;

                object? finalVal;
                if (val.MappedKeyValueId > 0 && val.MappedKeyValue is not null)
                    finalVal = GetSetKeyValueDto(val.MappedKeyValue);
                else
                    finalVal = val.Value.Value;

                if (valuesDict.TryGetValue(name, out var existingVal))
                {
                    if (existingVal is ICollection<object?> col)
                    {
                        if (!col.Contains(finalVal))
                        {
                            col.Add(finalVal);
                            valuesDict[name] = col;
                        }
                    }
                    else
                    {
                        if (!Equals(existingVal, finalVal))
                        {
                            col = [existingVal, finalVal];
                            valuesDict[name] = col;
                        }
                    }
                }
                else
                    valuesDict.Add(name, finalVal);
            }

            valuesDict.Add("__id", value.Id);

            dto.Values = valuesDict;
            return dto;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static Dictionary<string, object> GetSetKeyValue(ReferenceDataSetKeyValue value)
        {
            var keyValVal = value.Value.List.Name;

            var dict = new Dictionary<string, object>
            {
                { "id", value.Id  },
                { "key", new { value = value.Value.Value, name = keyValVal } }
            };

            var valuesDict = new Dictionary<string, object?>();

            foreach (var val in value.SetValues)
            {
                var name = string.IsNullOrEmpty(val.Alias) ? val.Value.List.Name : val.Alias;

                object? finalVal;
                if (val.MappedKeyValueId > 0 && val.MappedKeyValue is not null)
                    finalVal = GetSetKeyValue(val.MappedKeyValue);
                else
                    finalVal = val.Value.Value;

                if (valuesDict.TryGetValue(name, out var existingVal))
                {
                    if (existingVal is ICollection<object?> col)
                    {
                        col.Add(finalVal);
                        valuesDict[name] = col;
                    }
                    else
                    {
                        col = [existingVal, finalVal];
                        valuesDict[name] = col;
                    }
                }
                else
                    valuesDict.Add(name, finalVal);
            }

            valuesDict.Add("__id", value.Id);

            dict.Add("values", valuesDict);
            return dict;
        }

        public ReferenceDataResultSet(ReferenceDataSet set)
        {
            Name = set.Name;
            SetId = set.Id;

            if (set.SetKeyValues is null || set.SetKeyValues.Count == 0)
                return;

            foreach (var value in set.SetKeyValues)
                Data.Add(GetSetKeyValue(value));
        }

        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; } = null!;

        public string Source { get; set; } = null!;

        public string Version { get; set; } = null!;

        public string Authority { get; set; } = null!;

        /// <summary>
        /// 
        /// </summary>
        public long SetId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ICollection<object> Data { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValueMetadata
    {
        public string SetName { get; set; } = null!;
        public string Role { get; set; } = null!;
        public ReferenceDataListValue Value { get; set; } = null!;
        public string? Alias { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValueResultSet : ReferenceDataResultSet
    {
        public ReferenceDataValueMetadata Metadata { get; set; } = null!;

        public ReferenceDataValueResultSet(ReferenceDataListValue searchValue, ReferenceDataSet set) : base()
        {
            Name = set.Name;
            SetId = set.Id;
            var role = "Member";

            Name = set.Name;
            SetId = set.Id;

            if (set.SetKeyValues is null || set.SetKeyValues.Count == 0)
                return;

            foreach (var value in set.SetKeyValues)
            {
                if (value.Value is null || string.IsNullOrEmpty(value.Value?.List?.Name))
                    continue;

                if (searchValue.ListId == set.KeyListId)
                    role = "Key";

                Metadata ??= new ReferenceDataValueMetadata()
                {
                    Role = role,
                    SetName = set.Name,
                    Value = searchValue
                };

                var keyValVal = value.Value.List.Name;

                var dict = new Dictionary<string, object?>
                {
                    { "id", value.Id  },
                    { "key", new { value = value.Value.Value, name = keyValVal } }
                };

                var valuesDict = new Dictionary<string, object?>();

                foreach (var val in value.SetValues)
                {
                    var name = string.IsNullOrEmpty(val.Alias) ? val.Value.List.Name : val.Alias;

                    if (!name.Equals(val.Value.List.Name))
                    {
                        role = "Aliased Member";
                        Metadata = new ReferenceDataValueMetadata()
                        {
                            Role = role,
                            SetName = set.Name,
                            Value = searchValue,
                            Alias = val.Alias
                        };
                    }

                    if (valuesDict.TryGetValue(name, out var existingVal))
                    {
                        if (existingVal is ICollection<object?> col)
                        {
                            col.Add(val.Value.Value);
                            valuesDict[name] = col;
                        }
                        else
                        {
                            col = [existingVal, val.Value.Value];
                            valuesDict[name] = col;
                        }
                    }
                    else
                        valuesDict.Add(name, val.Value.Value);
                }

                dict.Add("values", valuesDict);
                Data.Add(dict);
            }
        }
    }
}
