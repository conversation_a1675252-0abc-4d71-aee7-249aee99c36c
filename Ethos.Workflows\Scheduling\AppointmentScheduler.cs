using Ethos.Model;
using Ethos.Model.Scheduling;
using Ethos.ReferenceData.Client;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.EntityFrameworkCore;
using ExecutionContext = System.Threading.ExecutionContext;

namespace Ethos.Workflows.Scheduling;

public sealed record SchedulingResult(
    IReadOnlyList<(DateOnly Date, RoomDbo Room, CareLocationShiftDbo Shift)> AvailableSlots,
    IReadOnlyDictionary<(DateOnly Date, Guid RoomId, Guid CareLocationShiftId), List<string>> SoftConstraintViolations);

public interface IAppointmentScheduler
{
    Task<SchedulingResult> FindAvailableSlotsForPatient(StudyDbo study, CareLocationDbo careLocation, DateOnly startDate, DateOnly endDate);
}

public class AppointmentScheduler : IAppointmentScheduler
{
    private readonly AppDbContext _dbContext;
    private readonly IReferenceDataClient _referenceDataClient;
    
    public AppointmentScheduler(AppDbContext dbContext, IReferenceDataClient referenceDataClient)
    {
        _dbContext = dbContext;
        _referenceDataClient = referenceDataClient;
    }

    private class ReferenceDataCache(IReferenceDataClient referenceDataClient) : IReferenceData
    {
        private readonly Dictionary<(Type entityType, long id), object?> _cache = new();
        
        public async Task<TRefData?> GetById<TRefData>(long? id) where TRefData : class, IReferenceDataEntity, new()
        {
            if (id == null)
                return null;
            if (_cache.TryGetValue((typeof(TRefData), id.Value), out var cachedValue))
            {
                return cachedValue as TRefData;
            }

            var result = await referenceDataClient.GetSetValueById<TRefData>(id.Value);
            
            var value = result?.Values;
            _cache[(typeof(TRefData), id.Value)] = value;
            return value;
        }
    }

    public async Task<SchedulingResult> FindAvailableSlotsForPatient(StudyDbo study, CareLocationDbo careLocation, DateOnly startDate, DateOnly endDate)
    {
        var order = study.Order;
        var patient = order.Patient;
        
        // Include rooms for the care location.
        CareLocationDbo careLocationWithOther = await _dbContext.Set<CareLocationDbo>()
            .Include(c => c.Equipment)
            .Include(c => c.ParentProvider)
            .FirstOrDefaultAsync(c => c.Id == careLocation.Id) 
                                                ?? throw new RequiredEntityDoesNotExistException(EntityType.CareLocation, careLocation.Id);
        
        var provider = careLocationWithOther.ParentProvider!;

        var allConstraints = _dbContext.Set<SchedulingConstraintDbo>()
            .ToList();

        var refData = new ReferenceDataCache(_referenceDataClient);
        
        var rooms = _dbContext.Set<RoomDbo>()
            .Where(r => r.CareLocationId == careLocation.Id)
            .Include(r => r.Equipment)
            .ToList();
        var shifts = _dbContext.Set<CareLocationShiftDbo>()
            .Where(r => r.CareLocationId == careLocation.Id);
        var takenSlots = _dbContext.Set<PatientAppointmentDbo>()
            .Where(a => a.Room.CareLocationId == careLocation.Id && a.Date >= startDate && a.Date <= endDate)
            .Select(a => new { a.Date, a.RoomId, a.CareLocationShiftId })
            .ToHashSet();
        var technicians = _dbContext.Set<TechnicianCareLocationRelationDbo>()
            .Where(s => s.CareLocationId == careLocation.Id)
            .Select(s => s.Technician)
            .ToList();

        var patientVars = SchedulerConfig.GetAttributes(patient, refData);
        var careLocationVars = SchedulerConfig.GetAttributes(careLocation, refData);
        var providerVars = SchedulerConfig.GetAttributes(provider, refData);
        var roomVars = rooms.ToDictionary(r => r.Id, (e) => SchedulerConfig.GetAttributes(e, refData));
        var shiftVars = shifts.ToDictionary(s => s.Id, (e) => SchedulerConfig.GetAttributes(e, refData));
        var technicianVars = technicians.ToDictionary(t => t.Id, (e) => SchedulerConfig.GetAttributes(e, refData));
        
        var availableSlots = new List<(DateOnly Date, RoomDbo Room, CareLocationShiftDbo Shift)>();
        var softConstraintViolatedSlots = new Dictionary<(DateOnly Date, Guid RoomId, Guid CareLocationShiftId), List<string>>();

        var executionContext = new ExprEvalContext() { };
        
        foreach (var room in rooms)
        {
            var roomVarsForRoom = roomVars[room.Id];
            
            foreach (var shift in shifts)
            {
                var shiftVarsForShift = shiftVars[shift.Id];
                
                var currentDate = startDate;
                while (currentDate <= endDate)
                {
                    Value.Object allVars = (Value.Of(new Dictionary<string, Value>()
                    {
                        ["Patient"] = patientVars,
                        ["Provider"] = providerVars,
                        ["CareLocation"] = careLocationVars,
                        ["Room"] = roomVarsForRoom,
                        ["Shift"] = shiftVarsForShift
                    }) as Value.Object)!;

                    if (takenSlots.Contains(new { Date = currentDate, RoomId = room.Id, CareLocationShiftId = shift.Id }))
                    {
                        continue;
                    }

                    var failedHardConstraints = false;
                    // Apply constraints.
                    foreach (var constraint in allConstraints)
                    {
                        var result = await constraint.Expression.Eval(executionContext, allVars);
                        if (result is not Value.Boolean boolResult)
                            throw new EvaluationException(
                                $"Constraint expression did not evaluate to a boolean for room {room.Id}: {constraint.Expression}");
                        var value = boolResult.Value;
                        if (!value)
                        {
                            if (constraint.IsHardConstraint)
                            {
                                failedHardConstraints = true;
                                break;
                            }
                            softConstraintViolatedSlots.TryGetValue((currentDate, room.Id, shift.Id), out var violations);
                            if (violations == null)
                            {
                                violations = new List<string>();
                                softConstraintViolatedSlots[(currentDate, room.Id, shift.Id)] = violations;
                            }
                            violations.Add(constraint.Name);
                        }
                    }
                    
                    if (failedHardConstraints)
                    {
                        currentDate = currentDate.AddDays(1);
                        continue;
                    }
                    
                    availableSlots.Add((Date: currentDate, Room: room, Shift: shift));
                    currentDate = currentDate.AddDays(1);
                }
            }
        }
        
        return new SchedulingResult(availableSlots, softConstraintViolatedSlots);
    }
}