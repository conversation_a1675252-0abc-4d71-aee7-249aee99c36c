"""
login_api.py ─ asyncio + aiohttp client for

    POST /api/login
    GET  /api/login/check     (requires Authorization header)

Drop this file next to your other generated clients, then:

    import aiohttp, asyncio
    from login_api import LoginApi, LoginRequestDto

    async def main():
        async with aiohttp.ClientSession(base_url="https://server.example") as s:
            auth = LoginApi(s)
            token = await auth.login(LoginRequestDto("test", "password"))
            print("JWT:", token)

            info = await auth.check_token_type()
            print("token type:", info.tokenType)

    asyncio.run(main())
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import Optional

import aiohttp

from ethos_common import Json

# ──────────────────────────────────────────────────────────────────────────────
# DTOs
# ──────────────────────────────────────────────────────────────────────────────


@dataclass(slots=True)
class LoginRequestDto:
    username: str
    password: str

    def to_json(self) -> Json:
        """Convert to JSON serializable dict."""
        return {"username": self.username, "password": self.password}


@dataclass(slots=True)
class TokenTypeResponse:
    tokenType: str


# ──────────────────────────────────────────────────────────────────────────────
# Client
# ──────────────────────────────────────────────────────────────────────────────


class LoginApi:
    """Light-weight async client mirroring Ethos `LoginController`."""

    _JSON_HDR = {"Content-Type": "application/json"}

    # --------------------------------------------------------------------- init
    def __init__(self, session: aiohttp.ClientSession) -> None:
        if session._base_url is None:
            raise ValueError("aiohttp.ClientSession must be created with base_url=")

        self._s: aiohttp.ClientSession = session
        self._path: str = "/api/login"
        self._bearer: Optional[str] = None
        self._log = logging.getLogger(self.__class__.__name__)

    # ----------------------------------------------------------- private helper
    async def _req(self, method: str, url: str, **kw) -> aiohttp.ClientResponse:
        hdrs = self._JSON_HDR.copy()
        if self._bearer:
            hdrs["Authorization"] = f"Bearer {self._bearer}"
        hdrs |= kw.pop("headers", {})
        kw["headers"] = hdrs

        self._log.debug("HTTP %s %s", method, url)
        resp = await self._s.request(method, url, **kw)

        if resp.status >= 400:
            text = await resp.text()
            self._log.error("%s %s → %s\n%s", method, url, resp.status, text)
            resp.raise_for_status()

        return resp

    # ------------------------------------------------------------------ public
    async def login(self, creds: LoginRequestDto) -> str:
        """
        POST /api/login

        Returns the issued JWT token string **and stores it** for subsequent
        authorised calls.
        """
        resp = await self._req("POST", self._path, json=creds.to_json())
        token = (await resp.json()).get("token")
        if not token:
            raise RuntimeError("No token returned from /api/login")

        self._bearer = token
        return token

    async def check_token_type(self) -> TokenTypeResponse:
        """
        GET /api/login/check   (requires Authorization header)

        Raises if no token is set.
        """
        if not self._bearer:
            raise ValueError("Bearer token not set – call login() first or set_bearer_token().")

        resp = await self._req("GET", f"{self._path}/check")
        return TokenTypeResponse(**await resp.json())

    # ------------------------------------------------------------- convenience
    def set_bearer_token(self, token: str) -> None:
        """Inject an external JWT (e.g. from another auth flow)."""
        self._bearer = token
