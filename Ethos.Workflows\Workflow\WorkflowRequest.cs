using System.Net;
using System.Text.Json;

namespace Ethos.Workflows.Workflow;

public sealed record WorkflowRequest<TData>(Guid? InstanceId, TData InputData);

public sealed record WorkflowResponse<TData>(Guid InstanceId, TData OutputData);

public class WorkflowRequest
{
    public Guid InstanceId { get; set; }
    public string? TransitionName { get; set; }
}

public sealed record DraftRequest<TData>(Guid? InstanceId, JsonElement InputData);

public sealed class WorkflowApiException : Exception
{
    public HttpStatusCode StatusCode { get; }
    public string ResponseBody   { get; }

    public WorkflowApiException(HttpStatusCode status, string body)
        : base($"Workflow API call failed with {(int)status} {status}.")
        => (StatusCode, ResponseBody) = (status, body);
}