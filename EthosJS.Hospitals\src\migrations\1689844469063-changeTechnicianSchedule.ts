import { MigrationInterface, QueryRunner } from 'typeorm';

export class changeTechnicianSchedule1689844469063 implements MigrationInterface {
    name = 'changeTechnicianSchedule1689844469063'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technician_schedules" ALTER COLUMN "facility_id" DROP NOT NULL');
      await queryRunner.query('ALTER TABLE "technician_schedules" ALTER COLUMN "capacity" DROP NOT NULL');
      await queryRunner.query('ALTER TABLE "technician_schedules" ALTER COLUMN "capacity" DROP DEFAULT');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technician_schedules" ALTER COLUMN "capacity" SET DEFAULT 1');
      await queryRunner.query('ALTER TABLE "technician_schedules" ALTER COLUMN "capacity" SET NOT NULL');
      await queryRunner.query('ALTER TABLE "technician_schedules" ALTER COLUMN "facility_id" SET NOT NULL');
    }

}
