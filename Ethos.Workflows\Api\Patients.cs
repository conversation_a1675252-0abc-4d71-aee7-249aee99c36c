using System.Diagnostics.CodeAnalysis;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Ethos.Model;
using Ethos.Utilities.Pagination;

namespace Ethos.Workflows.Api;


public sealed record PersonNameDto
{
    [CheckString(100)]
    public required string FirstName { get; set; }
    [CheckString(100)]
    public required string? MiddleName { get; set; }
    [CheckString(100)]
    public required string LastName { get; set; }
    [CheckReferenceData(typeof(NamePrefixEntity))]
    public required long? Prefix { get; set; }
    [CheckReferenceData(typeof(NameSuffixEntity))]
    public required long? Suffix { get; set; }
}

public sealed record PatientInformationDto
{
    [CheckReferenceData(typeof(NamePrefixEntity))]
    public required long? Prefix { get; set; }
    [CheckReferenceData(typeof(NameSuffixEntity))]
    public required long? Suffix { get; set; }
    [CheckString(hardPattern: @"^\d{3}-\d{2}-\d{4}$")]
    public required string Ssn { get; set; }
    [CheckString(100)]
    public required string FirstName { get; set; }
    [CheckString(100)]
    public required string? MiddleName { get; set; }
    [CheckString(100)]
    public required string LastName { get; set; }
    [CheckString(100)]
    public required string? Mrn { get; set; }
}

public sealed record DemographicsDto
{
    public required DateOnly? DateOfBirth { get; set; }
    [CheckReferenceData(typeof(GenderEntity))]
    public required long? Gender { get; set; }
    [CheckReferenceData(typeof(SexEntity))]
    public required long? BirthSex { get; set; }
    [CheckReferenceData(typeof(MaritalStatusEntity))]
    public required long? MaritalStatus { get; set; }
    [CheckReferenceData(typeof(RaceEntity))]
    public required long? Race { get; set; }
    [CheckReferenceData(typeof(EthnicityEntity))]
    public required long? Ethnicity { get; set; }
}

[CheckExpression("State.Country == Country")]
public sealed record AddressDto
{
    [CheckString(300)] 
    public required string Line1 { get; set; }
    [CheckString(300)] 
    public required string? Line2 { get; set; }
    [CheckString(100)] 
    public required string City { get; set; }
    [CheckReferenceData(typeof(StateEntity))]
    public required long? State { get; set; }
    [CheckString(100)] 
    public required string PostalCode { get; set; }
    [CheckReferenceData(typeof(CountryEntity))]
    public required long Country { get; set; }
}

public sealed record PersonalAddressDto
{
    [CheckReferenceData(typeof(AddressUseEntity))]
    public required long Use { get; set; }
    [CheckReferenceData(typeof(AddressTypeEntity))]
    public required long Type { get; set; }
    public required AddressDto Address { get; set; }
}

public sealed record PhysicalMeasurementsDto
{
    [CheckRealNumber(hardMin: 20.0, softMin: 54.0, softMax: 84.0, hardMax: 108)]
    public required decimal? HeightInches { get; set; }
    [property: CheckRealNumber(hardMin: 5.0, softMin: 80.0, softMax: 400.0, hardMax: 1500.0)]
    public required decimal? WeightPounds { get; set; }
    [property: CheckRealNumber(hardMin: 6.0, softMin: 12.0, softMax: 20.0, hardMax: 30.0)]
    public required decimal? NeckSize { get; set; }
    [property: CheckRealNumber(hardMin: 6.0, softMin: 15.0, softMax: 45.0, hardMax: 100.0)]
    public required decimal? Bmi { get; set; }
}

public sealed record PersonalPhoneNumberDto
{
    [CheckReferenceData(typeof(PhoneUseEntity))]
    public required long Type { get; set; }
    [CheckString(30)] 
    public required string Value { get; set; }
    [CheckReferenceData(typeof(PreferredContactTimeEntity))]
    public required long PreferredTime { get; set; }
    public required bool AllowsSms { get; set; }
    public required bool AllowsVoice { get; set; }
    public required bool IsPreferred { get; set; }
}

public sealed record PersonalEmailDto
{
    [CheckReferenceData(typeof(EmailUseEntity))]
    public required long Use { get; set; }
    [CheckString(100)] 
    public required string Value { get; set; }
    public required bool IsPreferred { get; set; }
}

public sealed record PersonalEmergencyContactDto
{
    [CheckReferenceData(typeof(NamePrefixEntity))]
    public required long? Prefix { get; set; }
    [CheckString(100)] 
    public required string FirstName { get; set; }
    [CheckString(100)] 
    public required string? MiddleName { get; set; }
    [CheckString(100)] 
    public required string LastName { get; set; }
    [CheckReferenceData(typeof(NameSuffixEntity))]
    public required long? Suffix { get; set; }
    [CheckReferenceData(typeof(RelationshipTypeEntity))]
    public required long Relationship { get; set; }
    [CheckString(100)]
    public required string ContactInformation { get; set; }
}

public sealed record PersonalContactDetailDto
{
    public required IReadOnlyList<PersonalPhoneNumberDto> PhoneNumbers { get; set; }
    public required IReadOnlyList<PersonalEmailDto> Emails { get; set; }
    public required IReadOnlyList<PersonalEmergencyContactDto> EmergencyContacts { get; set; }
    public required IReadOnlyList<PersonalAddressDto> Addresses { get; set; }
}

public sealed record AddressWithUseDto(
    AddressDto Address,
    [property: CheckReferenceData(typeof(AddressUseEntity))] long Use);

public sealed record EmailWithUseDto(
    [property: CheckString(100)] string Email,
    [property: CheckReferenceData(typeof(EmailUseEntity))] long Use);

public sealed record PhoneNumberWithUseDto(
    [property: CheckString(100)] string PhoneNumber,
    [property: CheckReferenceData(typeof(PhoneUseEntity))] long Use,
    bool? AllowsSMS,
    bool? AllowsVoice,
    bool? AllowsCommunication,
    [property: CheckString(20)] string? Extension);

public sealed record InsuranceHolderDto
{
    public required string Name { get; set; }
    public required DateOnly DateOfBirth { get; set; }
    public required long Relationship { get; set; }
}

public sealed record InsuranceDto(
    [property: CheckReferenceData(typeof(InsuranceCarrierEntity))] long InsuranceCarrier, // Changed from string to long
    [property: CheckString(50)] string? InsuranceId,
    [property: CheckString(50)] string PolicyId,
    [property: CheckString(50)] string GroupNumber,
    [property: CheckString(50)] string MemberId,
    InsuranceHolderDto? InsuranceHolder,
    PhoneNumberWithUseDto? PhoneNumber,
    EmailWithUseDto? Email,
    AddressWithUseDto? Address);

public sealed record PatientGuardianDto
{
    public required Guid? Id { get; set; }
    public required IReadOnlyList<PersonNameDto> Names { get; set; }
    public required IReadOnlyList<IdentifierDto> Identifiers { get; set; }
    public DemographicsDto? Demographics { get; set; }
    public PersonalContactDetailDto? ContactInformation { get; set; }
    public long? RelationshipToPatient { get; set; }
}

public sealed record SchedulingPreferencesDto(
    [property: CheckReferenceData(typeof(TechnicianPreferenceEntity))] long? TechnicianPreference,
    [property: CheckReferenceData(typeof(WeekDayEntity))] IReadOnlyList<long> PreferredDayOfWeek);

public sealed record IdentifierDto
{
    // [CheckReferenceData(typeof(IdentifierTypeEntity))]
    public required string System { get; set; }
    public required string Value { get; set; }
}

public sealed record CreatePatientInputDto : IInputDto
{
    public PatientInformationDto? PatientInformation { get; set; }
    public DemographicsDto? Demographics { get; set; }
    public PhysicalMeasurementsDto? PhysicalMeasurements { get; set; }
    public PersonalContactDetailDto? ContactInformation { get; set; }
    public IReadOnlyList<InsuranceDto>? Insurances { get; set; }
    public IReadOnlyList<PatientGuardianDto>? Guardians { get; set; }
    [CheckReferenceData(typeof(ClinicalConsiderationEntity))]
    public IReadOnlyList<long>? ClinicalConsiderations { get; set; }
    public SchedulingPreferencesDto? SchedulingPreferences { get; set; }
    public string? AdditionalPatientNotes { get; set; }
    public string? CaregiverInformation { get; set; }
    public IReadOnlyList<IdentifierDto>? Identifiers { get; set; }
}

public sealed record PatientDto
{
    public required Guid Id { get; set; }
    public required IReadOnlyList<PersonNameDto> Names { get; set; }
    public required IReadOnlyList<IdentifierDto> Identifiers { get; set; }
    public DemographicsDto? Demographics { get; set; }
    public PhysicalMeasurementsDto? PhysicalMeasurements { get; set; }
    public PersonalContactDetailDto? ContactInformation { get; set; }
    public IReadOnlyList<InsuranceDto>? Insurances { get; set; }
    public IReadOnlyList<PatientGuardianDto> Guardians { get; set; }
    public SchedulingPreferencesDto? SchedulingPreferences { get; set; }
    public IReadOnlyList<long>? ClinicalConsiderations { get; set; }
    
    // Calculated/Derived fields
    public required IReadOnlyList<Guid> OrderIds { get; set; }
    public required string? LastStudyLocation { get; set; }
    public DateOnly? NextStudyDate { get; set; }
}

public interface IPatientApi : IEntityHttpClient<CreatePatientInputDto, PatientDto, PatientQ>;

public class PatientHttpClient(System.Net.Http.HttpClient httpClient)
    : EntityHttpClientBase<CreatePatientInputDto, PatientDto, PatientQ>(httpClient, "patient"),
        IPatientApi;
