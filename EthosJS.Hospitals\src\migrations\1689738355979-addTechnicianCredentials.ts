import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTechnicianCredentials1689738355979 implements MigrationInterface {
    name = 'addTechnicianCredentials1689738355979'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('CREATE TABLE "credentials" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "code" character varying NOT NULL, "issued_by" character varying, CONSTRAINT "PK_1e38bc43be6697cdda548ad27a6" PRIMARY KEY ("id"))');
      await queryRunner.query('CREATE TABLE "technicians_credentials_credentials" ("technicians_id" integer NOT NULL, "credentials_id" integer NOT NULL, CONSTRAINT "PK_0be2521e75c773bbff3c0b23147" PRIMARY KEY ("technicians_id", "credentials_id"))');
      await queryRunner.query('CREATE INDEX "IDX_69362e6ecfbfd89e09c8f53895" ON "technicians_credentials_credentials" ("technicians_id") ');
      await queryRunner.query('CREATE INDEX "IDX_86352f155f17278f512b049491" ON "technicians_credentials_credentials" ("credentials_id") ');
      await queryRunner.query('ALTER TABLE "technicians_credentials_credentials" ADD CONSTRAINT "FK_69362e6ecfbfd89e09c8f538953" FOREIGN KEY ("technicians_id") REFERENCES "technicians"("id") ON DELETE CASCADE ON UPDATE NO ACTION');
      await queryRunner.query('ALTER TABLE "technicians_credentials_credentials" ADD CONSTRAINT "FK_86352f155f17278f512b049491e" FOREIGN KEY ("credentials_id") REFERENCES "credentials"("id") ON DELETE CASCADE ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "technicians_credentials_credentials" DROP CONSTRAINT "FK_86352f155f17278f512b049491e"');
      await queryRunner.query('ALTER TABLE "technicians_credentials_credentials" DROP CONSTRAINT "FK_69362e6ecfbfd89e09c8f538953"');
      await queryRunner.query('DROP INDEX "IDX_86352f155f17278f512b049491"');
      await queryRunner.query('DROP INDEX "IDX_69362e6ecfbfd89e09c8f53895"');
      await queryRunner.query('DROP TABLE "technicians_credentials_credentials"');
      await queryRunner.query('DROP TABLE "credentials"');
    }

}
