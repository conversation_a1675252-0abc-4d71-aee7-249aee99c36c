using System.IdentityModel.Tokens.Jwt;
using System.Reflection;
using System.Text;
using System.Text.Json;
using Ethos.Auth;
using Ethos.Model;
using Ethos.ReferenceData.Client;
using Ethos.Workflows;
using Ethos.Workflows.Api;
using Ethos.Workflows.Audit;
using Ethos.Workflows.Controllers;
using Ethos.Workflows.Database;
using Ethos.Workflows.Files;
using Ethos.Workflows.Notifications;
using Ethos.Workflows.Scheduling;
using Ethos.Workflows.Workflow;
using Ethos.Workflows.Workflow.AddNewOrder;
using Ethos.Workflows.Workflow.AddNewPatient;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Serilog;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Microsoft.AspNetCore.Http;
using Swashbuckle.AspNetCore.Swagger;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Writers;

public class Program
{
    private static readonly JsonSerializerOptions _json =
        new(JsonSerializerDefaults.Web)   // start from the “Web” preset
        {
            // you can set simple flags here if you want
        };
    
    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()                       // anything you had in Main
            .ConfigureWebHostDefaults(web =>
            {
                web.UseStartup<Startup>();      // THIS is what tests need
            });
    
    public static void Main(string[] args)
    {
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/log.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            Log.Information("Starting up");
            var builder = Host
                .CreateDefaultBuilder(args)
                .UseSerilog()
                .ConfigureWebHostDefaults(webBuilder => { webBuilder.UseStartup<Startup>(); });
            var host = builder.Build();

            // Automatically apply migrations
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    //context.Database.EnsureDeleted();
                    context.Database.Migrate(); // Applies all pending migrations
                    Log.Information("Database migration completed successfully.");
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An error occurred while migrating the database.");
                    // You might want to exit if the migration fails
                    throw;
                }
            }

            host.Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}


public class Startup
{
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    // Provides access to application configuration (e.g. appsettings.json)
    public IConfiguration Configuration { get; }

    // Called by the runtime to add services to the DI container.
    public void ConfigureServices(IServiceCollection services)
    {
        // Example: Add controllers, EF Core, Identity, etc.
        services.AddControllers();

        services.AddHttpClient<IReferenceDataClient, ReferenceDataClient>();
        services.AddScoped<IWorkflowEngine, WorkflowEngine>();
        services.AddScoped<IAppointmentScheduler, AppointmentScheduler>();
        services.AddScoped<IAddNewPatientFlow, AddNewPatientFlow>();
        services.AddScoped<IAddNewOrderFlow, AddNewOrderFlow>();
        services.AddScoped<INotificationService, NotificationService>();
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<IRealTimeHub, RealTimeHub>();
        
        services.AddScoped<InsuranceVerificationService, InsuranceVerificationService>();
        services.AddHostedService<InsuranceVerificationWorker>();
        services.Configure<InsuranceVerificationWorker.WorkerConfig>(
                Configuration.GetSection("InsuranceVerificationWorker"));
        services.Configure<FastAuthConfiguration>(
            Configuration.GetSection("FastAuth"));

        services.AddFileUploadServices(Configuration);

        services.AddCors(options =>
        {
            options.AddPolicy("AllowLocalhost",
                builder => builder
                    .AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader());
        });
        
        services.AddEthosAuthorization();

        // Add EF Core with PostgreSQL.
        services.AddDbContext<AppDbContext>(options =>
        {
            options.UseNpgsql(Configuration.GetConnectionString("DefaultConnection"), 
                builder => builder.MigrationsHistoryTable("__EFMigrationsHistory", IEntity.DefaultSchema));
        });
        
        services.AddScoped<DbContext, AppDbContext>();

        // Add Swagger for API documentation
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v3", new OpenApiInfo
            {
                Title = "Ethos Workflows API",
                Version = "v3",
                Description = "Ethos Workflows API Documentation",
                Contact = new OpenApiContact
                {
                    Name = "Ethos Team",
                    Email = "<EMAIL>"
                }
            });

            // Force Swagger to use OpenAPI 3.0
            c.CustomSchemaIds(t => t.FullName);
            c.UseAllOfToExtendReferenceSchemas();
            c.UseOneOfForPolymorphism();
            c.SupportNonNullableReferenceTypes();
            c.SelectSubTypesUsing(baseT =>
            {
                if (baseT.IsGenericType &&
                    baseT.GetGenericTypeDefinition() == typeof(QueryDto<>))
                {
                    var p = baseT.GetGenericArguments()[0];
                    return new[]
                    {
                        typeof(QueryDto<>.All).MakeGenericType(p),
                        typeof(QueryDto<>.Literal).MakeGenericType(p),
                        typeof(QueryDto<>.Not).MakeGenericType(p),
                        typeof(QueryDto<>.And).MakeGenericType(p),
                        typeof(QueryDto<>.Or).MakeGenericType(p)
                    };
                }
                return Enumerable.Empty<Type>();
            });

            // Add JWT Authentication support in Swagger UI
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Type = SecuritySchemeType.Http,
                Scheme = "bearer",
                BearerFormat = "JWT",
                Description = "JWT Authorization header using the Bearer scheme."
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
        });
    }

    // Called by the runtime to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        // if (env.IsDevelopment())
        // {
        //     
        // }
        
        // TODO: Remove this later if we ever deploy to production
        // app.UseDeveloperExceptionPage();
        app.UseMiddleware<JsonExceptionMiddleware>();
        
        app.UseSwagger(c =>
        {
            c.SerializeAsV2 = false; // Ensure OpenAPI 3.0 output
        });
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v3/swagger.json", "Ethos Workflows API v3");
            c.RoutePrefix = "swagger";
        });

        app.UseRouting();
        app.UseCors("AllowLocalhost");
        app.UseAuthentication();
        app.UseAuthorization();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            // Updated custom endpoint to use proper serialization
            endpoints.MapGet("/api/openapi.json", async context =>
            {
                var swaggerProvider = context.RequestServices.GetRequiredService<ISwaggerProvider>();
                var swagger = swaggerProvider.GetSwagger("v3");

                // Use Swagger's built-in serializer
                var jsonWriter = new StringWriter();
                swagger.SerializeAsV3(new OpenApiJsonWriter(jsonWriter));

                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync(jsonWriter.ToString());
            });
        });
    }
}
