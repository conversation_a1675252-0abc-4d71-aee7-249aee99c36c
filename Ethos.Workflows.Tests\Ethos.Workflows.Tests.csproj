<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>

        <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector"/>
        <PackageReference Include="FluentAssertions"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
        <PackageReference Include="Microsoft.NET.Test.Sdk"/>
        <PackageReference Include="Moq" />
        <PackageReference Include="Serilog.Sinks.XUnit" />
        <PackageReference Include="Testcontainers.PostgreSql" />
        <PackageReference Include="xunit"/>
        <PackageReference Include="xunit.runner.visualstudio"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Ethos.Model\Ethos.Model.csproj" />
        <ProjectReference Include="..\Ethos.Workflows\Ethos.Workflows.csproj" />
        <ProjectReference Include="..\Ethos.ReferenceData\Ethos.ReferenceData.csproj" />
    </ItemGroup>

</Project>
