apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.chart }}
  labels:
    app: {{ .Values.chart }}
spec:
  minReadySeconds: 15
  selector:
    matchLabels:
      app: {{ .Values.chart }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: {{ .Values.chart }}
      annotations:
        # Hack to force images with same tag to redeploy
        deployedAt: {{ now | unixEpoch | quote }}
    spec:
      containers:
        - image: {{ required "Variable 'image' is required" .Values.image }}
          imagePullPolicy: Always
          name: {{ .Values.chart }}
          ports:
            - containerPort: 80
          env:
            - name: PORT
              value: "80"
          {{- range $key, $value := .Values.additional_env_variables }}
            - name: {{ $key }}
              value: {{ $value | quote }}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ .Values.chart }}
            - configMapRef:
                name: common
            - secretRef:
                name: {{ .Values.chart }}
          {{- if eq .Values.env "production" }}
          resources:
            limits:
              cpu: 1000m
              memory: 512Mi
            requests:
              cpu: 500m
              memory: 512Mi
          {{- else }}
          resources:
            limits:
              cpu: 300m
              memory: 500Mi
            requests:
              cpu: 200m
              memory: 350Mi
          {{- end }}
      imagePullSecrets:
        - name: registry-secret
