files:
    "/root/stunnel/beats-psk.txt":
      mode: "000600"
      owner: root
      group: root
      content: |
        chforms:qXPM8d1oNYtO7LoL6xkVr_w_WBRaw5sq

    "/root/stunnel/stunnel.conf":
      mode: "000755"
      owner: root
      group: root
      content: |
        foreground = yes
        [beats]
        client = yes
        accept  = 0.0.0.0:5044
        connect = ***************:5044
        ciphers = PSK
        PSKsecrets = /etc/stunnel/beats-psk.txt

commands:
    command1:
      command: docker start stunnel || docker run -tid --restart always -v /root/stunnel/stunnel.conf:/etc/stunnel/stunnel.conf:ro -v /root/stunnel/beats-psk.txt:/etc/stunnel/beats-psk.txt -p 5044:5044 --entrypoint stunnel --name stunnel vimagick/stunnel