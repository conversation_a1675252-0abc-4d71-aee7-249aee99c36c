using Ethos.Model;
using Ethos.Workflows.Files;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

namespace Ethos.Workflows.Tests;

public class JwtUploadTokenServiceTests
{
    private readonly Mock<ILogger<JwtUploadTokenService>> _mockLogger;
    private readonly IOptions<JwtTokenOptions> _validOptions;
    private readonly JwtTokenOptions _validOptionsValue;
    private readonly JwtUploadTokenService _service;

    public JwtUploadTokenServiceTests()
    {
        _mockLogger = new Mock<ILogger<JwtUploadTokenService>>();

        // Set up valid JwtTokenOptions
        _validOptionsValue = new JwtTokenOptions
        {
            SecretKey = new string('A', 32), // 32 chars
            Issuer = "TestIssuer",
            Audience = "TestAudience",
            ExpiryMinutes = 5,
            ClockSkew = TimeSpan.FromSeconds(1)
        };
        _validOptions = Options.Create(_validOptionsValue);

        _service = new JwtUploadTokenService(_validOptions, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithShortSecretKey_ThrowsInvalidOperationException()
    {
        // Arrange
        var invalidOptions = Options.Create(new JwtTokenOptions
        {
            SecretKey = "TooShortSecret",
            Issuer = "TestIssuer",
            Audience = "TestAudience"
        });

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() =>
            new JwtUploadTokenService(invalidOptions, _mockLogger.Object)
        );
    }

    private static readonly EntityType TestContextEntityType = EntityType.Provider;
    private static readonly Guid TestContextEntityId = Guid.NewGuid();

    [Fact]
    public void GenerateToken_ValidClaims_ReturnsNonEmptyTokenString()
    {
        // Arrange
        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png", "image/jpeg" },
            MaxFileSize = 1048576
        };

        // Act
        var token = _service.GenerateToken(uploadTokenClaims, TimeSpan.FromMinutes(5));

        // Assert
        Assert.False(string.IsNullOrEmpty(token));
    }

    [Fact]
    public void ValidateToken_ValidToken_ReturnsMatchingClaims()
    {
        // Arrange
        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png", "image/jpeg" },
            MaxFileSize = 1048576
        };

        var token = _service.GenerateToken(uploadTokenClaims, TimeSpan.FromMinutes(5));

        // Act
        var validatedClaims = _service.ValidateToken(token);

        // Assert
        Assert.NotNull(validatedClaims);
        Assert.Equal(uploadTokenClaims.UserId, validatedClaims!.UserId);
        Assert.Equal(uploadTokenClaims.ContextEntityType, validatedClaims.ContextEntityType);
        Assert.Equal(uploadTokenClaims.ContextEntityId, validatedClaims.ContextEntityId);
        Assert.Equal(uploadTokenClaims.Purpose, validatedClaims.Purpose);
        Assert.Equal(uploadTokenClaims.MaxFileSize, validatedClaims.MaxFileSize);
        Assert.Equal(uploadTokenClaims.AllowedMimeTypes.Count, validatedClaims.AllowedMimeTypes.Count);
        Assert.True(uploadTokenClaims.AllowedMimeTypes.SequenceEqual(validatedClaims.AllowedMimeTypes));
    }

    [Fact]
    public async Task ValidateToken_ExpiredToken_ReturnsNull()
    {
        // Arrange
        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png" },
            MaxFileSize = 1000
        };

        // Generate token with 1 second expiry
        var token = _service.GenerateToken(uploadTokenClaims, TimeSpan.FromSeconds(1));

        // Wait to ensure token has expired
        await Task.Delay(2000);

        // Act
        var validatedClaims = _service.ValidateToken(token);

        // Assert
        Assert.Null(validatedClaims);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning, 
                It.IsAny<EventId>(), 
                It.IsAny<It.IsAnyType>(), 
                It.IsAny<Exception>(), 
                (Func<It.IsAnyType, Exception, string>)It.IsAny<object>()), 
            Times.AtLeastOnce,
            "Expected a warning log when token is expired."
        );
    }

    [Fact]
    public void ValidateToken_MissingEssentialClaims_ReturnsNull()
    {
        // Arrange
        // Generate a valid token first
        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png" },
            MaxFileSize = 1000
        };
        var token = _service.GenerateToken(uploadTokenClaims, TimeSpan.FromMinutes(5));

        // Tamper with the token's 'sub' (UserId) claim (remove it) 
        // For demonstration, we simply re-generate a token with missing or empty sub
        var missingClaims = new UploadTokenClaims
        {
            UserId = "", // Empty
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png" },
            MaxFileSize = 1000
        };
        var tokenMissingSub = _service.GenerateToken(missingClaims, TimeSpan.FromMinutes(5));

        // Act
        var validatedClaims = _service.ValidateToken(tokenMissingSub);

        // Assert
        Assert.Null(validatedClaims);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                null,
                (Func<It.IsAnyType, Exception, string>)It.IsAny<object>()),
            Times.AtLeastOnce,
            "Expected a warning log when sub or max_size is invalid."
        );
    }

    [Fact]
    public void ValidateToken_MaxFileSizeZero_ReturnsNull()
    {
        // Arrange
        // Generate a token with max_file_size = 0
        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png" },
            MaxFileSize = 0
        };

        var token = _service.GenerateToken(uploadTokenClaims, TimeSpan.FromMinutes(5));

        // Act
        var validatedClaims = _service.ValidateToken(token);

        // Assert
        Assert.Null(validatedClaims);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                null,
                (Func<It.IsAnyType, Exception, string>)It.IsAny<object>()),
            Times.AtLeastOnce,
            "Expected a warning log when max_file_size is invalid (<= 0)."
        );
    }

    [Fact]
    public void ValidateToken_InvalidSignature_ReturnsNull()
    {
        // Arrange
        // Use a separate instance with a different SecretKey
        var differentOptions = Options.Create(new JwtTokenOptions
        {
            SecretKey = new string('B', 32), 
            Issuer = "TestIssuer",
            Audience = "TestAudience",
            ExpiryMinutes = 5
        });
        var differentService = new JwtUploadTokenService(differentOptions, _mockLogger.Object);

        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png" },
            MaxFileSize = 1000
        };

        // Generate a token using the "different" service
        var token = differentService.GenerateToken(uploadTokenClaims, TimeSpan.FromMinutes(5));

        // Validate that token with the original service
        var validatedClaims = _service.ValidateToken(token);

        // Assert
        Assert.Null(validatedClaims);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                (Func<It.IsAnyType, Exception, string>)It.IsAny<object>()),
            Times.AtLeastOnce,
            "Expected an error log when token signature is invalid."
        );
    }

    [Fact]
    public void ValidateToken_InvalidIssuer_ReturnsNull()
    {
        // Arrange
        // Generate a valid token with the original service
        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png" },
            MaxFileSize = 1000
        };
        var token = _service.GenerateToken(uploadTokenClaims, TimeSpan.FromMinutes(5));

        // Create a new service with a different issuer
        var differentOptions = Options.Create(new JwtTokenOptions
        {
            SecretKey = _validOptionsValue.SecretKey,
            Issuer = "DifferentIssuer", // not matching original token
            Audience = _validOptionsValue.Audience,
            ExpiryMinutes = _validOptionsValue.ExpiryMinutes
        });
        var differentService = new JwtUploadTokenService(differentOptions, _mockLogger.Object);

        // Act
        var validatedClaims = differentService.ValidateToken(token);

        // Assert
        Assert.Null(validatedClaims);
    }

    [Fact]
    public void ValidateToken_InvalidAudience_ReturnsNull()
    {
        // Arrange
        var uploadTokenClaims = new UploadTokenClaims
        {
            UserId = "user123",
            ContextEntityType = TestContextEntityType,
            ContextEntityId = TestContextEntityId,
            Purpose = "TestPurpose",
            AllowedMimeTypes = new System.Collections.Generic.List<string> { "image/png" },
            MaxFileSize = 1000
        };
        var token = _service.GenerateToken(uploadTokenClaims, TimeSpan.FromMinutes(5));

        // Create a new service with a different audience
        var differentOptions = Options.Create(new JwtTokenOptions
        {
            SecretKey = _validOptionsValue.SecretKey,
            Issuer = _validOptionsValue.Issuer,
            Audience = "DifferentAudience", // not matching original token
            ExpiryMinutes = _validOptionsValue.ExpiryMinutes
        });
        var differentService = new JwtUploadTokenService(differentOptions, _mockLogger.Object);

        // Act
        var validatedClaims = differentService.ValidateToken(token);

        // Assert
        Assert.Null(validatedClaims);
    }
}