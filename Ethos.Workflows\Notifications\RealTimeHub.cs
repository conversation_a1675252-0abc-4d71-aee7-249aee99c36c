using Ethos.Workflows.Database;
using Microsoft.AspNetCore.SignalR;

namespace Ethos.Workflows.Notifications;

public interface IRealTimeHub
{
    Task BroadcastNotification(Notification notification, List<Guid> userIds);
    Task SendNotificationToUser(Notification notification, Guid userId);
}

public class RealTimeHub : Hub, IRealTimeHub
{
    // This is a standard SignalR Hub
    public async Task BroadcastNotification(Notification notification, List<Guid> userIds)
    {
        foreach (var userId in userIds)
        {
            await Clients.User(userId.ToString())
                .SendAsync("ReceiveNotification", notification);
        }
    }

    public async Task SendNotificationToUser(Notification notification, Guid userId)
    {
        await Clients.User(userId.ToString())
            .SendAsync("ReceiveNotification", notification);
    }
}