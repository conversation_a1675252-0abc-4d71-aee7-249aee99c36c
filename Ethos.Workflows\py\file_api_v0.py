from __future__ import annotations

from typing import Any, Dict

import mimetypes, pathlib
from aiohttp import FormData

from ethos_common import *
from ethos_common_types import *

@dataclass(slots=True)
class RequestUploadTokenDto:
    contextEntityType: str
    contextEntityId: str
    purpose: str

    def to_json(self) -> Dict[str, Any]:
        """Convert to JSON serializable dict."""
        return {
            'contextEntityType': self.contextEntityType,
            'contextEntityId': self.contextEntityId,
            'purpose': self.purpose,
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> RequestUploadTokenDto:
        """Create an instance from a JSON serializable dict."""
        if not isinstance(data, dict):
            raise TypeError("Expected a dict for deserialization.")
        return cls(
            contextEntityType=data['contextEntityType'],
            contextEntityId=data['contextEntityId'],
            purpose=data['purpose'],
        )

class FileApi(HttpClientBase):
    """Thin client generated from ControllerDefinition."""
    def __init__(self, session):
        super().__init__(session, 'File', None)

    async def request_upload_token(self, requestUploadTokenDto: RequestUploadTokenDto) -> Json:
        r = await self._req('POST', f'{self._path}/request-upload-token', json=requestUploadTokenDto.to_json())
        return await r.json()

    async def upload_file(
        self,
        file_path: str | pathlib.Path,
        upload_token: str,
        mime: str | None = None,
    ) -> dict:
        """
        Stream a single file using the Upload-Token header required by the API.
        Returns the JSON response (UploadResponseDto).
        """
        file_path = pathlib.Path(file_path)
        mime = mime or mimetypes.guess_type(str(file_path))[0] or "application/octet-stream"

        form = FormData()
        with file_path.open("rb") as fh:
            form.add_field(
                "file",
                fh,              # aiohttp streams, no full read
                filename=file_path.name,
                content_type=mime,
            )

            hdr = {"Upload-Token": upload_token}
            resp = await self._req("POST", f'{self._path}/upload', data=form, headers=hdr)
            return await resp.json()
