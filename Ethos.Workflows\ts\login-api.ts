/**
 * login-api.ts ─ axios client for Ethos authentication endpoints.
 *
 * POST /api/login
 * GET  /api/login/check
 */
import { AxiosInstance, AxiosRequestConfig } from 'axios';
import axios from 'axios';

// --------------------------------------------------------------------------
// DTOs
// --------------------------------------------------------------------------

export interface LoginRequestDto {
    username: string;
    password: string;
}

export interface LoginResponseDto {
    token: string;
}

export interface TokenTypeResponse {
    tokenType: string;
}

// --------------------------------------------------------------------------
// Client
// --------------------------------------------------------------------------

export class LoginApi {
    private readonly session: AxiosInstance;
    private readonly path: string = '/api/login';

    constructor(session: AxiosInstance) {
        if (!session.defaults.baseURL) {
            throw new Error('AxiosInstance must be created with a baseURL.');
        }
        this.session = session;
    }

    private async request<T>(config: AxiosRequestConfig): Promise<T> {
        try {
            const response = await this.session.request<T>(config);
            return response.data;
        } catch (error) {
            if (axios.isAxiosError(error)) {
                console.error(`HTTP ${config.method} ${config.url} -> ${error.response?.status}`);
                console.error('Error details:', error.response?.data);
            } else {
                console.error('An unexpected error occurred:', error);
            }
            throw error;
        }
    }

    /**
     * POST /api/login
     * @returns The issued JWT, which is also automatically set as the
     * bearer token for the underlying axios instance.
     */
    async login(creds: LoginRequestDto): Promise<string> {
        const response = await this.request<LoginResponseDto>({
            method: 'POST',
            url: this.path,
            data: creds,
        });
        if (!response.token) {
            throw new Error('No token returned from /api/login');
        }
        // Set token for subsequent requests
        this.session.defaults.headers.common['Authorization'] = `Bearer ${response.token}`;
        return response.token;
    }

    /**
     * GET /api/login/check (requires Authorization header)
     * Raises an error if no token has been set via `login()` or `setBearerToken()`.
     */
    async checkTokenType(): Promise<TokenTypeResponse> {
        if (!this.session.defaults.headers.common['Authorization']) {
            throw new Error("Bearer token not set. Call login() first or setBearerToken().");
        }
        return this.request<TokenTypeResponse>({
            method: 'GET',
            url: `${this.path}/check`,
        });
    }

    /**
     * Inject an external JWT token into the axios instance.
     */
    setBearerToken(token: string): void {
        this.session.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }
}
