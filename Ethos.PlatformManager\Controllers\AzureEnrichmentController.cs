using System.Text.Json;
using Ethos.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("api/azure")]
    [DisableEthosAuthorization]
    [Authorize(AuthenticationSchemes = "Basic")]
    public class AzureEnrichmentController : ControllerBase
    {
        readonly ILogger<AzureEnrichmentController> _logger;
        readonly PlatformManagerDbContext _dbContext;
        readonly IConfiguration _configuration;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="dbContext"></param>
        public AzureEnrichmentController(ILogger<AzureEnrichmentController> logger, PlatformManagerDbContext dbContext, IConfiguration configuration)
        {
            _logger = logger;
            _dbContext = dbContext;
            _configuration = configuration;
        }

        [HttpPost]
        [Route("beforeUserCreate")]
        public ActionResult<AzureBeforeUserCreateResponse> BeforeUserCreate([FromBody] AzureBeforeUserCreateRequest request)
        {
            /***
             {"step":"PostAttributeCollection","client_id":"a8d641a8-7112-4c1f-ad32-cf156f81459c",
                "ui_locales":"en-US","country":null,"email":"<EMAIL>","surname":"Jennings",
            "jobTitle":"Developer","displayName":"Alex Jennings","givenName":"Alex",
            "identities":[{"signInType":"emailAddress","issuer":"ethoshbcustnonprod.onmicrosoft.com","issuerAssignedId":"<EMAIL>"}]}
            ***/

            using (_logger.BeginScope(new { CodeMethod = nameof(BeforeUserCreate), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (_logger.IsEnabled(LogLevel.Debug))
                    _logger.LogDebug("Received Azure request for step {Step}: {RequestEntity}", request.Step, JsonSerializer.Serialize(request));

                string? message = null;

                if (string.IsNullOrEmpty(request.DisplayName))
                    message = "Display name is required.";

                if (string.IsNullOrEmpty(request.Surname))
                    message = "Last name is required.";

                if (string.IsNullOrEmpty(request.GivenName))
                    message = "Given name is required.";

                if (!string.IsNullOrEmpty(message))
                    return Ok(new AzureBeforeUserCreateResponse() { UserMessage = message, Action = AzureBeforeUserCreateResponse.ValidationError });

                _dbContext.Users.Add(new EthosUser()
                {
                    Active = true,
                    Deleted = false,
                    DisplayName = request.DisplayName!,
                    GivenName = request.GivenName!,
                    Surname = request.Surname!,
                    Title = request.JobTitle,
                });

                _dbContext.SaveChanges();
                return Ok(new AzureBeforeUserCreateResponse
                {
                });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("token")]
        public ActionResult<EthosAzureTokenEnrichmentResponse> EnrichToken([FromBody] AzureTokenEnrichmentRequest request)
        {
            // my user ID: f80ff48b-5c78-4bb6-ac46-4ae274552a91
            // test tenant: ed5e78a0-9e7c-43d2-9a9b-5cae9dad9761
            // license ID : 3fa85f64-5717-4562-b3fc-2c963f66afff
            using (_logger.BeginScope(new { CodeMethod = nameof(EnrichToken), TraceId = HttpContext.TraceIdentifier, Uri = HttpContext.Request.GetDisplayUrl() }))
            {
                if (_logger.IsEnabled(LogLevel.Debug))
                    _logger.LogDebug("Received Azure request for step {Step}: {RequestEntity}", request.Step, JsonSerializer.Serialize(request));

                var resp = new EthosAzureTokenEnrichmentResponse();

                if (string.IsNullOrEmpty(request.ObjectId))
                {
                    _logger.LogError("No object ID found in Azure pre-token issuance request.");
                    resp.SetBlock();
                    resp.UserMessage = "No user ID found.";
                    return Ok(resp);
                }
                
                if (!Guid.TryParse(request.ObjectId, out var id))
                {
                    _logger.LogError("Object ID {ObjectId} is not a valid UUID.", request.ObjectId);
                    resp.SetBlock();
                    resp.UserMessage = $"Object ID {request.ObjectId} is not a valid UUID.";
                    return Ok(resp);
                }

                var user = _dbContext.Users.Include(u => u.RoleAssignments).ThenInclude(ra => ra.Role).ThenInclude(ra => ra.Scopes).Where(u => u.Id == id).ToList();

                if (user.Count < 1)
                {
                    _logger.LogError("User not found with ID {ObjectId}", id);
                    resp.SetBlock();
                    resp.UserMessage = $"No such user found with ID {id}.";
                    return Ok(resp);
                }

                var products = _dbContext.Licenses.Include(l => l.LicenseProducts)
                                                 .ThenInclude(lp => lp.Features)
                                                 .Where(l => l.TenantId == user[0].TenantId &&
                                                        l.State == LicenseState.Active &&
                                                        l.StartDate <= DateTimeOffset.UtcNow &&
                                                        l.EndDate > DateTimeOffset.UtcNow);

                var enabledFeatures = products.SelectMany(p => p.LicenseProducts.SelectMany(lp => lp.Features.Where(f => f.Enabled)))
                                              .Select(o => new {
                                                  featureId = o.FeatureId,
                                                  featureName = o.Name,
                                                  licenseId = o.LicenseProduct.LicenseId,
                                                  productName = o.LicenseProduct.Product.Name,
                                                  productId = o.LicenseProduct.Product.Id
                                              });

                if (!enabledFeatures.Any())
                {
                    _logger.LogError("No valid licenses exist for user with ID {ObjectId}", id);
                    resp.SetBlock();
                    resp.UserMessage = $"No valid licenses exist for user with ID {id}.";
                    return Ok(resp);
                }

                resp.SetContinue();
                resp.TenantId = user[0].TenantId.ToString();
                resp.Scopes = string.Join(' ', user[0].RoleAssignments.SelectMany(r => r.Role.Scopes.Select(s => s?.ToString())).Distinct());
                resp.Products = JsonSerializer.Serialize(enabledFeatures);
                return Ok(resp);
            }
        }
    }
}
