﻿using System.Collections.Generic;
using Ethos.TenantConfig;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Ethos.TenantConfig.Migrations
{
    /// <inheritdoc />
    public partial class DynamicMigration_638786205723000966 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Dictionary<string, TenantConfigSecretValue>>(
                name: "Secrets",
                table: "TenantConfig",
                type: "jsonb",
                nullable: false);

            migrationBuilder.AddColumn<Dictionary<string, TenantConfigStorageValue>>(
                name: "Storage",
                table: "TenantConfig",
                type: "jsonb",
                nullable: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Secrets",
                table: "TenantConfig");

            migrationBuilder.DropColumn(
                name: "Storage",
                table: "TenantConfig");
        }
    }
}
