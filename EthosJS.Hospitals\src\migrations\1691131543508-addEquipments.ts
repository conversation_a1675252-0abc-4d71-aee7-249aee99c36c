import { MigrationInterface, QueryRunner } from 'typeorm';

export class addEquipments1691131543508 implements MigrationInterface {
    name = 'addEquipments1691131543508'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "studies" ADD "equipments" jsonb NOT NULL DEFAULT \'{}\'');
      await queryRunner.query('ALTER TABLE "schedules" ADD "equipments" jsonb NOT NULL DEFAULT \'{}\'');
      await queryRunner.query('ALTER TABLE "facilities" ADD "equipments" jsonb NOT NULL DEFAULT \'{}\'');
      await queryRunner.query('ALTER TABLE "bed_schedules" ADD "equipments" jsonb NOT NULL DEFAULT \'{}\'');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "bed_schedules" DROP COLUMN "equipments"');
      await queryRunner.query('ALTER TABLE "facilities" DROP COLUMN "equipments"');
      await queryRunner.query('ALTER TABLE "schedules" DROP COLUMN "equipments"');
      await queryRunner.query('ALTER TABLE "studies" DROP COLUMN "equipments"');
    }

}
