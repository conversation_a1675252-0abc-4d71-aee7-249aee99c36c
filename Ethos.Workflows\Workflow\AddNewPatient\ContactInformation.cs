// THIS FILE IS AUTO-GENERATED
// DO NOT EDIT

using System.Collections.Immutable;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using Ethos.Model.Types;
using Ethos.Utilities;

namespace Ethos.Workflows.Workflow.AddNewPatient;

public sealed record ContactInformation(
    IReadOnlyList<PhoneNumberContact> PhoneNumbers,
    IReadOnlyList<EmailContact> Emails,
    IReadOnlyList<EmergencyContact> EmergencyContacts);
