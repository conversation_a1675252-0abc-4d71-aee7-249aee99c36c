using System.Text.Json;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Utilities;
using Ethos.Workflows.Database;
using Microsoft.EntityFrameworkCore;

namespace Ethos.Workflows.Workflow;

public interface IWorkflowState;
public interface IWorkflowTransition;

public interface IAbstractFlow;

public sealed record FlowContext(
    Guid InstanceId,
    IReadOnlyDictionary<string, Guid> EntityLinks);
public sealed record FlowContext<TState>(
    Guid InstanceId,
    TState OldState,
    IReadOnlyDictionary<string, Guid> EntityLinks);

public interface IWorkflowName
{
    string Name { get; }
}
public sealed record WorkflowName<TState>(string Name) : IWorkflowName;

public interface IWorkflowStateName
{
    string Name { get; }
}
public sealed record WorkflowStateName<TState>(string Name) : IWorkflowStateName;

public interface IWorkflowTransitionName
{
    string Name { get; }
}
public sealed record WorkflowTransitionName<TTransition>(string Name) : IWorkflowTransitionName;

public sealed record KeyedTransition(
    string Key,
    DateTime? Timestamp,
    JsonObject? Data);

public sealed record EntityLink(
    string EntityType,
    Guid EntityId);

public sealed record WorkflowState<TStateData>(
    Guid Id,
    IReadOnlyDictionary<string, Guid> EntityLinks,
    TStateData? StateData,
    JsonObject? ErrorJson,
    IReadOnlyList<KeyedTransition> PastTransitions,
    IReadOnlyList<KeyedTransition> DraftTransitions);

public abstract record ValidationExpr
{
    private ValidationExpr()
    {
    }

    public sealed record BinOp(ValidationExpr Left, string Op, ValidationExpr Right) : ValidationExpr;
    public sealed record StringLiteral(string Value) : ValidationExpr;
    public sealed record RealLiteral(double Value) : ValidationExpr;
    public sealed record IntegerLiteral(int Value) : ValidationExpr;
    public sealed record Variable(string Name) : ValidationExpr;
    public sealed record Call(string Name, IReadOnlyList<ValidationExpr> Args) : ValidationExpr;
}

public sealed record ValidationRule(
    string Name,
    ValidationExpr Expr,
    string Message);

public sealed record ValidationResult(
    bool IsValid,
    List<Issue> Warning,
    List<Issue> Error);

public interface IWorkflowEngine
{
    Task<IReadOnlyList<Guid>> GetInstancesAsync<TState>(
        WorkflowName<TState> name,
        IReadOnlyDictionary<string, Guid>? entityLinks = null);

    Task<WorkflowState<TState>?> GetInstanceAsync<TState>(
        WorkflowName<TState> name,
        Guid instanceId);

    Task<Guid> StartNewWorkflowAsync<TState>(
        WorkflowName<TState> name, 
        IReadOnlyDictionary<string, Guid> entityLinks,
        bool commit = true);

    Task<Guid> CreateOrUpdateInstanceAsync<TState>(
        WorkflowName<TState> name,
        Guid? instanceId,
        IWorkflowTransitionName transitionName,
        string transitionData,
        IWorkflowStateName newStateName,
        TState instance,
        bool commit = true);

    Task UpdateInstanceAsync<TState>(
        WorkflowName<TState> name,
        Guid instanceId,
        IWorkflowTransitionName transitionName,
        string transitionData,
        IWorkflowStateName newStateName,
        TState instance,
        bool commit = true);

    Task RewindState<TState>(
        WorkflowName<TState> name,
        Guid instanceId,
        string? stateName,
        bool commit = true);

    Task CommitInstanceAsync<TState>(
        WorkflowName<TState> name,
        Guid instanceId,
        bool commit = true);

    Task AttachTransientErrorAsync<TState>(
        WorkflowName<TState> name,
        Guid instanceId,
        Exception ex,
        bool commit = true);

    Task CreateOrAttachDraftAsync<TState>(
        WorkflowName<TState> name, Guid? instanceId,
        string transitionName,
        JsonElement transitionData,
        bool commit = true);
}

public class WorkflowEngine : IWorkflowEngine
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<WorkflowEngine> _logger;

    public WorkflowEngine(AppDbContext dbContext, ILogger<WorkflowEngine> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<IReadOnlyList<Guid>> GetInstancesAsync<TState>(
        WorkflowName<TState> name,
        IReadOnlyDictionary<string, Guid>? entityLinks = null)
    {
        // Fast‑path: caller just wants “all instances of this workflow”.
        if (entityLinks is null || entityLinks.Count == 0)
        {
            return await _dbContext.WorkflowInstances
                .Where(i => i.WorkflowKey == name.Name)
                .Select(i => i.Id)
                .ToListAsync();
        }

        // 1.  Pull (instanceId, entityType, entityId) for the chosen workflow
        //     in a single round‑trip.
        var linkRows = await (
            from i in _dbContext.WorkflowInstances
            where i.WorkflowKey == name.Name
            join l in _dbContext.WorkflowEntityLinks            // navigation not required
                on i.Id equals l.InstanceId
            select new { i.Id, l.EntityType, l.EntityId }
        ).ToListAsync();

        // 2.  Keep only those instanceIds that contain *every* requested link.
        var matchingIds = linkRows
            .GroupBy(r => r.Id)
            .Where(g => entityLinks.All(kv =>
                g.Any(r => r.EntityType == kv.Key && r.EntityId == kv.Value)))
            .Select(g => g.Key)
            .ToList();

        return matchingIds;
    }

    public async Task<Guid> StartNewWorkflowAsync<TState>(
        WorkflowName<TState> name, 
        IReadOnlyDictionary<string, Guid> entityLinks,
        bool commit = true)
    {
        var instance = new WorkflowInstanceDbo(name.Name);
        _dbContext.WorkflowInstances.Add(instance);
        
        // Add links
        foreach (var link in entityLinks)
        {
            _dbContext.WorkflowEntityLinks.Add(new WorkflowEntityLinkDbo
            {
                InstanceId = instance.Id,
                EntityType = link.Key,
                EntityId = link.Value
            });
        }
        
        if (commit) await _dbContext.SaveChangesAsync();
        return instance.Id;
    }

    public async Task<WorkflowState<TState>?> GetInstanceAsync<TState>(WorkflowName<TState> name, Guid instanceId)
    {
        var workflowAndError = await _dbContext.WorkflowInstances
            .Where(i => i.WorkflowKey == name.Name && i.Id == instanceId)
            .Select(i => new { i.CurrentState, i.TransientErrorJson })
            .FirstOrDefaultAsync();

        if (workflowAndError == null) return null;

        var entityLinks = await _dbContext.WorkflowEntityLinks
            .Where(l => l.InstanceId == instanceId)
            .Select(l => new EntityLink(l.EntityType, l.EntityId))
            .ToDictionaryAsync(l => l.EntityType, l => l.EntityId);
        
        var transitionList = await _dbContext.WorkflowInstanceTransitions
            .Where(t => t.WorkflowInstanceId == instanceId)
            .OrderBy(t => t.SequenceId)
            .ToListAsync();

        var draftList = await _dbContext.WorkflowDraftTransitions
            .Where(t => t.WorkflowInstanceId == instanceId)
            .ToListAsync();

        JsonObject? decodedError = null;
        if (workflowAndError.TransientErrorJson != null)
        {
            decodedError = JsonSerializer.Deserialize<JsonObject>(workflowAndError.TransientErrorJson, JsonSerializerOptions.Web);
        }
        var result = JsonSerializer.Deserialize<TState?>(workflowAndError.CurrentState, JsonSerializerOptions.Web);
        return new WorkflowState<TState>(
            Id: instanceId,
            EntityLinks: entityLinks,
            StateData: result,
            ErrorJson: decodedError,
            transitionList
                .Select(t => new KeyedTransition(
                    t.TransitionName,
                    t.UpdatedAt,
                    JsonSerializer.Deserialize<JsonObject>(t.TransitionData, JsonSerializerOptions.Web))
                ).ToArray(),
            draftList
                .Select(t => new KeyedTransition(
                    t.TransitionName,
                    t.UpdatedAt,
                    JsonSerializer.Deserialize<JsonObject>(t.TransitionData, JsonSerializerOptions.Web))
                ).ToArray());
    }

    public async Task<Guid> CreateOrUpdateInstanceAsync<TState>(
        WorkflowName<TState> name,
        Guid? instanceId,
        IWorkflowTransitionName transitionName,
        string transitionData,
        IWorkflowStateName newStateName,
        TState instance,
        bool commit = true)
    {
        var utcNow = DateTime.UtcNow;

        WorkflowInstanceDbo? entity = null;
        if (instanceId != null)
        {
            entity = await _dbContext.WorkflowInstances
                .Where(i => i.WorkflowKey == name.Name && i.Id == instanceId)
                .FirstOrDefaultAsync();
            if (entity == null) throw new InvalidOperationException("Instance not found");
        }
        else
        {
            entity = new WorkflowInstanceDbo(name.Name);
            _dbContext.WorkflowInstances.Add(entity);
        }

        var newSequenceId = entity.CurrentSequence + 1;
        entity.CurrentSequence = newSequenceId;
        entity.UpdatedAt = utcNow;
        entity.CurrentState = JsonSerializer.Serialize(instance, JsonSerializerOptions.Web);

        _dbContext.WorkflowInstanceTransitions.Add(new WorkflowTransitionDbo(
            workflowInstanceId: entity.Id,
            sequenceId: newSequenceId,
            transitionName: transitionName.Name,
            transitionData: transitionData,
            newStateName: newStateName.Name,
            newStateData: entity.CurrentState));

        if (commit) await _dbContext.SaveChangesAsync();
        return entity.Id;
    }

    public async Task UpdateInstanceAsync<TState>(
        WorkflowName<TState> name,
        Guid instanceId,
        IWorkflowTransitionName transitionName,
        string transitionData,
        IWorkflowStateName newStateName,
        TState instance,
        bool commit = true)
    {
        await CreateOrUpdateInstanceAsync(name, instanceId, transitionName, transitionData, newStateName, instance, commit);
    }

    public async Task RewindState<TState>(
        WorkflowName<TState> name,
        Guid instanceId,
        string? stateName,
        bool commit = true)
    {
        var entity = await _dbContext.WorkflowInstances
            .Where(i => i.WorkflowKey == name.Name && i.Id == instanceId)
            .FirstOrDefaultAsync();
        if (entity == null) throw new InvalidOperationException("Instance not found");

        if (stateName == null)
        {
            entity.CurrentState = "null";
            entity.CurrentSequence = 0;
            entity.UpdatedAt = DateTime.UtcNow;

            var transitionsToDraft = await _dbContext.WorkflowInstanceTransitions
                .Where(t => t.WorkflowInstanceId == instanceId)
                .ToListAsync();

            foreach (var t in transitionsToDraft)
            {
                await CreateOrAttachDraftAsync(
                    name, instanceId, t.TransitionName,
                    JsonSerializer.Deserialize<JsonElement>(t.TransitionData, JsonSerializerOptions.Web), false);
                _dbContext.WorkflowInstanceTransitions.Remove(t);
            }
        }
        else
        {
            // Find the last transition that led to the state we want to rewind to
            var transition = await _dbContext.WorkflowInstanceTransitions
                .Where(t => t.WorkflowInstanceId == instanceId && t.NewStateName == stateName)
                .OrderByDescending(t => t.SequenceId)
                .FirstOrDefaultAsync();
            if (transition == null) throw new InvalidOperationException("Transition not found");

            entity.CurrentState = transition.NewStateData;
            entity.CurrentSequence = transition.SequenceId;
            entity.UpdatedAt = DateTime.UtcNow;

            // Make all transitions after the one we're rewinding to draft
            var transitionsToDraft = await _dbContext.WorkflowInstanceTransitions
                .Where(t => t.WorkflowInstanceId == instanceId && t.SequenceId > transition.SequenceId)
                .ToListAsync();

            foreach (var t in transitionsToDraft)
            {
                await CreateOrAttachDraftAsync(
                    name, instanceId, t.TransitionName,
                    JsonSerializer.Deserialize<JsonElement>(t.TransitionData, JsonSerializerOptions.Web), false);
                _dbContext.WorkflowInstanceTransitions.Remove(t);
            }
        }

        if (commit) await _dbContext.SaveChangesAsync();
    }

    public async Task CommitInstanceAsync<TState>(WorkflowName<TState> name, Guid instanceId, bool commit = true)
    {
        var entity = await _dbContext.WorkflowInstances
            .Where(i => i.WorkflowKey == name.Name && i.Id == instanceId)
            .FirstOrDefaultAsync();
        if (entity == null) throw new InvalidOperationException("Instance not found");

        entity.TransientErrorJson = null;
        entity.CompletedAt = DateTime.UtcNow;

        if (commit) await _dbContext.SaveChangesAsync();
    }

    public async Task AttachTransientErrorAsync<TState>(WorkflowName<TState> name, Guid instanceId, Exception ex, bool commit = true)
    {
        var entity = await _dbContext.WorkflowInstances
            .Where(i => i.WorkflowKey == name.Name && i.Id == instanceId)
            .FirstOrDefaultAsync();
        if (entity == null) throw new InvalidOperationException("Instance not found");

        // Log the error
        _logger.LogError(ex, "Transient error occurred");

        entity.TransientErrorJson = JsonSerializer.Serialize(new
        {
            ex.GetType().FullName,
            ex.Message,
            ex.StackTrace
        }, JsonSerializerOptions.Web);

        if (commit) await _dbContext.SaveChangesAsync();
    }

    public async Task CreateOrAttachDraftAsync<TState>(
        WorkflowName<TState> name, Guid? instanceId,
        string transitionName,
        JsonElement transitionData,
        bool commit = true)
    {
        WorkflowInstanceDbo? entity = null;

        if (instanceId != null)
        {
            entity = await _dbContext.WorkflowInstances
                .Where(i => i.WorkflowKey == name.Name && i.Id == instanceId)
                .FirstOrDefaultAsync();
            if (entity == null)
                throw new InvalidOperationException("Workflow instance not found");
        }
        else
        {
            entity = new WorkflowInstanceDbo(name.Name);
            _dbContext.WorkflowInstances.Add(entity);
        }

        // If there is already a draft transition, update it
        var draftTransition = await _dbContext.WorkflowDraftTransitions
            .Where(t => t.WorkflowInstanceId == entity.Id && t.TransitionName == transitionName)
            .FirstOrDefaultAsync();

        if (draftTransition != null)
        {
            draftTransition.TransitionData = JsonSerializer.Serialize(transitionData, JsonSerializerOptions.Web);
            draftTransition.UpdatedAt = DateTime.UtcNow;
        }
        else
        {
            _dbContext.WorkflowDraftTransitions.Add(new WorkflowDraftTransitionDbo(
                workflowInstanceId: entity.Id,
                transitionName: transitionName,
                transitionData: JsonSerializer.Serialize(transitionData, JsonSerializerOptions.Web)));
        }

        if (commit) await _dbContext.SaveChangesAsync();
    }
}