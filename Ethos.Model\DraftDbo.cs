using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Ethos.Model;

public class DraftIndexDbo
{
    public required string Name { get; set; }
    public required string Value { get; set; }
    public required long? LongValue { get; set; }
    public required double? DoubleValue { get; set; }
}

public class DraftDbo : IAuditableEntity<DraftDbo>
{
    public required EntityType EntityType { get; set; }
    public required Guid EntityId { get; set; }
    public required JsonObject Data { get; set; } = null!;
    public ICollection<DraftIndexDbo> Indexes { get; set; } = new List<DraftIndexDbo>();

    public new static void Register(ModelBuilder modelBuilder) =>
        modelBuilder.HasDefaultSchema(IEntity.DefaultSchema).Entity<DraftDbo>(Register);

    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false,
        DefaultIgnoreCondition = JsonIgnoreCondition.Never
    };
    
    public new static void Register(EntityTypeBuilder<DraftDbo> entity)
    {
        IAuditableEntity<DraftDbo>.Register(entity);
        entity.Property(e => e.EntityType)
            .IsRequired()
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<EntityType>(v, true));
        entity.Property(e => e.EntityId)
            .IsRequired();
        entity.Property(e => e.Data).HasConversion(
                v => JsonSerializer.Serialize(v, JsonSerializerOptions),
                v => JsonSerializer.Deserialize<JsonObject>(v, JsonSerializerOptions)!
            )
            .HasColumnType("jsonb")
            .IsRequired(false);
        
        entity.OwnsMany(e => e.Indexes, indexes =>
        {
            indexes.ToTable("Index", IEntity.DefaultSchema);
            indexes.WithOwner().HasForeignKey("DraftDboId");
            indexes.Property<int>("Id").ValueGeneratedOnAdd();
            indexes.HasKey("Id");
            indexes.Property(i => i.Name).HasMaxLength(64).IsRequired();
            indexes.Property(i => i.Value).HasMaxLength(512).IsRequired(false);
            indexes.Property(i => i.LongValue).IsRequired(false);
            indexes.Property(i => i.DoubleValue).IsRequired(false);
            indexes.HasIndex(i => i.Name)
                .HasDatabaseName($"IX_{nameof(DraftDbo)}_{nameof(DraftIndexDbo.Name)}");
            indexes.HasIndex(i => i.Value)
                .HasDatabaseName($"IX_{nameof(DraftDbo)}_{nameof(DraftIndexDbo.Value)}");
            indexes.HasIndex(i => i.LongValue)
                .HasDatabaseName($"IX_{nameof(DraftDbo)}_{nameof(DraftIndexDbo.LongValue)}");
            indexes.HasIndex(i => i.DoubleValue)
                .HasDatabaseName($"IX_{nameof(DraftDbo)}_{nameof(DraftIndexDbo.DoubleValue)}");
        });
        
        entity.HasIndex(e => e.EntityId);
        
        entity.HasIndex(e => new { e.EntityType, e.EntityId })
            .IsUnique();
    }
}

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(DraftQ.WithId), "WithId")]
[JsonDerivedType(typeof(DraftQ.WithEntityId), "WithEntityId")]
[JsonDerivedType(typeof(DraftQ.WithIndexValue), "WithIndexValue")]
public abstract record DraftQ : IPrimitiveQuery
{
    public sealed record WithId(Guid Id) : DraftQ;
    public sealed record WithEntityId(EntityType Type, Guid Id) : DraftQ;
    public sealed record WithIndexValue(string Name, JsonNode Value) : DraftQ;

    public static DraftQ HasId(Guid id) => new WithId(id);
    public static DraftQ HasEntityId(EntityType type, Guid id) => new WithEntityId(type, id);
    public static DraftQ HasIndexValue(string name, JsonNode value) => new WithIndexValue(name, value);

    public Expression BuildPredicateBody(ParameterExpression self)
    {
        return this switch
        {
            WithId id => Expression.Equal(Expression.Property(self, nameof(DraftDbo.Id)), Expression.Constant(id.Id)),
            WithEntityId entityId => 
                Expression.AndAlso(
                    Expression.Equal(Expression.Property(self, nameof(DraftDbo.EntityType)), Expression.Constant(entityId.Type)),
                    Expression.Equal(Expression.Property(self, nameof(DraftDbo.EntityId)), Expression.Constant(entityId.Id))
                ),
            WithIndexValue withValue => BuildIndexValuePredicate(self, withValue.Name, withValue.Value),
            _ => throw new NotSupportedException($"Query type {this.GetType().Name} is not supported.")
        };
    }
    
    private static Expression BuildIndexValuePredicate(
        ParameterExpression self, string name, JsonNode value)
    {
        var indexesProp = Expression.Property(self, nameof(DraftDbo.Indexes));
        var idxParam    = Expression.Parameter(typeof(DraftIndexDbo), "idx");

        var nameProp    = Expression.Property(idxParam, nameof(DraftIndexDbo.Name));
        var valueProp   = Expression.Property(idxParam, nameof(DraftIndexDbo.Value));

        var nameMatch   = Expression.Equal(nameProp, Expression.Constant(name));
        var valueMatch  = Expression.Equal(valueProp, Expression.Constant(value.ToJsonString()));

        var lambda      = Expression.Lambda<Func<DraftIndexDbo, bool>>(
            Expression.AndAlso(nameMatch, valueMatch),
            idxParam);

        return Expression.Call(
            typeof(Enumerable),
            nameof(Enumerable.Any),
            new[] { typeof(DraftIndexDbo) },
            indexesProp,
            lambda);
    }
}