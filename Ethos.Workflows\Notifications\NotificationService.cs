using System.Text.Json.Serialization;
using Ethos.Model;
using Ethos.Workflows.Database;

namespace Ethos.Workflows.Notifications;

public interface INotificationService
{
    Task<Notification> CreateNotificationAsync(
        string uniqueKey,
        string severity,
        string category,
        string message,
        bool isHighUrgency = false);

    Task NotifyRolesAsync(IEnumerable<string> roles, string title, string message,
        string category, NotificationSeverity severity);

    Task NotifyUserAsync(Guid userId, string title, string message, string category,
        NotificationSeverity severity);

    // Possibly more methods for marking read, escalations, etc.
}

public class NotificationService : INotificationService
{
    private readonly AppDbContext _dbContext;
    // private readonly ISmsSender _smsSender;          // Twilio
    // private readonly IEmailSender _emailSender;      // SendGrid
    private readonly IRealTimeHub _realTimeHub;      // SignalR/WS

    public NotificationService(
        AppDbContext dbContext,
        // ISmsSender smsSender,
        // IEmailSender emailSender,
        IRealTimeHub realTimeHub)
    {
        _dbContext = dbContext;
        // _smsSender = smsSender;
        // _emailSender = emailSender;
        _realTimeHub = realTimeHub;
    }

    public async Task<Notification> CreateNotificationAsync(
        string uniqueKey, 
        string severity,
        string category,
        string message,
        bool isHighUrgency = false)
    {
        var notification = new Notification
        {
            Id = Guid.NewGuid(),
            UniqueKey = uniqueKey,
            Severity = severity,
            Category = category,
            Message = message,
            Timestamp = DateTime.UtcNow,
            IsHighUrgency = isHighUrgency
        };
        _dbContext.Notifications.Add(notification);
        await _dbContext.SaveChangesAsync();
        return notification;
    }

    public async Task NotifyRolesAsync(IEnumerable<string> roles, string title, string message,
        string category, NotificationSeverity severity)
    {
        // 1. Create single Notification row
        var notification = await CreateNotificationAsync(
            Guid.NewGuid().ToString(),
            severity.ToString(),
            category,
            $"{title}: {message}",
            severity == NotificationSeverity.Error);

        // 2. For each user in those roles, add a record in UserNotifications
        var users = await _dbContext.GetUsersByRolesAsync(roles); // your custom method
        foreach (var user in users)
        {
            var userNotification = new UserNotification
            {
                NotificationId = notification.Id,
                UserId = user.Id,
                IsRead = false,
                CreatedAt = DateTime.UtcNow
            };
            _dbContext.UserNotifications.Add(userNotification);
        }
        await _dbContext.SaveChangesAsync();

        // 3. In-app (SignalR)
        await _realTimeHub.BroadcastNotification(notification, users.Select(u => u.Id).ToList());

        // // 4. Email or SMS (depending on severity)
        // //    This can also be done asynchronously via a queue.
        // if (notification.IsHighUrgency)
        // {
        //     foreach (var user in users)
        //     {
        //         if (user.PhoneNumber != null)
        //             await _smsSender.SendSmsAsync(user.PhoneNumber, message);
        //
        //         if (user.Email != null)
        //             await _emailSender.SendEmailAsync(user.Email, title, message);
        //     }
        // }
        // else
        // {
        //     // Default channel is email
        //     foreach (var user in users)
        //     {
        //         if (user.Email != null)
        //             await _emailSender.SendEmailAsync(user.Email, title, message);
        //     }
        // }
    }

    public async Task NotifyUserAsync(Guid userId, string title, string message,
        string category, NotificationSeverity severity)
    {
        // Similar logic but for a single user
        var notification = await CreateNotificationAsync(
            Guid.NewGuid().ToString(),
            severity.ToString(),
            category,
            $"{title}: {message}",
            severity == NotificationSeverity.Error);

        var userNotification = new UserNotification
        {
            NotificationId = notification.Id,
            UserId = userId,
            IsRead = false,
            CreatedAt = DateTime.UtcNow
        };
        _dbContext.UserNotifications.Add(userNotification);
        await _dbContext.SaveChangesAsync();

        // Real-time push
        await _realTimeHub.SendNotificationToUser(notification, userId);

        // Possibly email/SMS
        // ...
    }
}

[JsonConverter(typeof(StringEnumConverter<NotificationSeverity>))]
public enum NotificationSeverity
{
    Info,
    Warning,
    Error
}