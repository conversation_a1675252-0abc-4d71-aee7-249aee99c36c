﻿"""
ethos_common.py  – shared helpers for all autogenerated clients.
Requires Python 3.11+   •   pip install aiohttp
"""

from __future__ import annotations
from dataclasses import dataclass, asdict, is_dataclass
from typing import Any, Generic, List, TypeVar, Union, Mapping, Dict, Sequence, TypeAlias

import asyncio, base64, logging, urllib.parse
import json as jsonlib
from uuid import UUID
import aiohttp

JsonArray: TypeAlias = List['Json']
JsonDict: TypeAlias = Dict[str, 'Json']
Json: TypeAlias = Union[str, int, float, bool, None, JsonArray, JsonDict]
DateOnly: TypeAlias = str
TimeOnly: TypeAlias = str  # ISO 8601 format, e.g. "12:34:56"
DateTime: TypeAlias = str  # ISO 8601 format, e.g. "2023-10-01T12:34:56Z"
DateTimeOffset: TypeAlias = str  # ISO 8601 format with offset, e.g. "2023-10-01T12:34:56+00:00"

# -------------------------------------------------------------------------------------------------
#  GENERIC HELPERS
# -------------------------------------------------------------------------------------------------
TInput  = TypeVar('TInput')
TOutput = TypeVar('TOutput')
TQuery  = TypeVar('TQuery')
TPrim   = TypeVar('TPrim')  # primitive query node

@dataclass(slots=True)
class PagingParameters:
    offset: int = 0
    limit: int = 100
    def as_query(self) -> dict[str, str]:
        return {'offset': str(self.offset), 'limit': str(self.limit)}

@dataclass(slots=True)
class PagedResponse(Generic[TOutput]):
    items: List[TOutput]
    total: int | None = None
    @classmethod
    def from_dict(cls, raw: dict, typ):
        return cls(items=[typ(**x) for x in raw.get('items', [])], total=raw.get('total'))

@dataclass(slots=True)
class Issue:
    paths: List[str]
    message: str
    issueId: UUID
    data: JsonDict | None = None
    @classmethod
    def from_dict(cls, raw: dict):
        print(f'Issue.from_dict: {raw}')
        return cls(
            paths=raw['paths'],
            message=raw['message'],
            issueId=UUID(raw['issueId']),
            data=raw.get('data')
        )

@dataclass(slots=True)
class DraftDto:
    id: UUID
    entity_type: str
    entity_id: UUID
    data: JsonDict
    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=UUID(raw['id']),
            entity_type=raw['entityType'],
            entity_id=UUID(raw['entityId']),
            data=raw['data']
        )

@dataclass(slots=True)
class ValidatedDraftDto:
    id: UUID
    entity_type: str
    entity_id: UUID
    data: JsonDict
    errors: List[Issue] | None = None
    warnings: List[Issue] | None = None
    @classmethod
    def from_dict(cls, raw: dict):
        raw_errors = raw.get('errors')
        raw_warnings = raw.get('warnings')
        return cls(
            id=UUID(raw['id']),
            entity_type=raw['entityType'],
            entity_id=UUID(raw['entityId']),
            data=raw['data'],
            errors=[Issue.from_dict(e) for e in raw_errors] if raw_errors else None,
            warnings=[Issue.from_dict(w) for w in raw_warnings] if raw_warnings else None
        )

def _asdict(obj: Any) -> dict:  # python → json safe dict
    if hasattr(obj, 'to_json'): return obj.to_json()
    return asdict(obj) if is_dataclass(obj) else obj.__dict__

def _json_or_none(obj: Any | None) -> Any | None:
    if obj is None: return None
    if hasattr(obj, 'to_json'): return obj.to_json()
    if is_dataclass(obj): return _asdict(obj)
    raise TypeError(f'Unsupported object for JSON: {type(obj)}')

def _strip_etag(resp: aiohttp.ClientResponse) -> str | None:
    etag = resp.headers.get('ETag');  return etag.strip('"') if etag else None

def _fmt_etag(tag: str | None) -> str:  return f'"{tag}"' if tag else ''

def _query_str(p: PagingParameters | None = None, q: Any | None = None) -> str:
    qs: dict[str, str] = {}
    if p: qs |= p.as_query()
    if q is not None:
        payload = jsonlib.dumps(_json_or_none(q), default=str).encode()
        qs['queryBase64'] = base64.b64encode(payload).decode()
    return '?' + urllib.parse.urlencode(qs) if qs else ''

# -------------------------------------------------------------------------------------------------
#  GENERIC  QueryDto ADT  (All / Literal / Not / And / Or)
# -------------------------------------------------------------------------------------------------
class QueryDto(Generic[TPrim]):
    """Discriminated-union equivalent matching the C# QueryDto ADT."""

    All : ClassVar[type[All[TPrim]]] = None  # type: ignore
    Literal: ClassVar[type[Literal[TPrim]]] = None  # type: ignore
    Not: ClassVar[type[Not[TPrim]]] = None  # type: ignore
    And: ClassVar[type[And[TPrim]]] = None  # type: ignore
    Or: ClassVar[type[Or[TPrim]]] = None  # type: ignore

    def to_json(self) -> dict:  raise NotImplementedError

    @staticmethod
    def _decode(raw: dict) -> 'QueryDto':
        tp = raw.get('$type')
        match tp.lower():
            case 'all':      return QueryDto.All()
            case 'literal':  return QueryDto.Literal(raw['value'])
            case 'not':      return QueryDto.Not(QueryDto._decode(raw['expr']))
            case 'and':      return QueryDto.And([QueryDto._decode(e) for e in raw['exprs']])
            case 'or':       return QueryDto.Or([QueryDto._decode(e) for e in raw['exprs']])
        raise ValueError(f'Unknown QueryDto type {tp}')


# ---- concrete records --------------------------------------------------------------------
@dataclass(slots=True)
class All(Generic[TPrim], QueryDto[TPrim]):
    def to_json(self): return {'$type': 'All'}

@dataclass(slots=True)
class Literal(Generic[TPrim], QueryDto[TPrim]):
    value: TPrim
    def to_json(self): return {'$type': 'Literal', 'value': _json_or_none(self.value)}

@dataclass(slots=True)
class Not(Generic[TPrim], QueryDto[TPrim]):
    expr: 'QueryDto[TPrim]'
    def to_json(self): return {'$type': 'Not', 'expr': self.expr.to_json()}

@dataclass(slots=True)
class And(Generic[TPrim], QueryDto[TPrim]):
    exprs: List['QueryDto[TPrim]']
    def to_json(self): return {'$type': 'And', 'exprs': [e.to_json() for e in self.exprs]}

@dataclass(slots=True)
class Or(Generic[TPrim], QueryDto[TPrim]):
    exprs: List['QueryDto[TPrim]']
    def to_json(self): return {'$type': 'Or', 'exprs': [e.to_json() for e in self.exprs]}

QueryDto.All = All
QueryDto.Literal = Literal
QueryDto.Not = Not
QueryDto.And = And
QueryDto.Or = Or

# -------------------------------------------------------------------------------------------------
#  BASE ASYNC API CLIENT  (aiohttp)
# -------------------------------------------------------------------------------------------------
class HttpClientBase:
    _json_hdr = {'Content-Type': 'application/json'}

    def __init__(self, session: aiohttp.ClientSession, route: str, output_type):
        if session._base_url is None:
            raise ValueError('aiohttp.ClientSession must be created with base_url=')

        self._s = session
        self._path = f'/api/{route.strip("/")}'
        self._out = output_type
        self._bearer: str | None = None
        self._log = logging.getLogger(route)

    def set_bearer_token(self, token: str):  self._bearer = token

    # ---- internal -----------------------------------------------------------------------------
    async def _req(
        self,
        method: str,
        url: str,
        *,
        json: Any | None = None,
        data: Any | None = None,
        headers: Mapping[str, str] | None = None,
        **kw
    ):
        hdr: dict[str, str] = {}
        if self._bearer:
            hdr["Authorization"] = f"Bearer {self._bearer}"
        if headers:
            hdr.update(headers)

        if json is not None and data is None:
            hdr.setdefault("Content-Type", "application/json")
            kw["json"] = json            # aiohttp will serialize
        else:
            kw["data"] = data            # could be FormData, bytes, async‑gen

        self._log.debug('HTTP %%s %%s', method, url)

        # print('Payload: ', jsonlib.dumps(json, indent=2) if json else 'None')

        r = await self._s.request(method, url, headers=hdr, **kw)

        if r.status >= 400:
            text = await r.text()
            self._log.error('%%s %%s -> %%s\n%%s', method, url, r.status)
            try:
                jerror = jsonlib.loads(text)
                if isinstance(jerror, dict) and 'message' in jerror:
                    import io, textwrap
                    buf = io.StringIO()
                    def print_exception(e: dict, indent: int = 0):
                        buf.write(' ' * indent + f"{e['type']}: {e['message']}\n")
                        if 'stackTrace' in e and e['stackTrace']:
                            buf.write(textwrap.indent(e['stackTrace'], ' ' * (indent + 2)))
                        if 'innerException' in e and e['innerException']:
                            print_exception(e['innerException'], indent + 2)
                    print_exception(jerror)
                    self._log.error('Error details:\n%s', buf.getvalue())
                else:
                    self._log.error('Error response: %s', text)
            except jsonlib.JSONDecodeError:
                self._log.error('Failed to parse error response as JSON: %s', text)
            r.raise_for_status()
        return r

class EntityHttpClientBase(Generic[TInput, TOutput, TQuery], HttpClientBase):
    _json_hdr = {'Content-Type': 'application/json'}

    def __init__(self, session: aiohttp.ClientSession, route: str, output_type):
        super().__init__(session, route, output_type)

    # ---- CRUD ---------------------------------------------------------------------------------
    async def get_by_id(self, id_: UUID):
        r = await self._req('GET', f'{self._path}/{id_}')
        return self._out(**await r.json()), _strip_etag(r)

    async def get(self, query: QueryDto[TQuery] | None = None, paging: PagingParameters | None = None):
        r = await self._req('GET', self._path + _query_str(paging, query))
        return PagedResponse.from_dict(await r.json(), self._out)

    async def search(self, query: QueryDto[TQuery], paging: PagingParameters | None = None):
        r = await self._req('POST', self._path + '/search' + _query_str(paging), json=query.to_json())
        return PagedResponse.from_dict(await r.json(), self._out)

    async def create(self, dto: TInput):
        r = await self._req('POST', self._path, json=_asdict(dto))
        return self._out(**await r.json()), _strip_etag(r)

    async def put(self, id_: UUID, dto: TInput, etag: str | None = None):
        hdr = {'If-Match': _fmt_etag(etag)} if etag else {}
        r = await self._req('PUT', f'{self._path}/{id_}', json=_asdict(dto), headers=hdr)
        return self._out(**await r.json()), _strip_etag(r), r.status == 201

    async def patch(self, id_: UUID, dto: TInput, etag: str):
        hdr = {'If-Match': _fmt_etag(etag)}
        r = await self._req('PATCH', f'{self._path}/{id_}', json=_asdict(dto), headers=hdr)
        return self._out(**await r.json()), _strip_etag(r)

    # ---- Drafts -------------------------------------------------------------------------------
    async def create_draft(self, data: JsonDict | None = None, entity_id: UUID | None = None):
        """Creates a new draft, either from raw data or from an existing entity."""
        if data is not None and entity_id is not None:
            raise ValueError("Cannot provide both 'data' and 'entity_id' to create_draft.")

        qs = '' if entity_id is None else f'?entityId={entity_id}'
        r = await self._req('POST', f'{self._path}/draft{qs}', json=data)
        return ValidatedDraftDto.from_dict(await r.json()), _strip_etag(r)

    async def get_draft_by_id(self, draft_id: UUID):
        """Retrieves a draft by its ID."""
        r = await self._req('GET', f'{self._path}/draft/{draft_id}')
        return DraftDto.from_dict(await r.json()), _strip_etag(r)

    async def modify_draft(self, draft_id: UUID, data: JsonDict, etag: str):
        """Modifies an existing draft's data."""
        hdr = {'If-Match': _fmt_etag(etag)}
        r = await self._req('PUT', f'{self._path}/draft/{draft_id}', json=data, headers=hdr)
        return ValidatedDraftDto.from_dict(await r.json()), _strip_etag(r)

    async def validate_unsaved_draft(self, data: JsonDict):
        """Validates a draft's data."""
        r = await self._req('POST', f'{self._path}/draft/validate', json=data)
        return ValidatedDraftDto.from_dict(await r.json()), _strip_etag(r)

    async def validate_draft(self, draft_id: UUID):
        """Validates an existing draft."""
        r = await self._req('POST', f'{self._path}/draft/{draft_id}/validate')
        return ValidatedDraftDto.from_dict(await r.json()), _strip_etag(r)

    async def commit_draft(self, draft_id: UUID, etag: str):
        """Commits a draft, creating or updating the real entity."""
        hdr = {'If-Match': _fmt_etag(etag)}
        r = await self._req('POST', f'{self._path}/draft/{draft_id}/commit', headers=hdr)
        return self._out(**await r.json()), _strip_etag(r), r.status == 201
