import { MigrationInterface, QueryRunner } from 'typeorm';
import { IFacilityEquipment } from '@app/modules/facility/types';

export class changeFacilities1692938947760 implements MigrationInterface {
    name = 'changeFacilities1692938947760'

    public async up(queryRunner: QueryRunner): Promise<void> {
      const equipments = await queryRunner.query('SELECT * FROM equipments');
      const equipmentsMap = equipments.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      let offset = 0;
      const limit = 100;
      let facilities = await queryRunner.query('SELECT id, equipments FROM facilities OFFSET $1 LIMIT $2', [offset, limit]);

      while(facilities.length) {
        for (const facility of facilities) {
          const equipments = Object.keys(facility.equipments)
            .map((equipmentId) => {
              const equipmentCount = facility.equipments[equipmentId];
              const equipment = equipmentsMap[equipmentId];

              return {
                equipmentId,
                equipmentName: equipment.name,
                count: equipmentCount,
              };
            })
            .reduce((acc: Record<number, IFacilityEquipment>, item: any) => {
              acc[item.equipmentId] = item;

              return acc;
            }, {});


          await queryRunner.query('UPDATE facilities SET equipments = $1 WHERE id = $2', [equipments, facility.id]);
        }

        offset += limit;
        facilities = await queryRunner.query('SELECT id, equipments FROM facilities OFFSET $1 LIMIT $2', [offset, limit]);
      }

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      let offset = 0;
      const limit = 100;
      let facilities = await queryRunner.query('SELECT id, equipments FROM facilities OFFSET $1 LIMIT $2', [offset, limit]);

      while(facilities.length) {
        for (const facility of facilities) {
          const equipments = Object.values(facility.equipments)
            .reduce((acc: Record<number, number>, item: any) => {
              acc[item.equipmentId] = item.count;

              return acc;
            }, {});


          await queryRunner.query('UPDATE facilities SET equipments = $1 WHERE id = $2', [equipments, facility.id]);
        }

        offset += limit;
        facilities = await queryRunner.query('SELECT id, equipments FROM facilities OFFSET $1 LIMIT $2', [offset, limit]);
      }
    }
}
