﻿using System.Text.Json.Serialization;
using System.Text.Json;

namespace Ethos.ReferenceData.Client
{
    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataValueConverter : JsonConverter<object>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="typeToConvert"></param>
        /// <param name="options"></param>
        /// <returns></returns>
        /// <exception cref="JsonException"></exception>
        public override object? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            switch (reader.TokenType)
            {
                case JsonTokenType.String:
                    return reader.GetString();
                case JsonTokenType.Number:
                    if (reader.TryGetInt64(out long longValue))
                    {
                        return longValue;
                    }
                    return reader.GetDouble();
                case JsonTokenType.True:
                case JsonTokenType.False:
                    return reader.GetBoolean();
                case JsonTokenType.Null:
                    return null;
                case JsonTokenType.StartObject:
                    return ReadObject(ref reader, options);
                case JsonTokenType.StartArray:
                    return ReadArray(ref reader, options);
                default:
                    throw new JsonException($"Unexpected token type {reader.TokenType}");
            }
        }

        private object? ReadObject(ref Utf8JsonReader reader, JsonSerializerOptions options)
        {
            var dictionary = new Dictionary<string, object?>();

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return dictionary;
                }

                if (reader.TokenType != JsonTokenType.PropertyName)
                {
                    throw new JsonException($"Expected property name, but got {reader.TokenType}");
                }

                string? propertyName = reader.GetString();
                if (propertyName == null)
                    continue;  // Or handle null property names as needed

                reader.Read();
                dictionary[propertyName] = Read(ref reader, typeof(object), options);
            }

            throw new JsonException("Expected end object");
        }

        private object? ReadArray(ref Utf8JsonReader reader, JsonSerializerOptions options)
        {
            var list = new List<object?>();

            while (reader.Read() && reader.TokenType != JsonTokenType.EndArray)
            {
                list.Add(Read(ref reader, typeof(object), options));
            }

            return list;
        }

        public override void Write(Utf8JsonWriter writer, object? value, JsonSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNullValue();
            }
            else if (value is string stringValue)
            {
                writer.WriteStringValue(stringValue);
            }
            else if (value is long longValue)
            {
                writer.WriteNumberValue(longValue);
            }
            else if (value is double doubleValue)
            {
                writer.WriteNumberValue(doubleValue);
            }
            else if (value is bool boolValue)
            {
                writer.WriteBooleanValue(boolValue);
            }
            else if (value is DateTimeOffset dateTimeOffsetValue)
            {
                writer.WriteStringValue(dateTimeOffsetValue); // Use the correct Write method for DateTimeOffset
            }
            else if (value is DateTime dateTimeValue)
            {
                writer.WriteStringValue(dateTimeValue);
            }
            else if (value is Dictionary<string, object?> dictionaryValue)
            {
                WriteObject(writer, dictionaryValue, options);
            }
            else if (value is List<object?> listValue)
            {
                WriteArray(writer, listValue, options);
            }
            else
            {
                JsonSerializer.Serialize(writer, value, options);
            }
        }

        private void WriteObject(Utf8JsonWriter writer, Dictionary<string, object?> value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();

            foreach (var kvp in value)
            {
                writer.WritePropertyName(kvp.Key);
                Write(writer, kvp.Value, options);
            }

            writer.WriteEndObject();
        }

        private void WriteArray(Utf8JsonWriter writer, List<object?> value, JsonSerializerOptions options)
        {
            writer.WriteStartArray();

            foreach (var item in value)
            {
                Write(writer, item, options);
            }

            writer.WriteEndArray();
        }
    }
}


