import { MigrationInterface, QueryRunner } from 'typeorm';
import { IFacilityEquipment } from '@app/modules/facility/types';

export class changeStudyEuipments1694006566354 implements MigrationInterface {
    name = 'changeStudyEuipments1694006566354'

    public async up(queryRunner: QueryRunner): Promise<void> {
      const equipments = await queryRunner.query('SELECT * FROM equipments');
      const equipmentsMap = equipments.reduce((acc: Record<number, any>, item: any) => {
        acc[item.id] = item;

        return acc;
      }, {});

      let offset = 0;
      const limit = 100;
      let studies = await queryRunner.query('SELECT id, equipments FROM studies OFFSET $1 LIMIT $2', [offset, limit]);

      while(studies.length) {
        for (const study of studies) {
          const equipments = Object.keys(study.equipments)
            .map((equipmentId) => {
              const equipmentCount = study.equipments[equipmentId];
              const equipment = equipmentsMap[equipmentId];

              return {
                equipmentId,
                equipmentName: equipment.name,
                count: equipmentCount,
              };
            })
            .reduce((acc: Record<number, IFacilityEquipment>, item: any) => {
              acc[item.equipmentId] = item;

              return acc;
            }, {});


          await queryRunner.query('UPDATE studies SET equipments = $1 WHERE id = $2', [equipments, study.id]);
        }

        offset += limit;
        studies = await queryRunner.query('SELECT id, equipments FROM studies OFFSET $1 LIMIT $2', [offset, limit]);
      }

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      let offset = 0;
      const limit = 100;
      let studies = await queryRunner.query('SELECT id, equipments FROM studies OFFSET $1 LIMIT $2', [offset, limit]);

      while(studies.length) {
        for (const study of studies) {
          const equipments = Object.values(study.equipments)
            .reduce((acc: Record<number, number>, item: any) => {
              acc[item.equipmentId] = item.count;

              return acc;
            }, {});


          await queryRunner.query('UPDATE studies SET equipments = $1 WHERE id = $2', [equipments, study.id]);
        }

        offset += limit;
        studies = await queryRunner.query('SELECT id, equipments FROM studies OFFSET $1 LIMIT $2', [offset, limit]);
      }
    }

}
