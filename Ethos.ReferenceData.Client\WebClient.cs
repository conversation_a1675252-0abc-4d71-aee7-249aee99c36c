﻿using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Text.Json;

namespace Ethos.ReferenceData.Client
{
    /// <summary>
    /// 
    /// </summary>
    public interface IWebClient : IDisposable
    {
        event EventHandler<ResponseReceivedEventArgs>? ResponseReceived;
        event EventHandler<HttpErrorStatusEventArgs>? HttpStatusError;
        string? BearerToken { get; set; }
        string BaseUri { get; set; }
        JsonSerializerOptions JsonOptions { get; set; }
        Task<HttpResponseMessage> GetAsync(Uri uri);
        Task<HttpResponseMessage> PostContentAsync(Uri uri, HttpContent content);
        Task<HttpResponseMessage> PutContentAsync(Uri uri, HttpContent content);
        Task<HttpResponseMessage> PatchContentAsync(Uri uri, HttpContent content);
        Task<HttpResponseMessage> DeleteAsync(Uri uri);
        Task<HttpResponseMessage> PutJsonAsync<T>(Uri uri, T content);
        Task<HttpResponseMessage> PatchJsonAsync<T>(Uri uri, T content);
        Task<HttpResponseMessage> PostJsonAsync<T>(Uri uri, T content);
        Task<T?> GetJsonAsync<T>(Uri uri);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="message"></param>
    public class HttpErrorStatusEventArgs(HttpResponseMessage message) : EventArgs
    {
        public HttpResponseMessage Response { get; set; } = message;
        public HttpStatusCode Status { get; set; } = message.StatusCode;
        public string ReasonPhrase { get; set; } = message.ReasonPhrase ?? string.Empty;
        public string? ContentType { get; set; } = message.Content?.Headers?.ContentType?.MediaType;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="message"></param>
    public class ResponseReceivedEventArgs(HttpResponseMessage message) : EventArgs
    {
        public HttpResponseMessage Response { get; set; } = message;
        public HttpStatusCode Status {  get; set; } = message.StatusCode;
        public string? ContentType { get; set; } = message.Content?.Headers?.ContentType?.MediaType;
        public string? ResponseString { get; set; } = message.Content?.ReadAsStringAsync()?.Result;
    }

    /// <summary>
    /// 
    /// </summary>
    public class WebClient : IWebClient, IDisposable
    {
        readonly HttpClient httpClient;
        bool disposed = false;

        public event EventHandler<ResponseReceivedEventArgs>? ResponseReceived;
        public event EventHandler<HttpErrorStatusEventArgs>? HttpStatusError;

        JsonSerializerOptions jsonSerializerOptions = new()
        {
            AllowTrailingCommas = true,
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        string? baseUri, token;

        /// <summary>
        /// 
        /// </summary>
        public string BaseUri
        {
            get
            {
                return baseUri ?? httpClient?.BaseAddress?.ToString() ?? string.Empty;
            }

            set
            {
                baseUri = value;
                if (!string.IsNullOrEmpty(baseUri))
                    httpClient.BaseAddress = new Uri(baseUri);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public string? BearerToken
        {
            get
            {
                return token;
            }

            set
            {
                token = value;
                if (!string.IsNullOrEmpty(token))
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public JsonSerializerOptions JsonOptions
        {
            get
            {
                return jsonSerializerOptions;
            }

            set
            {
                jsonSerializerOptions = value;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpClient"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public WebClient(HttpClient httpClient)
        {
            this.httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            BaseUri = string.Empty;
            httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <returns></returns>
        /// <exception cref="ObjectDisposedException"></exception>
        public async Task<HttpResponseMessage> GetAsync(Uri uri)
        {
            if (disposed)
                throw new ObjectDisposedException(nameof(WebClient));

            if (!string.IsNullOrEmpty(BearerToken) && httpClient.DefaultRequestHeaders.Authorization is null)
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", BearerToken);

            var resp = await httpClient.GetAsync(uri).ConfigureAwait(false);
            OnResponseReceived(resp);
            return resp;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="message"></param>
        protected void OnResponseReceived(HttpResponseMessage message)
        {
            ResponseReceived?.Invoke(this, new ResponseReceivedEventArgs(message));

            if (!message.IsSuccessStatusCode && message.StatusCode != HttpStatusCode.NotModified)
                HttpStatusError?.Invoke(this, new HttpErrorStatusEventArgs(message));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <returns></returns>
        public async Task<string?> GetStringAsync(Uri uri)
        {
            var response = await GetAsync(uri).ConfigureAwait(false);
            if (response.Content is not null)
                return await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            return default;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <returns></returns>
        public async Task<Stream?> GetStreamAsync(Uri uri)
        {
            var response = await GetAsync(uri).ConfigureAwait(false);
            if (response.Content is not null)
                return await response.Content.ReadAsStreamAsync().ConfigureAwait(false);
            return default;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <returns></returns>
        public async Task<byte[]> GetByteArrayAsync(Uri uri)
        {
            var response = await GetAsync(uri).ConfigureAwait(false);
            if (response.Content is not null)
                return await response.Content.ReadAsByteArrayAsync().ConfigureAwait(false);
            return [];
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="uri"></param>
        /// <returns></returns>
        public async Task<T?> GetJsonAsync<T>(Uri uri)
        {
            var response = await GetAsync(uri).ConfigureAwait(false);
            if (response.Content is not null)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(content, JsonOptions);
            }
            return default;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="uri"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public async Task<HttpResponseMessage> PostJsonAsync<T>(Uri uri, T content)
        {
            var jsonContent = JsonContent.Create(content, typeof(T), new MediaTypeHeaderValue(MediaTypeNames.Application.Json), JsonOptions);
            return await PostContentAsync(uri, jsonContent).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="uri"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public async Task<HttpResponseMessage> PutJsonAsync<T>(Uri uri, T content)
        {
            var jsonContent = JsonContent.Create(content, typeof(T), new MediaTypeHeaderValue(MediaTypeNames.Application.Json), JsonOptions);
            return await PutContentAsync(uri, jsonContent).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="uri"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public async Task<HttpResponseMessage> PatchJsonAsync<T>(Uri uri, T content)
        {
            var jsonContent = JsonContent.Create(content, typeof(T), new MediaTypeHeaderValue(MediaTypeNames.Application.Json), JsonOptions);
            return await PatchContentAsync(uri, jsonContent).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        /// <exception cref="ObjectDisposedException"></exception>
        public async Task<HttpResponseMessage> PostContentAsync(Uri uri, HttpContent content)
        {
            if (disposed)
                throw new ObjectDisposedException(nameof(WebClient));

            if (!string.IsNullOrEmpty(BearerToken) && httpClient.DefaultRequestHeaders.Authorization is null)
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", BearerToken);

            var resp = await httpClient.PostAsync(uri, content).ConfigureAwait(false);
            OnResponseReceived(resp);
            return resp;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        /// <exception cref="ObjectDisposedException"></exception>
        public async Task<HttpResponseMessage> PutContentAsync(Uri uri, HttpContent content)
        {
            if (disposed)
                throw new ObjectDisposedException(nameof(WebClient));

            if (!string.IsNullOrEmpty(BearerToken) && httpClient.DefaultRequestHeaders.Authorization is null)
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", BearerToken);

            var resp = await httpClient.PutAsync(uri, content).ConfigureAwait(false);
            OnResponseReceived(resp);
            return resp;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        /// <exception cref="ObjectDisposedException"></exception>
        public async Task<HttpResponseMessage> PatchContentAsync(Uri uri, HttpContent content)
        {
            if (disposed)
                throw new ObjectDisposedException(nameof(WebClient));

            if (!string.IsNullOrEmpty(BearerToken) && httpClient.DefaultRequestHeaders.Authorization is null)
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", BearerToken);

            var resp = await httpClient.PatchAsync(uri, content).ConfigureAwait(false);
            OnResponseReceived(resp);
            return resp;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="uri"></param>
        /// <returns></returns>
        /// <exception cref="ObjectDisposedException"></exception>
        public async Task<HttpResponseMessage> DeleteAsync(Uri uri)
        {
            if (disposed)
                throw new ObjectDisposedException(nameof(WebClient));

            if (!string.IsNullOrEmpty(BearerToken) && httpClient.DefaultRequestHeaders.Authorization is null)
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", BearerToken);

            var resp = await httpClient.DeleteAsync(uri).ConfigureAwait(false);
            OnResponseReceived(resp);
            return resp;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="disposing"></param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    // Don't do this
                    // _httpClient?.Dispose();
                }
                disposed = true;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 
        /// </summary>
        ~WebClient()
        {
            Dispose(false);
        }
    }
}
