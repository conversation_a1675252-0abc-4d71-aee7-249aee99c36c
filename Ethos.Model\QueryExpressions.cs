using System.Linq.Expressions;
using System.Reflection;

namespace Ethos.Model;

public static class QueryExpressions
{
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // Helpers
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /// <summary>
    /// Creates a reusable predicate factory from a LINQ expression.
    /// This allows the logic of a predicate to be "detached" from its original parameter
    /// and reapplied in a new context with a different parameter.
    /// </summary>
    /// <typeparam name="TEntity">The entity type the predicate applies to.</typeparam>
    /// <param name="expression">The original LINQ expression, e.g., "p => p.Name == "Test"".</param>
    /// <returns>A function that takes a new ParameterExpression and returns the body of the
    /// original expression with its parameter replaced by the new one.</returns>
    public static Func<ParameterExpression, Expression> Of<TEntity>(Expression<Func<TEntity, bool>> expression)
    {
        // Extract the single parameter from the original expression.
        var originalParameter = expression.Parameters.Single();

        // Return a factory function. When this function is called with a new parameter,
        // it will use the visitor to replace the original parameter in the expression's body.
        return newParameter => new ParameterReplacerVisitor(originalParameter, newParameter).Visit(expression.Body);
    }

    /// <summary>
    /// An ExpressionVisitor that traverses an expression tree and replaces all occurrences
    /// of a specific ParameterExpression with another one.
    /// </summary>
    private sealed class ParameterReplacerVisitor : ExpressionVisitor
    {
        private readonly ParameterExpression _oldParameter;
        private readonly ParameterExpression _newParameter;

        public ParameterReplacerVisitor(ParameterExpression oldParameter, ParameterExpression newParameter)
        {
            _oldParameter = oldParameter;
            _newParameter = newParameter;
        }

        /// <summary>
        /// Overridden to replace the parameter node if it matches the one we're looking for.
        /// </summary>
        protected override Expression VisitParameter(ParameterExpression node)
        {
            // If the current node is the parameter we want to replace, return the new parameter.
            // Otherwise, continue visiting the tree as normal.
            return node == _oldParameter ? _newParameter : base.VisitParameter(node);
        }
    }
    
    private const string Names = nameof(Names);
    private const string Identifiers = nameof(Identifiers);
    private const string ContactDetail = nameof(ContactDetail);
    
    private static void RequireProperty<TSelf>(string propertyName)
    {
        if (typeof(TSelf).GetProperty(propertyName) == null)
        {
            throw new InvalidOperationException($"The type {typeof(TSelf).Name} does not have a property named '{propertyName}'.");
        }
    }
    
    private static readonly MethodInfo _Enumerable_Any = 
        typeof(Enumerable).GetMethods(BindingFlags.Static | BindingFlags.Public)
        .First(m => m.Name == "Any" && m.GetParameters().Length == 2);
    public static MethodInfo Enumerable_Any(Type elementType) => 
        _Enumerable_Any.MakeGenericMethod(elementType);

    public static readonly MethodInfo ToLowerMethod = typeof(string).GetMethod("ToLower", Type.EmptyTypes)!;
    public static readonly MethodInfo StringContainsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) })!;
    public static readonly MethodInfo StringStartsWithMethod = typeof(string).GetMethod("StartsWith", new[] { typeof(string) })!;
    public static readonly MethodInfo StringJoinMethod = typeof(string).GetMethod("Join", new[] { typeof(string), typeof(IEnumerable<string>) })!;

    // Method Infos for reflection
    private static readonly MethodInfo ToLowerMethodInfo =
        typeof(string).GetMethod("ToLower", System.Type.EmptyTypes) // Parameterless ToLower()
        ?? throw new InvalidOperationException("Could not find string.ToLower() method.");

    private static readonly MethodInfo StartsWithMethodInfo =
        typeof(string).GetMethod("StartsWith", new[] { typeof(string) }) // StartsWith(string)
        ?? throw new InvalidOperationException("Could not find string.StartsWith(string) method.");
    
    // Method Infos (cache them for performance)
    public static readonly MethodInfo StringEqualsMethodInfo = 
        typeof(string).GetMethod("Equals", BindingFlags.Public | BindingFlags.Instance, null, new[] { typeof(string) }, null)
        ?? throw new InvalidOperationException("Could not find the required method 'string.Equals(string)'."); // Overload: Equals(string)
    public static readonly MethodInfo StringStartsWithMethodInfo = 
        typeof(string).GetMethod("StartsWith", BindingFlags.Public | BindingFlags.Instance, null, new[] { typeof(string) }, null) 
        ?? throw new InvalidOperationException("Could not find the required method 'string.StartsWith(string)'."); // Overload: StartsWith(string)
    // public static readonly MethodInfo StringContainsMethodInfo; // Keep if needed for other logic
    
    public static Expression Get(this Expression self, string propName) =>
        Expression.Property(self, propName);
    
    public static Expression BuildIdentifierPredicate(string system, string value, ParameterExpression self)
    {
        var identifiers = Expression.Property(self, nameof(PhysicianDbo.Identifiers));

        var idParam     = Expression.Parameter(typeof(IdentifierDbo), "i");

        var systemMatch = Expression.Equal(
            Expression.Property(idParam, nameof(IdentifierDbo.System)),
            Expression.Constant(system));

        var valueMatch  = Expression.Equal(
            Expression.Property(idParam, nameof(IdentifierDbo.Value)),
            Expression.Constant(value));

        var predicate   = Expression.Lambda<Func<IdentifierDbo,bool>>(
            Expression.AndAlso(systemMatch, valueMatch),
            idParam);

        return Expression.Call(Enumerable_Any(typeof(IdentifierDbo)), identifiers, predicate);
    }

    /// <summary>
    /// Builds: owner.Names.Any(name => name.FirstName.ToLower().Equals(firstNameLower))
    /// </summary>
    public static Expression BuildFirstNamePredicate<TOwner>(string firstName, ParameterExpression ownerParam) where TOwner : class
    {
        // Check that TOwner has a property named "Names"
        if (ownerParam.Type.GetProperty(Names) == null)
        {
            throw new InvalidOperationException($"The type {typeof(TOwner).Name} does not have a property named 'Names'.");
        }
        
        // Pre-convert input to lower case
        string firstNameLower = firstName?.ToLowerInvariant() ?? string.Empty; // Use InvariantCulture for consistency
        var firstNameLowerConstant = Expression.Constant(firstNameLower);

        // Lambda: name => name.FirstName.ToLowerInvariant().Equals(firstNameLowerConstant)
        var nameParam = Expression.Parameter(typeof(PersonNameDbo), "name");
        var firstNameProperty = Expression.Property(nameParam, nameof(PersonNameDbo.FirstName));
        // Call name.FirstName.ToLowerInvariant() - No null check needed as FirstName is required
        var toLowerCall = Expression.Call(firstNameProperty, ToLowerMethodInfo);
        // Call ToLowerResult.Equals(firstNameLowerConstant)
        var equalsCall = Expression.Call(toLowerCall, StringEqualsMethodInfo, firstNameLowerConstant);
        var lambda = Expression.Lambda<Func<PersonNameDbo, bool>>(equalsCall, nameParam);

        // Outer: owner.Names.Any(lambda)
        var namesProperty = Expression.Property(ownerParam, Names);
        var anyNameMethod = Enumerable_Any(typeof(PersonNameDbo));
        return Expression.Call(anyNameMethod, namesProperty, lambda);
    }

    /// <summary>
    /// Builds: owner.Names.Any(name => name.LastName.ToLower().Equals(lastNameLower))
    /// </summary>
    public static Expression BuildLastNamePredicate<TOwner>(string lastName, ParameterExpression ownerParam) where TOwner : class
    {
        // Check that TOwner has a property named "Names"
        if (ownerParam.Type.GetProperty(Names) == null)
        {
            throw new InvalidOperationException($"The type {typeof(TOwner).Name} does not have a property named 'Names'.");
        }
        
        // Pre-convert input to lower case
        string lastNameLower = lastName?.ToLowerInvariant() ?? string.Empty; // Use InvariantCulture
        var lastNameLowerConstant = Expression.Constant(lastNameLower);

        // Lambda: name => name.LastName.ToLowerInvariant().Equals(lastNameLowerConstant)
        var nameParam = Expression.Parameter(typeof(PersonNameDbo), "name");
        var lastNameProperty = Expression.Property(nameParam, nameof(PersonNameDbo.LastName));
        // Call name.LastName.ToLowerInvariant() - No null check needed as LastName is required
        var toLowerCall = Expression.Call(lastNameProperty, ToLowerMethodInfo);
        // Call ToLowerResult.Equals(lastNameLowerConstant)
        var equalsCall = Expression.Call(toLowerCall, StringEqualsMethodInfo, lastNameLowerConstant);
        var lambda = Expression.Lambda<Func<PersonNameDbo, bool>>(equalsCall, nameParam);

        // Outer: owner.Names.Any(lambda)
        var namesProperty = Expression.Property(ownerParam, Names);
        var anyNameMethod = Enumerable_Any(typeof(PersonNameDbo));
        return Expression.Call(anyNameMethod, namesProperty, lambda);
    }

    /// <summary>
    /// Builds approximate name predicate for the new structure.
    /// Checks if ALL parts of approxName start EITHER FirstName OR MiddleName (if not null) OR LastName (case-insensitive).
    /// Builds: owner.Names.Any(name =>
    ///     (name.FirstName.ToLower().StartsWith(part1Lower) || (name.MiddleName != null && name.MiddleName.ToLower().StartsWith(part1Lower)) || name.LastName.ToLower().StartsWith(part1Lower))
    ///     &&
    ///     (name.FirstName.ToLower().StartsWith(part2Lower) || (name.MiddleName != null && name.MiddleName.ToLower().StartsWith(part2Lower)) || name.LastName.ToLower().StartsWith(part2Lower))
    ///     && ... for all parts
    /// )
    /// </summary>
    public static Expression BuildApproximateNamePredicate<TOwner>(string approxName, ParameterExpression ownerParam) where TOwner : class
    {
        // Check that TOwner has a property named "Names"
        if (ownerParam.Type.GetProperty(Names) == null)
        {
            throw new InvalidOperationException($"The type {typeof(TOwner).Name} does not have a property named 'Names'.");
        }
        
        var nameParam = Expression.Parameter(typeof(PersonNameDbo), "name"); // Lambda parameter for PersonName

        // Split and pre-convert parts to lower case using InvariantCulture
        var namePartsLower = (approxName ?? string.Empty)
            .Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
            .Select(p => p.ToLowerInvariant())
            .Where(p => !string.IsNullOrEmpty(p)) // Ensure no empty parts after split/trim
            .ToList();

        if (namePartsLower.Count == 0)
        {
            // If input is empty or only whitespace, return Any(name => false) to yield no results
            var namesPropertyForEmpty = Expression.Property(ownerParam, Names);
            var falseLambda = Expression.Lambda<Func<PersonNameDbo, bool>>(Expression.Constant(false), nameParam);
            var anyNameMethodForEmpty = Enumerable_Any(typeof(PersonNameDbo));
            return Expression.Call(anyNameMethodForEmpty, namesPropertyForEmpty, falseLambda);
        }

        // Property Accessors within the 'name' lambda parameter
        var firstNameProperty = Expression.Property(nameParam, nameof(PersonNameDbo.FirstName));
        var middleNameProperty = Expression.Property(nameParam, nameof(PersonNameDbo.MiddleName));
        var lastNameProperty = Expression.Property(nameParam, nameof(PersonNameDbo.LastName));

        Expression? combinedPredicateForAllParts = null; // Use AndAlso to combine requirements for each part

        foreach (var partLower in namePartsLower)
        {
            var partLowerConstant = Expression.Constant(partLower);

            // --- Create checks for the current part ---

            // FirstName Check: name.FirstName.ToLowerInvariant().StartsWith(partLowerConstant)
            var firstNameLower = Expression.Call(firstNameProperty, ToLowerMethodInfo);
            var firstNameStartsWith = Expression.Call(firstNameLower, StringStartsWithMethodInfo, partLowerConstant);

            // MiddleName Check: name.MiddleName != null && name.MiddleName.ToLowerInvariant().StartsWith(partLowerConstant)
            var middleNameIsNotNull = Expression.NotEqual(middleNameProperty, Expression.Constant(null, typeof(string)));
            var middleNameLower = Expression.Call(middleNameProperty, ToLowerMethodInfo);
            var middleNameStartsWith = Expression.Call(middleNameLower, StringStartsWithMethodInfo, partLowerConstant);
            var middleNameCheck = Expression.AndAlso(middleNameIsNotNull, middleNameStartsWith); // Combine null check and StartsWith

            // LastName Check: name.LastName.ToLowerInvariant().StartsWith(partLowerConstant)
            var lastNameLower = Expression.Call(lastNameProperty, ToLowerMethodInfo);
            var lastNameStartsWith = Expression.Call(lastNameLower, StringStartsWithMethodInfo, partLowerConstant);

            // Combine checks for the current part: Does this part match the start of FirstName OR MiddleName OR LastName?
            // (firstNameStartsWith || middleNameCheck || lastNameStartsWith)
            var partMatches = Expression.OrElse(Expression.OrElse(firstNameStartsWith, middleNameCheck), lastNameStartsWith);

            // --- Combine this part's requirement with previous parts using AND ---
            // (All parts must find a match within the same PersonName)
            combinedPredicateForAllParts = combinedPredicateForAllParts == null
                ? partMatches
                : Expression.AndAlso(combinedPredicateForAllParts, partMatches);
        }

        // Should not be null if namePartsLower was not empty, but check for safety
        if (combinedPredicateForAllParts == null) return Expression.Constant(false);

        // Create the lambda for the PersonName: name => combinedPredicateForAllParts
        var nameLambda = Expression.Lambda<Func<PersonNameDbo, bool>>(combinedPredicateForAllParts, nameParam);

        // Apply to the owner's Names collection: owner.Names.Any(nameLambda)
        var namesProperty = Expression.Property(ownerParam, Names);
        var anyNameMethod = Enumerable_Any(typeof(PersonNameDbo));
        return Expression.Call(anyNameMethod, namesProperty, nameLambda);
    }
    
    public static Expression BuildSimpleApproximateNamePredicate<TSelf>(
        MemberExpression nameProperty, string? approxName)
    {
        RequireProperty<TSelf>("Name");
        
        // 1. Break the search text into non-empty, lower-cased parts
        var parts = (approxName ?? string.Empty)
            .Split(' ', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
            .Select(p => p.ToLowerInvariant())   // use invariant culture once
            .ToArray();

        if (parts.Length == 0)
            return Expression.Constant(true);           // nothing to match ⇒ always true

        // 2. Build one lowered-name expression up-front:  nameProperty.ToLower()
        var toLowerMethod = typeof(string).GetMethod(nameof(string.ToLower), Type.EmptyTypes)!;
        var loweredName   = Expression.Call(nameProperty, toLowerMethod);

        // 3. Build the predicate: loweredName.Contains(part1) && loweredName.Contains(part2) && …
        var containsMethod = typeof(string).GetMethod(nameof(string.Contains), new[] { typeof(string) })!;
        Expression? predicate = null;

        foreach (var part in parts)
        {
            var containsPart = Expression.Call(loweredName, containsMethod, Expression.Constant(part));
            predicate = predicate is null ? containsPart
                : Expression.AndAlso(predicate, containsPart);
        }

        return predicate!;
    }
    
    public static Expression HasIdentifierPredicate<TSelf>(ParameterExpression self, string system, string value)
    {
        // p => p.Identifiers.Any(i => i.System == withIdentifier.System && i.Value == withIdentifier.Value)
        RequireProperty<TSelf>(Identifiers);

        var identifierParam = Expression.Parameter(typeof(IdentifierDbo), "i");
        var systemProperty = Expression.Property(identifierParam, nameof(IdentifierDbo.System));
        var valueProperty = Expression.Property(identifierParam, nameof(IdentifierDbo.Value));

        var systemEquals = Expression.Equal(systemProperty, Expression.Constant(system));
        var valueEquals = Expression.Equal(valueProperty, Expression.Constant(value));
        var and = Expression.AndAlso(systemEquals, valueEquals);
        var lambda = Expression.Lambda<Func<IdentifierDbo, bool>>(and, identifierParam);

        var identifiersProperty = Expression.Property(self, nameof(PatientDbo.Identifiers));
        var anyMethod = Enumerable_Any(typeof(IdentifierDbo));
        return Expression.Call(anyMethod, identifiersProperty, lambda);
    }
    
    public static Expression HasDemographicsFieldPredicate<TSelf>(ParameterExpression self, string name, long? value)
    {
        // Logic: p => p.Demographics != null && p.Demographics.SomeProperty == value
        RequireProperty<TSelf>("Demographics");

        var demographicsProperty = Expression.Property(self, nameof(PatientDbo.Demographics));
        var fieldProperty = Expression.Property(demographicsProperty, name);

        // Explicitly create the constant with the Nullable<long> type.
        // This prevents the value from being implicitly converted to a plain 'long' (wtf C#).
        var valueConstant = Expression.Constant(value, typeof(long?));

        // Combine with null check: p.Demographics != null && p.Demographics.SomeProperty == value
        var nullCheck = Expression.NotEqual(demographicsProperty, Expression.Constant(null));
        var equals = Expression.Equal(fieldProperty, valueConstant);
    
        return Expression.AndAlso(nullCheck, equals);
    }
    
    public static Expression BuildWithDateOfBirthPredicate<TSelf>(ParameterExpression self, DateOnly? lower, DateOnly? upper)
    {
        RequireProperty<TSelf>("Demographics");
        
        // This builds a predicate to filter patients by a date of birth range.
        // It translates to the following logic, which EF Core can convert to SQL:
        // p => p.Demographics != null &&
        //      p.Demographics.DateOfBirth.HasValue &&
        //      (withDob.Lower == null || p.Demographics.DateOfBirth.Value >= withDob.Lower.Value) &&
        //      (withDob.Upper == null || p.Demographics.DateOfBirth.Value <= withDob.Upper.Value)

        // If both bounds are null, an empty query is ambiguous. Returning no results is a safe default.
        if (!lower.HasValue && !upper.HasValue)
        {
            return Expression.Constant(false);
        }

        // Expression for accessing p.Demographics
        var demographicsProperty = Expression.Property(self, nameof(PatientDbo.Demographics));
        // Expression for accessing p.Demographics.DateOfBirth
        var dobProperty = Expression.Property(demographicsProperty, nameof(DemographicsDbo.DateOfBirth));

        // p.Demographics != null
        var demographicsNullCheck = Expression.NotEqual(
            demographicsProperty,
            Expression.Constant(null, typeof(DemographicsDbo))
        );
        
        // p.Demographics.DateOfBirth.HasValue
        var dobHasValueCheck = Expression.Property(dobProperty, nameof(Nullable<DateOnly>.HasValue));

        // Start building the predicate: p.Demographics != null && p.Demographics.DateOfBirth.HasValue
        Expression predicate = Expression.AndAlso(demographicsNullCheck, dobHasValueCheck);

        // Expression for accessing p.Demographics.DateOfBirth.Value
        var dobValueProperty = Expression.Property(dobProperty, nameof(Nullable<DateOnly>.Value));

        if (lower.HasValue)
        {
            // ... && p.Demographics.DateOfBirth.Value >= withDob.Lower.Value
            var lowerBound = Expression.Constant(lower.Value, typeof(DateOnly));
            var lowerCheck = Expression.GreaterThanOrEqual(dobValueProperty, lowerBound);
            predicate = Expression.AndAlso(predicate, lowerCheck);
        }

        if (upper.HasValue)
        {
            // ... && p.Demographics.DateOfBirth.Value <= withDob.Upper.Value
            var upperBound = Expression.Constant(upper.Value, typeof(DateOnly));
            var upperCheck = Expression.LessThanOrEqual(dobValueProperty, upperBound);
            predicate = Expression.AndAlso(predicate, upperCheck);
        }

        // The short-circuiting behavior of 'AndAlso' ensures we don't access properties
        // on a null Demographics object, preventing a NullReferenceException at runtime.
        return predicate;
    }

    public static Expression HasEmailPredicate<TSelf>(ParameterExpression self, string email)
    {
        // Logic: p => p.ContactDetail != null && p.ContactDetail.Emails.Any(e => e.Email.ToLower() == withEmail.Email.ToLower())
        RequireProperty<TSelf>(ContactDetail);
        
        var emailConstantLower = Expression.Constant(email.ToLower());

        // Inner lambda: e => e.Email.ToLower() == withEmail.Email.ToLower()
        var emailParam = Expression.Parameter(typeof(PersonalEmailDbo), "e");
        var emailProperty = Expression.Property(emailParam, nameof(PersonalEmailDbo.Email));
        var toLowerCall = Expression.Call(emailProperty, ToLowerMethod);
        var equals = Expression.Equal(toLowerCall, emailConstantLower);
        var innerLambda = Expression.Lambda<Func<PersonalEmailDbo, bool>>(equals, emailParam);

        // Get the navigation property: p.ContactDetail
        var contactDetailProperty = Expression.Property(self, nameof(PatientDbo.ContactDetail));
    
        // Get the collection: p.ContactDetail.Emails
        // This assumes your ContactDetailEntity has a property named "Emails" of type ICollection<PatientEmail>
        var emailsProperty = Expression.Property(contactDetailProperty, "Emails"); 

        // Build the Any() call
        var anyMethod = Enumerable_Any(typeof(PersonalEmailDbo));
        var anyCall = Expression.Call(anyMethod, emailsProperty, innerLambda);

        var nullCheck = Expression.NotEqual(contactDetailProperty, Expression.Constant(null));
        // Combine with AndAlso: p.ContactDetail != null && p.ContactDetail.Emails.Any(...)
        return Expression.AndAlso(nullCheck, anyCall);
    }

    public static Expression HasPhoneNumberPredicate<TSelf>(ParameterExpression self, string value)
    {
        // Logic: p => p.ContactDetail != null && p.ContactDetail.PhoneNumbers.Any(ph => ph.PhoneNumber == withPhone.PhoneNumber)
        RequireProperty<TSelf>(ContactDetail);

        // Inner lambda: ph => ph.PhoneNumber == withPhone.PhoneNumber
        var phoneParam = Expression.Parameter(typeof(PersonalPhoneNumberDbo), "ph");
        var numberProperty = Expression.Property(phoneParam, nameof(PersonalPhoneNumberDbo.PhoneNumber));
        var equals = Expression.Equal(numberProperty, Expression.Constant(value));
        var innerLambda = Expression.Lambda<Func<PersonalPhoneNumberDbo, bool>>(equals, phoneParam);

        // Get the navigation property: p.ContactDetail
        var contactDetailProperty = Expression.Property(self, nameof(PatientDbo.ContactDetail));
    
        // Get the collection: p.ContactDetail.PhoneNumbers
        // This assumes your ContactDetailEntity has a property named "PhoneNumbers" of type ICollection<PatientPhoneNumber>
        var phonesProperty = Expression.Property(contactDetailProperty, "PhoneNumbers");

        // Build the Any() call
        var anyMethod = Enumerable_Any(typeof(PersonalPhoneNumberDbo));
        var anyCall = Expression.Call(anyMethod, phonesProperty, innerLambda);

        // Add a null check for robustness: p.ContactDetail != null
        var nullCheck = Expression.NotEqual(contactDetailProperty, Expression.Constant(null));
    
        // Combine with AndAlso: p.ContactDetail != null && p.ContactDetail.PhoneNumbers.Any(...)
        return Expression.AndAlso(nullCheck, anyCall);
    }
}