import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFacilityFields1689678651128 implements MigrationInterface {
    name = 'addFacilityFields1689678651128'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "facilities" ADD "address_line1" character varying');
      await queryRunner.query('ALTER TABLE "facilities" ADD "address_line2" character varying');
      await queryRunner.query('ALTER TABLE "facilities" ADD "zip" character varying');
      await queryRunner.query('ALTER TABLE "facilities" ADD "phone" character varying');
      await queryRunner.query('ALTER TABLE "facilities" ADD "fax" character varying');
      await queryRunner.query('ALTER TABLE "facilities" ADD "city_id" integer NOT NULL');
      await queryRunner.query('ALTER TABLE "facilities" ADD CONSTRAINT "FK_56a2b74b969706df53b1c809195" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "facilities" DROP CONSTRAINT "FK_56a2b74b969706df53b1c809195"');
      await queryRunner.query('ALTER TABLE "facilities" DROP CONSTRAINT "FK_231953b0c606b9e3af91cf5be52"');
      await queryRunner.query('ALTER TABLE "facilities" DROP COLUMN "city_id"');
      await queryRunner.query('ALTER TABLE "facilities" DROP COLUMN "fax"');
      await queryRunner.query('ALTER TABLE "facilities" DROP COLUMN "phone"');
      await queryRunner.query('ALTER TABLE "facilities" DROP COLUMN "zip"');
      await queryRunner.query('ALTER TABLE "facilities" DROP COLUMN "address_line2"');
      await queryRunner.query('ALTER TABLE "facilities" DROP COLUMN "address_line1"');
    }

}
