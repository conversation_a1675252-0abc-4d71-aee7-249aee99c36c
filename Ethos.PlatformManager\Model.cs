﻿using Ethos.Auth;

namespace Ethos.PlatformManager
{
    public enum TenantStatus
    {
        Active,
        Inactive,
    }

    /// <summary>
    /// 
    /// </summary>
    public static class BuiltinRoleIds
    {
        public static readonly Guid InterpretingPhysician = new("0b469dbc-95ee-4d14-929e-cec638b7b289");
        public static readonly Guid ReferringPhysician = new("6f8079e0-980e-4fd7-941f-bca77bb6c858");
        public static readonly Guid AlliedHealth = new("d93b0144-c852-467d-93df-c0ebfbda0ed8");
        public static readonly Guid PracticeAdministrator = new("e9296b7f-**************-2d45ea5d8042");
        public static readonly Guid SystemAdministrator = new("fe38a51d-c325-4af8-b02e-7599c2413f5f");
        public static readonly Guid ScoringTechnician = new("2f20eccb-2a24-44c4-8cac-dac26a70ec95");
        public static readonly Guid BillingCoordinator = new("5598a139-6949-4a78-a3de-25bbf93f8d7c");
        public static readonly Guid BillingAdministrator = new("da81d11e-daca-4bf5-8e0d-************");
        public static readonly Guid RegistrationIntakeCoordinator = new("ca6bf22f-41f0-4f5b-8fe2-e16d04cb8fb6");
        public static readonly Guid SchedulingCoordinator = new("8030cda4-2e66-43e6-848b-727934688df4");
        public static readonly Guid SecurityAdministrator = new("3fefe844-5a79-437e-b4c8-6a04ddcc3697");
    }

    /// <summary>
    /// 
    /// </summary>
    public class BuiltinRole
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; } = null!;

        /// <summary>
        /// 
        /// </summary>
        public static readonly List<BuiltinRole> All = [
                 new() { Name = "Interpreting Physician", Id = BuiltinRoleIds.InterpretingPhysician },
                 new() { Name = "Referring Physician", Id = BuiltinRoleIds.ReferringPhysician },
                 new() { Name = "Scoring Technician", Id = BuiltinRoleIds.ScoringTechnician },
                 new() { Name = "Allied Health/Clinical Care Team", Id = BuiltinRoleIds.AlliedHealth },
                 new() { Name = "Practice/Clinic Administrator", Id = BuiltinRoleIds.PracticeAdministrator },
                 new() { Name = "IT/System Administrator", Id = BuiltinRoleIds.SystemAdministrator },
                 new() { Name = "Registration/Intake Coordinator", Id = BuiltinRoleIds.RegistrationIntakeCoordinator },
                 new() { Name = "Scheduling Coordinator", Id = BuiltinRoleIds.SchedulingCoordinator },
                 new() { Name = "Billing Coordinator", Id = BuiltinRoleIds.BillingCoordinator },
                 new() { Name = "Billing Administrator", Id = BuiltinRoleIds.BillingAdministrator },
                 new() { Name = "Security Administrator", Id = BuiltinRoleIds.SecurityAdministrator },
            ];

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool IsBuiltin(Guid id)
        {
            return All.Any(r => id == r.Id);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool IsBuiltin(Guid? id)
        {
            return IsBuiltin(id ?? Guid.Empty);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static BuiltinRole? GetBuiltin(Guid? id)
        {
            if ((id ?? Guid.Empty) == Guid.Empty)
                return default;
            return All.FirstOrDefault(r => r.Id == id);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static bool IsBuiltinName(string? name)
        {
            return All.Any(r => r.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static Guid? GetBuiltinId(string name)
        {
            var b = All.FirstOrDefault(r => r.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            return b?.Id;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        public static bool IsBuiltin(EthosRoleDto role)
        {
            return IsBuiltin(role.Id ?? Guid.Empty);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        public static bool IsBuiltin(EthosRole role)
        {
            return IsBuiltin(role.Id);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRole
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string[] Filters { get; set; } = [];
        public ICollection<EthosRoleScope> Scopes { get; set; } = [];
        public Guid TenantId { get; set; }
        public ICollection<EthosRoleAssignment> RoleAssignments { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosUser
    {
        public Guid Id { get; set; }
        public string DisplayName { get; set; } = null!;
        public string GivenName { get; set; } = null!;
        public string Surname { get; set; } = null!;
        public string? MiddleName { get; set; }
        public string? Nickname { get; set; }
        public string? NamePrefix { get; set; }
        public string? NameSuffix { get; set; }
        public Guid TenantId { get; set; }
        public string? Title { get; set; }
        public string? Department { get; set; }
        public string? UserType { get; set; }
        public bool Active { get; set; }
        public bool Deleted { get; set; }
        public ICollection<EthosRoleAssignment> RoleAssignments { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRoleAssignment
    {
        public Guid RoleId { get; set; }
        public EthosRole Role { get; set; } = null!;
        public Guid UserId { get; set; }
        public Guid TenantId { get; set; }
        public EthosUser User { get; set; } = null!;
        public EthosTenant Tenant { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosDbScope
    {
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public bool Privileged { get; set; }
        public bool Assignable { get; set; }
        public long RowId { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosRoleScope
    {
        public Guid RoleId { get; set; }
        public Guid TenantId { get; set; }
        public EthosRole Role { get; set; } = null!;

        public string Scope
        {
            get
            {
                return scope ?? string.Empty;
            }

            set
            {
                if (!EthosScope.TryParse(value, null, out var _scope))
                    throw new ArgumentException($"Invalid scope: {value}", nameof(value));

                if (!_scope.IsFullyQualified())
                    throw new ArgumentException($"Invalid scope: {value}", nameof(value));

                scope = _scope.ToString();
            }
        }
        public string DisplayName { get; set; } = null!;
        string? scope;
    }

    public class License
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public Guid TenantId { get; set; }
        public EthosTenant Tenant { get; set; } = null!;
        public LicenseState State { get; set; } = LicenseState.Active;
        public DateTimeOffset StartDate { get; set; }
        public DateTimeOffset EndDate { get; set; }
        public DateTimeOffset Created { get; set; }
        public DateTimeOffset? Updated { get; set; }
        public ICollection<LicenseProduct> LicenseProducts { get; set; } = [];
    }

    public class Product
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public ICollection<LicenseProduct> LicenseProducts { get; set; } = [];
        public ICollection<ProductFeature> Features { get; set; } = [];
    }

    public class Feature
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string ScopePrefix { get; set; } = null!;
    }

    public class ProductFeature
    {
        public Guid FeatureId { get; set; }
        public Guid ProductId { get; set; }
        public string Name { get; set; } = null!;
        public bool Enabled { get; set; }
        public Product Product { get; set; } = null!;
        public Feature Feature { get; set; } = null!;
    }

    public class LicenseProductFeature
    {
        public string Name { get; set; } = null!;
        public Guid FeatureId { get; set; }
        public Feature Feature { get; set; } = null!;
        public LicenseProduct LicenseProduct { get; set; } = null!;
        public Guid LicenseProductId { get; set; }
        public bool Enabled { get; set; }
        public DateTimeOffset StartDate { get; set; }
        public DateTimeOffset EndDate { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class LicenseProduct
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public Guid LicenseId { get; set; }
        public Guid ProductId { get; set; }
        public License License { get; set; } = null!;
        public Product Product { get; set; } = null!;
        public ICollection<LicenseProductFeature> Features { get; set; } = [];
    }

    public enum TenantContactType
    {
        Primary = 0,
        Billing = 1,
        Technical = 2,
        Administrative = 3,
        Clinical = 4,
    }

    public class TenantContactPerson
    {
        public Guid Id { get; set; }
        public Guid TenantId { get; set; }
        public string LastName { get; set; } = null!;
        public string FirstName { get; set; } = null!;
        public string? MiddleName { get; set; }
        public string? Title { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public TenantContactType ContactType { get; set; }
        public string? OrganizationName { get; set; }
        public EthosTenant Tenant { get; set; } = null!;
    }

    public class EthosTenant
    {
        public Guid Id { get; set; }
        public TenantStatus Status { get; set; }
        public ICollection<License> Licenses { get; set; } = [];
        public string OrganizationName { get; set; } = null!;
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? Address3 { get; set; }
        public string? PostalCode { get; set; }
        public string? City { get; set; }
        public string? Region { get; set; }
        public string? Country { get; set; }
        public string? OrganizationPhone { get; set; }
        public string? OrganizationEmail { get; set; }
        public ICollection<TenantContactPerson> Contacts { get; set; } = [];
    }
}
