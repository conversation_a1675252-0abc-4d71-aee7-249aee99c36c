apiVersion: apps/v1
kind: Deployment
metadata:
  name: ethos-platform-manager
  namespace: ethos-ns-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ethos-platform-manager
  template:
    metadata:
      labels:
        app: ethos-platform-manager
    spec:
      imagePullSecrets:
      - name: acr-secret
      containers:
      - name: ethos-platform-manager
        image: ethoscrdev.azurecr.io/ethos-platform-manager:2025.06.1
        imagePullPolicy: Always
        ports:
        - containerPort: 8080 
        env:
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              key: connection-string
              name: db-connection-secret
              optional: false
        - name: ASPNETCORE_URLS
          value: "http://*:8080"
        - name: Auth__Authorization__DebugScopes_JSON
          value: "[ \"*.*.Admin\" ]"
        - name: Auth__Basic__azure
          value: "10000.OvkwqHKf8gY2xjmCK2xMlg==.k4dHorktFIbIW1pf+MAI1/I6FfgCMFuZIyzuHQthGeg="
        - name: Events__BaseUri
          value: "https://goodspace.org"
        
      restartPolicy: Always 