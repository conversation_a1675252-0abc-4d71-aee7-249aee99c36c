from dataclasses import dataclass
from typing import List, Optional, Dict, Any, <PERSON><PERSON><PERSON><PERSON>, Tuple
from collections import OrderedDict
from uuid import UUID

NonEmptyString: TypeAlias = str

Sex: TypeAlias = NonEmptyString

Gender: TypeAlias = NonEmptyString

MaritalStatus: TypeAlias = NonEmptyString

Race: TypeAlias = NonEmptyString

Ethnicity: TypeAlias = NonEmptyString

SSN: TypeAlias = str

Email: TypeAlias = str

@dataclass
class Address:
    """Address"""
    line: List[str]
    city: NonEmptyString
    state: NonEmptyString
    postalCode: NonEmptyString
    country: NonEmptyString

@dataclass
class AddressWithUse:
    """Address with use"""
    address: Address
    use: NonEmptyString

@dataclass
class PhoneNumberWithUse:
    """Phone number with purpose"""
    type: NonEmptyString
    phoneNumber: NonEmptyString
    use: NonEmptyString
    allowsSMS: Optional[bool]
    allowsVoice: Optional[bool]
    allowsCommunication: Optional[bool]
    extension: Optional[NonEmptyString]

@dataclass
class EmailWithUse:
    """Email with purpose"""
    email: Email
    use: NonEmptyString

@dataclass
class ContactInformation:
    """Contact information"""
    phoneNumbers: List[PhoneNumberWithUse]
    emails: List[EmailWithUse]

@dataclass
class PatientId:
    """Patient ID"""
    system: NonEmptyString
    id: NonEmptyString

@dataclass
class PersonName:
    """Person name"""
    firstNames: List[NonEmptyString]
    lastNames: List[NonEmptyString]
    middleInitials: List[NonEmptyString]
    middleNames: List[NonEmptyString]
    prefixes: List[NonEmptyString]
    suffixes: List[NonEmptyString]

BirthDate: TypeAlias = str

@dataclass
class Demographics:
    """Demographics"""
    dateOfBirth: BirthDate
    gender: Gender
    birthSex: Gender
    maritalStatus: MaritalStatus
    race: Race
    ethnicity: Ethnicity

@dataclass
class Guardian:
    """Guardian information"""
    name: PersonName
    relationship: str
    contactInformation: ContactInformation

@dataclass
class PhysicalMeasurements:
    heightInches: float
    weightPounds: float
    neckSize: float
    bmi: float

@dataclass
class InsuranceHolder:
    """Insurance holder information"""
    name: str  # Changed from PersonName to str to match NamePart
    dateOfBirth: BirthDate
    relationship: int  # Changed from str to int to match Integer

@dataclass
class Insurance:
    payerName: str
    payerId: Optional[str]
    payerAddress: Address
    policyId: NonEmptyString
    groupNumber: NonEmptyString
    memberId: NonEmptyString
    insuranceHolder: Optional[InsuranceHolder]

