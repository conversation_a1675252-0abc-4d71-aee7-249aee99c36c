{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import Optional, List\n", "from dataclasses import dataclass\n", "\n", "import asyncio\n", "import logging\n", "import json\n", "import requests\n", "import aiohttp\n", "from uuid import uuid4, UUID\n", "from dataclasses import asdict\n", "\n", "from FlowTypes import *\n", "from AddNewPatient import *"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["DEBUG:AddNewPatientClientLogger:POST AddNewPatient.Start: {'instanceId': None, 'inputData': {}}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["DEBUG:AddNewPatientClientLogger:POST AddNewPatient.AddBasicInformation: {'instanceId': '019564dd-5b7a-7af8-acae-7482953113c2', 'inputData': {'name': {'firstNames': ['John'], 'lastNames': ['Doe'], 'middleInitials': ['H'], 'middleNames': ['Henry'], 'prefixes': [], 'suffixes': []}, 'ssn': '***********', 'demographics': {'dateOfBirth': '1985-01-01', 'gender': 'male', 'birthSex': 'male', 'maritalStatus': 'single', 'race': 'White', 'ethnicity': 'Not Hispanic or Latino'}, 'guardian': None}}\n", "ERROR:AddNewPatientClientLogger:HTTP 400 error: {\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"errors\":{\"InputData.PatientBasicInformation\":[\"The PatientBasicInformation field is required.\"]},\"traceId\":\"00-2223eba6c2acb8821799a6e55e5d4da2-6d90a4d20bb2c7c9-00\"}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Logged in successfully. Token:\n", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************.YnwTg8zSJqJ-CwUD0sAbFkSgmyW3We_p2nzIxdqyq5g\n", "The token type is: LocalJWT\n", "Workflow started. Current state: Start\n"]}, {"ename": "Exception", "evalue": "HTTP 400 error: {\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"errors\":{\"InputData.PatientBasicInformation\":[\"The PatientBasicInformation field is required.\"]},\"traceId\":\"00-2223eba6c2acb8821799a6e55e5d4da2-6d90a4d20bb2c7c9-00\"}", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mException\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Input \u001b[1;32mIn [2]\u001b[0m, in \u001b[0;36m<cell line: 156>\u001b[1;34m()\u001b[0m\n\u001b[0;32m    152\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>t\u001b[39;00m client\u001b[38;5;241m.\u001b[39mreviewandcommit(instance_id, review_request)\n\u001b[0;32m    153\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWorkflow completed successfully.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m--> 156\u001b[0m \u001b[38;5;28;01mawait\u001b[39;00m main_workflow()\n", "Input \u001b[1;32mIn [2]\u001b[0m, in \u001b[0;36mmain_workflow\u001b[1;34m()\u001b[0m\n\u001b[0;32m     54\u001b[0m \u001b[38;5;66;03m# 5) Add basic information\u001b[39;00m\n\u001b[0;32m     55\u001b[0m basic_info_request \u001b[38;5;241m=\u001b[39m AddBasicInformationRequest(\n\u001b[0;32m     56\u001b[0m     name\u001b[38;5;241m=\u001b[39mPersonName(\n\u001b[0;32m     57\u001b[0m         firstNames\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mJohn\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     73\u001b[0m     guardian\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m     74\u001b[0m )\n\u001b[1;32m---> 75\u001b[0m _, state \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m client\u001b[38;5;241m.\u001b[39maddbasicinformation(instance_id, basic_info_request)\n\u001b[0;32m     76\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAdded basic information. Current state:\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mtype\u001b[39m(state)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m)\n\u001b[0;32m     78\u001b[0m \u001b[38;5;66;03m# contactInformation=ContactInformation(\u001b[39;00m\n\u001b[0;32m     79\u001b[0m \u001b[38;5;66;03m#             addresses=[\u001b[39;00m\n\u001b[0;32m     80\u001b[0m \u001b[38;5;66;03m#                 AddressWithUse(\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    113\u001b[0m \n\u001b[0;32m    114\u001b[0m \u001b[38;5;66;03m# 6) Add physical measurements\u001b[39;00m\n", "File \u001b[1;32md:\\ws\\persante\\repo\\Ethos.Workflows\\AddNewPatient.py:213\u001b[0m, in \u001b[0;36mAddNewPatientClient.addbasicinformation\u001b[1;34m(self, instance_id, request)\u001b[0m\n\u001b[0;32m    211\u001b[0m     error_body \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m resp\u001b[38;5;241m.\u001b[39mtext()\n\u001b[0;32m    212\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39merror(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mHTTP \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresp\u001b[38;5;241m.\u001b[39mstatus\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m error: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00merror_body\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m--> 213\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mHTTP \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresp\u001b[38;5;241m.\u001b[39mstatus\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m error: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00merror_body\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    214\u001b[0m \u001b[38;5;66;03m# non-final => returns new AddNewPatientState\u001b[39;00m\n\u001b[0;32m    215\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m resp\u001b[38;5;241m.\u001b[39mjson()\n", "\u001b[1;31mException\u001b[0m: HTTP 400 error: {\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"errors\":{\"InputData.PatientBasicInformation\":[\"The PatientBasicInformation field is required.\"]},\"traceId\":\"00-2223eba6c2acb8821799a6e55e5d4da2-6d90a4d20bb2c7c9-00\"}"]}], "source": ["def login():\n", "    \"\"\"\n", "    Logs in against an example endpoint, returns a <PERSON><PERSON> token.\n", "    Adjust the URL, credentials, etc. to match your environment.\n", "    \"\"\"\n", "    url = \"http://localhost:4000/api/login\"\n", "    credentials = {\"username\": \"test\", \"password\": \"password\"}\n", "    response = requests.post(url, json=credentials)\n", "    if response.status_code == 200:\n", "        token = response.json().get(\"token\")\n", "        print(\"Logged in successfully. Token:\")\n", "        print(token)\n", "        return token\n", "    else:\n", "        print(\"Login failed:\", response.status_code, response.text)\n", "        exit(1)\n", "\n", "def check_token(token):\n", "    \"\"\"\n", "    (Optional) Demonstrates using the token to call a protected endpoint.\n", "    \"\"\"\n", "    url = \"http://localhost:4000/api/login/check\"\n", "    headers = {\"Authorization\": f\"Bearer {token}\"}\n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code == 200:\n", "        token_type = response.json().get(\"tokenType\")\n", "        print(\"The token type is:\", token_type)\n", "    else:\n", "        print(\"Token check failed:\", response.status_code, response.text)\n", "\n", "async def main_workflow():\n", "    # 1) Login with the 'requests' library to get token\n", "    token = login()\n", "    check_token(token)\n", "\n", "    # 2) Create an aiohttp.ClientSession with the token\n", "    headers = {\"Authorization\": f\"Bearer {token}\"}\n", "    async with aiohttp.ClientSession(headers=headers) as session:\n", "        # (Optional) Create a logger if desired\n", "        logger = logging.getLogger(\"AddNewPatientClientLogger\")\n", "        logger.setLevel(logging.DEBUG)\n", "        logging.basicConfig(level=logging.DEBUG)\n", "\n", "        # 3) Create an instance of AddNewPatientClient\n", "        base_url = \"http://localhost:4000\"  # Adjust as needed\n", "        client = AddNewPatientClient(base_url, session, logger=logger)\n", "\n", "        # 4) Start the workflow\n", "        start_request = StartRequest()\n", "        instance_id = None  # The server can generate a new ID, or you can supply one\n", "        instance_id, state = await client.start(instance_id, start_request)\n", "        print(\"Workflow started. Current state:\", type(state).__name__)\n", "\n", "        # 5) Add basic information\n", "        basic_info_request = AddBasicInformationRequest(\n", "            name=<PERSON><PERSON><PERSON>(\n", "                firstNames=[\"<PERSON>\"],\n", "                middleInitials=[\"H\"],\n", "                middleNames=[\"<PERSON>\"],\n", "                lastNames=[\"Doe\"],\n", "                prefixes=[],\n", "                suffixes=[]\n", "            ),\n", "            ssn=\"***********\",\n", "            demographics=Demographics(\n", "                dateOfBirth=\"1985-01-01\",\n", "                gender=\"male\",\n", "                birthSex=\"male\",\n", "                maritalStatus=\"single\",\n", "                race=\"White\",\n", "                ethnicity=\"Not Hispanic or Latino\",\n", "            ),\n", "            guardian=None\n", "        )\n", "        _, state = await client.addbasicinformation(instance_id, basic_info_request)\n", "        print(\"Added basic information. Current state:\", type(state).__name__)\n", "\n", "        # contactInformation=ContactInformation(\n", "        #             addresses=[\n", "        #                 AddressWithUse(\n", "        #                     address=Address(\n", "        #                         line=[\"123 Main St\"],\n", "        #                         city=\"Testville\",\n", "        #                         state=\"TX\",\n", "        #                         postalCode=\"12345\",\n", "        #                         country=\"USA\"\n", "        #                     ),\n", "        #                     use=\"home\"\n", "        #                 )\n", "        #             ],\n", "        #             phoneNumbers=[\n", "        #                 PhoneNumberWithUse(\n", "        #                     type=\"mobile\",\n", "        #                     phoneNumber=\"************\",\n", "        #                     use=\"personal\",\n", "        #                     allowsSMS=True,\n", "        #                     allowsVoice=True,\n", "        #                     allowsCommunication=True,\n", "        #                     extension=None\n", "        #                 )\n", "        #             ],\n", "        #             emails=[\n", "        #                 EmailWithUse(\n", "        #                     email=\"<EMAIL>\",\n", "        #                     use=\"personal\"\n", "        #                 )\n", "        #             ],\n", "        #         ),\n", "        #         identifiers=[\n", "        #             PatientId(system=\"ExampleSystem\", id=\"123456\")\n", "        #         ]\n", "        #     ),\n", "\n", "        # 6) Add physical measurements\n", "        physical_measurements_request = AddPhysicalMeasurementsRequest(\n", "            physicalMeasurements=PhysicalMeasurements(\n", "                heightInches=70.0,\n", "                weightPounds=180.0,\n", "                neckSize=15.0,\n", "                bmi=25.8\n", "            )\n", "        )\n", "        _, state = await client.addphysicalmeasurements(instance_id, physical_measurements_request)\n", "        print(\"Added physical measurements. Current state:\", type(state).__name__)\n", "\n", "        # 7) Add insurance information\n", "        # Example insurance data\n", "        insurance_request = AddInsuranceInformationRequest(\n", "            insurances=[\n", "                Insurance(\n", "                    payerName=\"Example Insurance Co\",\n", "                    payerId=\"EX123\",\n", "                    payerAddress=Address(\n", "                        line=[\"PO Box 9876\"],\n", "                        city=\"Insurance City\",\n", "                        state=\"TX\",\n", "                        postalCode=\"78910\",\n", "                        country=\"USA\"\n", "                    ),\n", "                    policyId=\"POLICY123\",\n", "                    groupNumber=\"GROUP999\",\n", "                    memberId=\"MEM999\",\n", "                    insuranceHolder=None\n", "                )\n", "            ]\n", "        )\n", "        _, state = await client.addinsuranceinformation(instance_id, insurance_request)\n", "        print(\"Added insurance information. Current state:\", type(state).__name__)\n", "\n", "        # 8) Review and commit final step\n", "        review_request = ReviewAndCommitRequest()\n", "        await client.reviewandcommit(instance_id, review_request)\n", "        print(\"Workflow completed successfully.\")\n", "\n", "\n", "await main_workflow()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 2}