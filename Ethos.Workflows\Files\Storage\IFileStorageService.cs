using System.Text.Json.Serialization;
using Ethos.Model;

namespace Ethos.Workflows.Files;

public class StorageOptions
{
    public const string SectionName = "Storage";
    public StorageProvider Provider { get; set; } = StorageProvider.Local;
    public string LocalBasePath { get; set; } = string.Empty; // e.g., "/app/storage" or "C:/storage"
    public string AzureBlobConnectionString { get; set; } = string.Empty;
    public string AzureBlobContainerName { get; set; } = string.Empty;
}

[JsonConverter(typeof(StringEnumConverter<StorageProvider>))]
public enum StorageProvider
{
    Local,
    AzureBlob
}

/// <summary>
/// Interface for abstracting file storage operations.
/// </summary>
public interface IFileStorageService
{
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Saves a file stream to the storage provider.
    /// </summary>
    /// <param name="stream">The file content stream.</param>
    /// <param name="storagePath">The relative path where the file should be stored.</param>
    /// <param name="contentType">The MIME type of the file.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Task representing the async operation.</returns>
    /// <exception cref="IOException">Thrown if there's an error during storage.</exception>
    Task SaveFileAsync(Stream stream, string storagePath, string contentType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves a file stream from the storage provider.
    /// </summary>
    /// <param name="storagePath">The relative path of the file to retrieve.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A readable stream of the file content, or null if not found.</returns>
    Task<Stream?> GetFileStreamAsync(string storagePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a file from the storage provider.
    /// </summary>
    /// <param name="storagePath">The relative path of the file to delete.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Task representing the async operation.</returns>
    Task DeleteFileAsync(string storagePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a publicly accessible URL for a file, if applicable (e.g., for Blob storage with SAS tokens or public access).
    /// May return null if direct URLs are not supported or configured.
    /// </summary>
    /// <param name="storagePath">The relative path of the file.</param>
    /// <param name="expiryTime">Optional duration for which the URL should be valid.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A URL string or null.</returns>
    Task<string?> GetFileUrlAsync(string storagePath, TimeSpan? expiryTime = null, CancellationToken cancellationToken = default);
}