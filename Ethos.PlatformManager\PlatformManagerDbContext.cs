﻿using Microsoft.EntityFrameworkCore;

namespace Ethos.PlatformManager
{
    /// <summary>
    /// 
    /// </summary>
    public class PlatformManagerDbContext : DbContext
    {
        /// <summary>
        /// 
        /// </summary>
        public DbSet<License> Licenses { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosTenant> Tenants { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<TenantContactPerson> TenantContacts { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosUser> Users { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<Product> Products { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<ProductFeature> ProductFeatures { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<Feature> Features { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<LicenseProduct> LicenseProducts { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<LicenseProductFeature> LicenseProductFeatures { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        public PlatformManagerDbContext(DbContextOptions<PlatformManagerDbContext> options)
            : base(options) {
        }

        /// <summary>
        /// 
        /// </summary>
        public const string PlatformDbSchema = "Platform";


        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosRole> Roles { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosDbScope> Scopes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosRoleAssignment> RoleAssignments { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DbSet<EthosRoleScope> RoleScopes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="modelBuilder"></param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Roles
            modelBuilder.Entity<License>(entity =>
            {
                entity.ToTable("Licenses", PlatformDbSchema);

                entity.HasKey(l => l.Id);

                entity.Property(l => l.State)
                      .IsRequired()
                      .HasDefaultValue(LicenseState.Active);

                entity.Property(l => l.TenantId)
                      .IsRequired()
                      .HasDefaultValue(Guid.Empty);

                entity
                    .HasMany(l => l.LicenseProducts)
                    .WithOne(lv => lv.License)
                    .HasForeignKey(l => l.LicenseId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(l => l.Tenant)
                    .WithMany(lv => lv.Licenses)
                    .HasForeignKey(l => l.TenantId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Products
            modelBuilder.Entity<Product>(entity =>
            {
                entity.ToTable("Products", PlatformDbSchema);
                entity.HasKey(l => l.Id);
                entity.Property(l => l.Name)
                      .IsRequired();
                entity.HasIndex(l => l.Name)
                      .IsUnique();
                entity
                    .HasMany(l => l.LicenseProducts)
                    .WithOne(lv => lv.Product)
                    .HasForeignKey(l => l.ProductId)
                    .OnDelete(DeleteBehavior.Restrict);
                entity
                   .HasMany(l => l.Features)
                   .WithOne(lv => lv.Product)
                   .HasForeignKey(l => l.ProductId)
                   .OnDelete(DeleteBehavior.Restrict);
            });

            // Features
            modelBuilder.Entity<Feature>(entity =>
            {
                entity.ToTable("Features", PlatformDbSchema);
                entity.HasKey(l => l.Id);
                entity.Property(l => l.Name)
                      .IsRequired();
                entity.HasIndex(l => l.Name)
                      .IsUnique();
                entity.Property(l => l.ScopePrefix)
                      .IsRequired();
            });

            // Product Features
            modelBuilder.Entity<ProductFeature>(entity =>
            {
                entity.ToTable("ProductFeatures", PlatformDbSchema);
                entity.HasKey(l => new { l.ProductId, l.FeatureId });
                entity.Property(l => l.Name)
                      .IsRequired();
                entity.Property(l => l.Enabled)
                      .IsRequired()
                      .HasDefaultValue(true);
                entity.Property(l => l.ProductId)
                      .IsRequired();
                entity.Property(l => l.FeatureId)
                      .IsRequired();
                //entity.HasIndex(l => new { l.ProductId, l.FeatureId })
                //      .IsUnique();
                entity
                   .HasOne(l => l.Product)
                   .WithMany(lv => lv.Features)
                   .HasForeignKey(l => l.ProductId)
                   .OnDelete(DeleteBehavior.Cascade);
            });

            // License Product Features
            modelBuilder.Entity<LicenseProductFeature>(entity =>
            {
                entity.ToTable("LicenseProductFeatures", PlatformDbSchema);
                entity.HasKey(l => new { l.LicenseProductId, l.FeatureId });
                entity.Property(l => l.Name)
                      .IsRequired();
                entity.Property(l => l.Enabled)
                      .IsRequired()
                      .HasDefaultValue(true);
                entity.Property(l => l.LicenseProductId)
                      .IsRequired();
                entity.Property(l => l.FeatureId)
                      .IsRequired();
                //entity.HasIndex(l => new { l.LicenseProductId, l.FeatureId })
                //      .IsUnique();
                entity
                   .HasOne(l => l.LicenseProduct)
                   .WithMany(lv => lv.Features)
                   .HasForeignKey(l => l.LicenseProductId)
                   .OnDelete(DeleteBehavior.Cascade);
            });

            // Tenants
            modelBuilder.Entity<EthosTenant>(entity =>
            {
                entity.ToTable("Tenants", PlatformDbSchema);
                entity.HasKey(l => l.Id);
                entity.Property(l => l.OrganizationName)
                      .IsRequired();
                entity
                   .HasMany(l => l.Licenses)
                   .WithOne(lv => lv.Tenant)
                   .HasForeignKey(l => l.TenantId)
                   .OnDelete(DeleteBehavior.Restrict);

                entity
                   .HasMany(l => l.Contacts)
                   .WithOne(lv => lv.Tenant)
                   .HasForeignKey(l => l.TenantId)
                   .OnDelete(DeleteBehavior.Cascade);

            });

            // Tenant contacts
            modelBuilder.Entity<TenantContactPerson>(entity =>
            {
                entity.ToTable("TenantContacts", PlatformDbSchema);
                entity.HasKey(l => l.Id);
                entity.Property(l => l.FirstName)
                      .IsRequired();
                entity.Property(l => l.LastName)
                      .IsRequired();
                entity
                   .HasOne(l => l.Tenant)
                   .WithMany(lv => lv.Contacts)
                   .HasForeignKey(l => l.TenantId)
                   .OnDelete(DeleteBehavior.Restrict);

            });

            // Licensed products
            modelBuilder.Entity<LicenseProduct>(entity =>
            {
                entity.ToTable("LicenseProducts", PlatformDbSchema);

                entity.HasKey(l => l.Id);

                entity.Property(l => l.Name)
                     .IsRequired();

                entity
                    .HasOne(l => l.License)
                    .WithMany(lv => lv.LicenseProducts)
                    .HasForeignKey(l => l.LicenseId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasOne(l => l.Product)
                    .WithMany(lv => lv.LicenseProducts)
                    .HasForeignKey(l => l.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasMany(l => l.Features)
                    .WithOne(lv => lv.LicenseProduct)
                    .HasForeignKey(l => l.LicenseProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Roles
            modelBuilder.Entity<EthosRole>(entity =>
            {
                entity.ToTable("Roles", PlatformDbSchema);

                entity.HasKey(l => new { l.Id, l.TenantId });

                entity.Property(l => l.Name)
                      .IsRequired();

                entity.HasIndex(l => new { l.Name, l.TenantId })
                      .IsUnique();

                entity.Property(l => l.Filters)
                      .HasColumnType("jsonb")
                      .HasDefaultValue(null);

                entity
                    .HasMany(l => l.RoleAssignments)
                    .WithOne(lv => lv.Role)
                    .HasForeignKey(l => l.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Scopes
            modelBuilder.Entity<EthosDbScope>(entity =>
            {
                entity.ToTable("Scopes", PlatformDbSchema);
                entity.HasKey(l => l.RowId);
                entity.Property(l => l.RowId)
                      .ValueGeneratedOnAdd();
                entity.Property(l => l.Name)
                      .IsRequired();
                entity.HasIndex(l => l.Name)
                      .IsUnique();
            });

            // Role assignments
            modelBuilder.Entity<EthosRoleAssignment>(entity =>
            {
                entity.ToTable("Assignments", PlatformDbSchema);

                entity.HasKey(l => new { l.RoleId, l.UserId, l.TenantId });

                entity
                    .HasOne(l => l.Role)
                    .WithMany(lv => lv.RoleAssignments)
                    .HasForeignKey(l => new { l.RoleId, l.TenantId })
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(l => l.User)
                    .WithMany(lv => lv.RoleAssignments)
                    .HasForeignKey(l => new { l.UserId, l.TenantId })
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Role scopes
            modelBuilder.Entity<EthosRoleScope>(entity =>
            {
                entity.ToTable("RoleScopes", PlatformDbSchema);

                entity.HasKey(l => new { l.RoleId, l.Scope, l.TenantId });

                entity
                    .HasOne(l => l.Role)
                    .WithMany(lv => lv.Scopes)
                    .HasForeignKey(l => new { l.RoleId, l.TenantId })
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Users
            modelBuilder.Entity<EthosUser>(entity =>
            {
                entity.ToTable("Users", PlatformDbSchema);

                entity.HasKey(l => new { l.Id, l.TenantId });

                entity.Property(l => l.DisplayName)
                      .IsRequired();

                entity.Property(l => l.Active)
                      .IsRequired()
                      .HasDefaultValue(true);

                entity.Property(l => l.Deleted)
                      .IsRequired()
                      .HasDefaultValue(false);

                entity.Property(l => l.GivenName)
                      .IsRequired();

                entity.Property(l => l.Surname)
                      .IsRequired();

                entity
                    .HasMany(l => l.RoleAssignments)
                    .WithOne(lv => lv.User)
                    .HasForeignKey(l => new { l.UserId, l.TenantId })
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
