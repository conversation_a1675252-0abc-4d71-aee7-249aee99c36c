namespace Ethos.Workflows.HttpClient;

using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;

/// <summary>
/// Talks to /api/login and caches the returned bearer token.
/// </summary>
public sealed class AuthHttpClient
{
    public readonly HttpClient HttpClient;
    private string? _cachedToken;

    public AuthHttpClient(HttpClient httpClient)
    {
        HttpClient = httpClient;                     // BaseAddress must point at the API root.
    }

    /* ---------- DTOs that match the controller ---------- */

    private sealed record LoginRequest(string Username, string Password);
    private sealed record LoginResponse(string Token);
    private sealed record TokenTypeResponse(string TokenType);

    /* ---------- Public API ---------- */

    /// <summary>
    /// POST /api/login  →  caches and returns the bearer token.
    /// </summary>
    public async Task<string> LoginAsync(string username, string password)
    {
        var body = new LoginRequest(username, password);
        var resp = await HttpClient.PostAsJsonAsync("api/login", body);
        resp.EnsureSuccessStatusCode();

        var data = await resp.Content.ReadFromJsonAsync<LoginResponse>()
                   ?? throw new("Malformed login response.");

        _cachedToken = data.Token;
        HttpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", _cachedToken);

        return _cachedToken;
    }

    /// <summary>
    /// GET /api/login/check  (requires Authorization header)  
    /// Returns "AzureAD" or "LocalJWT".
    /// </summary>
    public async Task<string> CheckTokenTypeAsync()
    {
        EnsureToken();
        var resp = await HttpClient.GetAsync("api/login/check");
        resp.EnsureSuccessStatusCode();

        var data = await resp.Content.ReadFromJsonAsync<TokenTypeResponse>()
                   ?? throw new("Malformed check‑token response.");

        return data.TokenType;
    }

    /// <summary>Convenience accessor for the last successful login.</summary>
    public string CurrentToken
    {
        get => _cachedToken ?? throw new("No token cached. Call LoginAsync first.");
    }

    private void EnsureToken()
    {
        if (_cachedToken is null)
            throw new("No bearer token present. Call LoginAsync first.");
    }
}