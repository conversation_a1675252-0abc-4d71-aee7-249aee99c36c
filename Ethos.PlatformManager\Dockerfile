FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
# Install packages once, cache the debs across builds
RUN apt-get update \
    && apt-get install -y --no-install-recommends postgresql-client \
    && rm -rf /var/lib/apt/lists/*
WORKDIR /app
# ARG APP_UID=10001
RUN useradd -u 10001 -r -s /sbin/nologin appuser
# USER appuser

# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

COPY ["Ethos.Utilities/Ethos.Utilities.csproj", "Ethos.Utilities/"]
COPY ["Directory.Packages.props", "Ethos.Utilities/"]
COPY ["Ethos.Auth/Ethos.Auth.csproj", "Ethos.Auth/"]
COPY ["Directory.Packages.props", "Ethos.Auth/"]
COPY ["Ethos.ReferenceData.Client/Ethos.ReferenceData.Client.csproj", "Ethos.ReferenceData.Client/"]
COPY ["Directory.Packages.props", "Ethos.ReferenceData.Client/"]
COPY ["Ethos.Model/Ethos.Model.csproj", "Ethos.Model/"]
COPY ["Directory.Packages.props", "Ethos.Model/"]
COPY ["Ethos.Events.Client/Ethos.Events.Client.csproj", "Ethos.Events.Client/"]
COPY ["Directory.Packages.props", "Ethos.Events.Client/"]

COPY ["Ethos.PlatformManager/Ethos.PlatformManager.csproj", "Ethos.PlatformManager/"]
COPY ["Directory.Packages.props", "Ethos.PlatformManager/"]
RUN dotnet restore "./Ethos.PlatformManager/Ethos.PlatformManager.csproj"

COPY ["Ethos.Utilities/", "Ethos.Utilities/"]
COPY ["Ethos.Auth/", "Ethos.Auth/"]
COPY ["Ethos.ReferenceData.Client/", "Ethos.ReferenceData.Client/"]
COPY ["Ethos.Model/", "Ethos.Model/"]
COPY ["Ethos.Events.Client/", "Ethos.Events.Client/"]
COPY ["Ethos.PlatformManager/", "Ethos.PlatformManager/"]
WORKDIR "/src/Ethos.PlatformManager"
RUN dotnet build "./Ethos.PlatformManager.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Ethos.PlatformManager.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM runtime AS final
WORKDIR /app
COPY --from=publish /app/publish .

COPY "./Ethos.PlatformManager/entrypoint.sh" "entrypoint.sh"
RUN chmod +x ./entrypoint.sh

EXPOSE 8080
EXPOSE 8081
USER appuser
ENTRYPOINT ["./entrypoint.sh"]