<Project Sdk="Microsoft.NET.Sdk.Worker">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>dotnet-Persante.Workflow.Service-d7b2b1f5-6f4f-4df5-990e-495865997602</UserSecretsId>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <PackageId>Ethos.Workflows</PackageId>
        <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" />
        <PackageReference Include="JsonPath.Net" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Common" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Hosting" />
        <PackageReference Include="Microsoft.Identity.Web" />
        <PackageReference Include="Newtonsoft.Json" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
        <PackageReference Include="Serilog.AspNetCore" />
        <PackageReference Include="Serilog.Sinks.Console" />
        <PackageReference Include="Serilog.Sinks.File" />
        <PackageReference Include="SixLabors.ImageSharp" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
        <PackageReference Include="YamlDotNet" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="flow.json" />
        <EmbeddedResource Include="py\ethos_common.py" />
        <EmbeddedResource Include="py\login_api_v0.py" />
        <EmbeddedResource Include="py\file_api_v0.py" />
        <EmbeddedResource Include="py\test_script.py" />

        <EmbeddedResource Include="ts\ethos-common.ts" />
        <EmbeddedResource Include="ts\login-api.ts" />
        <EmbeddedResource Include="ts\test-script.ts" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Ethos.Model\Ethos.Model.csproj" />
        <ProjectReference Include="..\Ethos.Auth\Ethos.Auth.csproj" />
        <ProjectReference Include="..\Ethos.Utilities\Ethos.Utilities.csproj" />
        <ProjectReference Include="..\FastAuthClient\FastAuthClient.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Migrations\" />
    </ItemGroup>
</Project>
