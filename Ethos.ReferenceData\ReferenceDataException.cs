﻿namespace Ethos.ReferenceData
{
    /// <summary>
    /// 
    /// </summary>
    public class ReferenceDataException : Exception
    {
        /// <summary>
        /// 
        /// </summary>
        public int StatusCode { get; set; } = 400;

        /// <summary>
        /// 
        /// </summary>
        public ReferenceDataException() { }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <param name="message"></param>
        public ReferenceDataException(int code, string message) : base(message)
        { StatusCode = code; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <param name="message"></param>
        /// <param name="innerEx"></param>
        public ReferenceDataException(int code, string message, Exception innerEx) : base(message, innerEx)
        { StatusCode = code; }
    }
}
