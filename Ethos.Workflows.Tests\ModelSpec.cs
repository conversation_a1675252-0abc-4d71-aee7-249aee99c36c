using System.Collections.Immutable;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Model.Scheduling;
using Ethos.ReferenceData.Client;
using Ethos.Utilities;
using Ethos.Workflows.Api;
using Ethos.Workflows.Api.Analysis;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit.Abstractions;

namespace Ethos.Workflows.Tests;

public class ModelSpec(ITestOutputHelper testOutputHelper)
{
    [Fact]
    public void CheckThatGetAllEntityTypesReturnsExpectedTypes()
    {
        var entityTypes = Helpers.GetAllEntityTypes();
        entityTypes.Should().NotBeEmpty("Expected to find at least one entity type in the assembly.");
        entityTypes.Should().Contain(typeof(EditRecordDbo));
    }
    
    [Fact]
    public void CheckThatGetAllQueryTypesReturnsExpectedTypes()
    {
        var queryTypes = Helpers.GetAllQueryTypes();
        queryTypes.Should().NotBeEmpty("Expected to find at least one query type in the assembly.");
        queryTypes.Should().Contain(typeof(PatientQ));
        queryTypes.Should().Contain(typeof(SchedulingConstraintQ));
    }

    

    [Fact]
    public void TestEntityTypeSerialization()
    {
        var result = JsonSerializer.Serialize(EntityType.Address);
        result.Should().Be("\"Address\"", "Expected EntityType.Address to serialize to \"Address\".");
        testOutputHelper.WriteLine($"Serialized EntityType.Address: {result}");
        var deserialized = JsonSerializer.Deserialize<EntityType>(result);
        deserialized.Should().Be(EntityType.Address, "Expected deserialized value to match EntityType.Address.");
    }
    
    [Fact]
    public void CheckThatGetAllDtoTypesReturnsExpectedTypes()
    {
        var dtoTypes = Helpers.GetAllDtoTypes();
        dtoTypes.Should().NotBeEmpty("Expected to find at least one DTO type in the assembly.");
        dtoTypes.Should().Contain(typeof(PatientDto));
        dtoTypes.Should().Contain(typeof(SchedulingConstraintDto));
    }

    [Fact]
    public void CheckDbos()
    {
        var entityTypes = Helpers.GetAllEntityTypes();
        
        foreach (var entityType in entityTypes)
        {
            // RULE: Entity type names must end with "Dbo".
            var entityTypeName = entityType.Name;
            entityTypeName.Should().EndWith("Dbo", 
                $"Expected entity type name to end with 'Dbo', but found '{entityTypeName}'.");
            
            // RULE: Entity types must not have any bare public fields, only properties.
            var fields = entityType.GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            fields.Should().BeEmpty($"Entity type {entityType.Name} should not have any fields, only properties. Found fields: {string.Join(", ", fields.Select(f => f.Name))}.");
            
            testOutputHelper.WriteLine("Checking entity type: " + entityType.Name);
        }
    }
    
    
    [Fact]
    public void CheckDtos()
    {
        var entityControllerBaseType = typeof(Ethos.Workflows.Api.PatientDto);
        var assembly = entityControllerBaseType.Assembly;
        
        // Find all types that end with "Dto" and are not abstract
        var dtoTypes = Helpers.GetAllDtoTypes();

        foreach (var dtoType in dtoTypes)
        {
            // RULE: Dto types must not have any bare public fields, only properties.
            var fields = dtoType.GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            fields.Should().BeEmpty($"Dto type {dtoType.Name} should not have any fields, only properties. Found fields: {string.Join(", ", fields.Select(f => f.Name))}.");
            
            // RULE: Dto type names must end with "Dto".
            var dtoTypeName = dtoType.Name;
            dtoTypeName.Should().EndWith("Dto", 
                $"Expected DTO type name to end with 'Dto', but found '{dtoTypeName}'.");
            
            // RULE: Dto types must be sealed records.
            dtoType.IsSealedRecord().Should().BeTrue($"Expected DTO type {dtoType.Name} to be a sealed record, but it is not.");
            
            foreach (var property in dtoType.GetProperties())
            {
                // RULE: Dto properties must be public and have both getter and setter.
                property.GetMethod.Should().NotBeNull($"Property {property.Name} in {dtoType.Name} should have a public getter.");
                property.SetMethod.Should().NotBeNull($"Property {property.Name} in {dtoType.Name} should have a public setter.");
                
                // RULE: Dto properties must either be primitive types or other DTOs.
                // this rule is ignored for ReferenceDataValidationDto.
                if (dtoType == typeof(ReferenceDataValidationDto) && property.Name == "Value")
                {
                    continue; // Special case for ReferenceDataValidationDto
                }
                var propertyType = property.PropertyType;
                if (!Helpers.IsValidDtoProperty(propertyType))
                {
                    throw new InvalidOperationException(
                        $"Property {property.Name} in {dtoType.Name} has an unsupported type: {propertyType.FullName}. " +
                        "Only primitive types or other DTOs are allowed.");
                }
            }
            
            // RULE: All DTOs must be generatable by the Generator.
            for (int i = 0; i < 100; i++)
            {
                var value = Generator.Generate(dtoType);
                value.Should().NotBeNull($"Expected to generate a valid instance of {dtoType.Name}, but got null.");
                
                // Check that the generated value is of the correct type
                value.Should().BeOfType(dtoType, 
                    $"Expected generated value to be of type {dtoType.Name}, but got {value.GetType().Name}.");
            }
        }
    }

    [Fact]
    public void FindMissingConverters()
    {
        // Find all pairs of (dbo, dto) with the same base name
        var dboTypes = Helpers.GetAllEntityTypes();
        var dtoTypes = Helpers.GetAllDtoTypes();
        var dboToDtoPairs = dboTypes.Select(dbo => 
            (dbo, dto: dtoTypes.FirstOrDefault(dto => dto.Name.StartsWith(dbo.Name.Substring(0, dbo.Name.Length - 3) + "Dto"))))
            .Where(pair => pair.dto != null)
            .ToList();
        
        testOutputHelper.WriteLine($"Found {dboToDtoPairs.Count} dbo-dto pairs.");
        foreach(var (dbo, dto) in dboToDtoPairs)
        {
            testOutputHelper.WriteLine($"Dbo: {dbo.Name}, Dto: {dto.Name}");
        }
        
        // Check that there is a converter for each pair
        var toDboConverters = Helpers.GetToEntityConverters();
        var toDtoConverters = Helpers.GetToDtoConverters();
        
        foreach (var (dbo, dto) in dboToDtoPairs)
        {
            // RULE: There must be a ToEntity converter for each Dbo type.
            var toEntityConverter = toDboConverters.FirstOrDefault(c => c.to == dbo);
            toEntityConverter.Should().NotBeNull($"Expected a ToEntity converter for {dbo.Name}, but found none.");
            
            // RULE: There must be a ToDto converter for each Dto type.
            var toDtoConverter = toDtoConverters.FirstOrDefault(c => c.from == dto);
            toDtoConverter.Should().NotBeNull($"Expected a ToDto converter for {dto.Name}, but found none.");
        }
    }
    
    [Fact]
    public void TestConverters()
    {
        var classType = typeof(Converters);
        void TestConverter(Type type)
        {
            var toEntity = classType.GetMethod("ToEntity", [type]);
            var entityType = toEntity!.ReturnType;
            var toDto = classType.GetMethod("ToDto", [entityType]);
            
            // RULE: Entity type name should end with "Dbo".
            var entityTypeName = entityType.Name;
            entityTypeName.Should().EndWith("Dbo", 
                $"Expected entity type name to end with 'Dbo', but found '{entityTypeName}' for type '{type.Name}'.");
            entityTypeName = entityTypeName.Substring(0, entityTypeName.Length - "Dbo".Length);
            
            // RULE: DTO type name should end with "Dto".
            var dtoTypeName = type.Name;
            dtoTypeName.Should().EndWith("Dto", 
                $"Expected DTO type name to end with 'Dto', but found '{dtoTypeName}' for entity type '{entityTypeName}'.");
            dtoTypeName = dtoTypeName.Substring(0, dtoTypeName.Length - "Dto".Length);
            
            // RULE: Entity type name should match DTO type name.
            entityTypeName.Should().Be(dtoTypeName, 
                $"Expected entity type name '{entityTypeName}' to match DTO type name '{dtoTypeName}'.");

            for (int i = 0; i < 100; i++)
            {
                var value = Generator.Generate(type);
                Helpers.ValidateInstance(value!);
                var entity = toEntity.Invoke(null, [value]);
                entity.Should().NotBeNull();
                // Helpers.ValidateInstance(entity);
                var dto = toDto!.Invoke(null, [entity]);
                dto.Should().NotBeNull();
                value.Should().BeEquivalentTo(dto);
                Helpers.ValidateInstance(dto);
            }
        }
        
        // Find all conversion methods in the Converters class
        foreach (var type in Helpers.GetToEntityConverters().Select(t => t.from).Distinct())
        {
            testOutputHelper.WriteLine($"Testing ToEntity conversion for type: {type.Name}");
            TestConverter(type);
        }
    }
    
    [Fact]
    public void CheckQueries()
    {
        var iEntityType = typeof(Ethos.Model.IEntity);
        var queryTypes = Helpers.GetAllQueryTypes();
        var assembly = iEntityType.Assembly;
        // Find all types <: IEntity
        var entityTypes = Helpers.GetAllEntityTypes();
        
        typeof(PatientDbo).HasProperty("Demographics")
            .Should().BeTrue(
                "Expected PatientDbo to have a Demographics property, but it does not.");
        typeof(PatientDbo).HasProperty("Demographics", typeof(Ethos.Model.DemographicsDbo))
            .Should().BeTrue(
                "Expected PatientDbo to have a Demographics property of type DemographicsDbo, but it does not.");
        
        foreach (var queryType in queryTypes)
        {
            testOutputHelper.WriteLine($"Checking query type: {queryType.Name}");
            
            // RULE: Every Query type must end with a "Q" suffix
            queryType.Name.Should().EndWith("Q");
            var queryName = queryType.Name.Substring(0, queryType.Name.Length - 1);
            
            // RULE: Every Query type has a corresponding entity type with the same name
            var entityType = entityTypes.FirstOrDefault(t => t.Name == (queryName + "Dbo"));
            entityType.Should().NotBeNull($"Expected an entity type for query type {queryType.Name}, but found none.");
            
            // Find all derived types of the query type
            var derivedQueryTypes = Helpers.GetAllQueryConstructorTypes(queryType);
            
            // RULE: Query type's class must have a BuildPredicateBody method
            var buildPredicateBodyMethod = queryType.GetMethod("BuildPredicateBody");
            buildPredicateBodyMethod.Should().NotBeNull(
                $"Expected query type {queryType.Name} to have a BuildPredicateBody method, but found none.");
            
            // RULE: Every Query type has JsonPolymorphic and JsonDerivedType attributes
            var jsonPolymorphicAttribute = queryType.GetCustomAttributes(typeof(System.Text.Json.Serialization.JsonPolymorphicAttribute), false)
                .FirstOrDefault() as System.Text.Json.Serialization.JsonPolymorphicAttribute;
            jsonPolymorphicAttribute.Should().NotBeNull(
                $"{queryType.Name}: Expected query type to have a JsonPolymorphic attribute, but found none.");
            
            // RULE: Every Query type has at least one JsonDerivedType attribute.
            var jsonDerivedTypeAttributes = queryType.GetCustomAttributes(typeof(System.Text.Json.Serialization.JsonDerivedTypeAttribute), false)
                .Cast<System.Text.Json.Serialization.JsonDerivedTypeAttribute>()
                .ToList();
            jsonDerivedTypeAttributes.Should().NotBeEmpty(
                $"{queryType.Name}: Expected query type to have at least one JsonDerivedType attribute, but found none.");
            foreach (var derivedQueryType in derivedQueryTypes)
            {
                var derivedTypeName = derivedQueryType.Name;
                var jsonDerivedTypeAttribute = jsonDerivedTypeAttributes
                    .FirstOrDefault(a => a.DerivedType.Name == derivedTypeName);
                
                // RULE: Every Query constructor has a JsonDerivedType attribute
                jsonDerivedTypeAttribute.Should().NotBeNull(
                    $"{queryType.Name}: Expected query type to have a JsonDerivedType attribute for {derivedTypeName}, but found none.");
                
                // RULE: Every Query constructor has a TypeDiscriminator that matches the derived type name
                jsonDerivedTypeAttribute.TypeDiscriminator.Should().Be(derivedTypeName,
                    $"{queryType.Name}: Expected JsonDerivedType attribute for {derivedTypeName} to have TypeDiscriminator {derivedTypeName}, but found {jsonDerivedTypeAttribute.TypeDiscriminator}.");
                
                // RULE: Every Query constructor is a sealed record.
                derivedQueryType.IsSealedRecord().Should().BeTrue(
                    $"{queryType.Name}: Expected derived query type {derivedTypeName} to be a sealed record, but it is not.");
            }
            
            void RequireDerivedType(string expectedName, List<(string Name, Type type)> arguments)
            {
                var derivedType = derivedQueryTypes.FirstOrDefault(t => t.Name == expectedName);
                derivedType.Should().NotBeNull(
                    $"Expected a derived query type named {expectedName} for entity {entityType.Name}, but found none.");
                // Check that the derived type has the expected arguments
                var constructor = derivedType.GetConstructor(arguments.Select(a => a.type).ToArray());
                constructor.Should().NotBeNull(
                    $"Expected derived query type {derivedType.Name} to have a constructor with arguments {string.Join(", ", arguments.Select(a => $"{a.Name}: {a.type.Name}"))}, but found none.");
                // Check that the derived type has the expected properties
                foreach (var arg in arguments)
                {
                    var property = derivedType.GetProperty(arg.Name);
                    property.Should().NotBeNull(
                        $"Expected derived query type {derivedType.Name} to have a property {arg.Name} of type {arg.type.Name}, but found none.");
                    property!.PropertyType.Should().Be(arg.type,
                        $"Expected property {arg.Name} of derived query type {derivedType.Name} to be of type {arg.type.Name}, but found {property.PropertyType.Name}.");
                }
            }
            
            // RULE: If Entity type has Id property, then there must be `WithId` query constructor.
            // public sealed record WithId(Guid Id) : PatientQ;
            // public static DraftQ HasId(Guid id) => new WithId(id);
            if (entityType.HasProperty("Id")) RequireDerivedType("WithId", [("Id", typeof(Guid))]);
            
            // RULE: If Entity type has Identifiers property, then there must be `WithIdentifier` query constructor.
            // public sealed record WithIdentifier(string System, string Value) : PatientQ;
            if (entityType.HasProperty("Identifiers")) RequireDerivedType("WithIdentifier", [("System", typeof(string)), ("Value", typeof(string))]);
            
            // RULE: If Entity type has PersonalContactDetailEntity ContactDetail property, then there must be `WithEmail` and `WithPhoneNumber` query constructors.
            // public PersonalContactDetailEntity ContactDetail { get; set; } = null!;
            // public sealed record WithEmail(string Email) : PatientQ;
            // public sealed record WithPhoneNumber(string PhoneNumber) : PatientQ; // Exact match?
            if (entityType.HasProperty("ContactDetail", typeof(Ethos.Model.PersonalContactDetailDbo)))
            {
                RequireDerivedType("WithEmail", [("Email", typeof(string))]);
                RequireDerivedType("WithPhoneNumber", [("PhoneNumber", typeof(string))]);
            }
            
            if (entityType.HasProperty("Demographics", typeof(Ethos.Model.DemographicsDbo)))
            {
                RequireDerivedType("WithBirthSex", [("Value", typeof(long?))]);
                RequireDerivedType("WithGender", [("Value", typeof(long?))]);
                RequireDerivedType("WithRace", [("Value", typeof(long?))]);
                RequireDerivedType("WithEthnicity", [("Value", typeof(long?))]);
                RequireDerivedType("WithMaritalStatus", [("Value", typeof(long?))]);
                RequireDerivedType("WithDateOfBirth", [("Lower", typeof(DateOnly?)), ("Upper", typeof(DateOnly?))]);
            }
            
            // RULE: If Entity type has Names property, then there must be `WithGivenName`, `WithLastName`, `WithApproximateFullName` query constructors.
            if (entityType.HasProperty("Names"))
            {
                RequireDerivedType("WithGivenName", [("Value", typeof(string))]);
                RequireDerivedType("WithLastName", [("Value", typeof(string))]);
                RequireDerivedType("WithApproximateFullName", [("Value", typeof(string))]);
            }
            
            // RULE: If Entity type has Name property, then there must be `WithName`, `WithApproximateName` query constructors.
            if (entityType.HasProperty("Name"))
            {
                RequireDerivedType("WithName", [("Name", typeof(string))]);
                RequireDerivedType("WithApproximateName", [("Name", typeof(string))]);
            }
            
            foreach (var derivedQueryType in derivedQueryTypes)
            {
                for (int i = 0; i < 100; i++)
                {
                    var q = Generator.Generate(derivedQueryType);
                    // RULE: Every query constructor must be compilable to an Expression<Func<TEntity, bool>>.
                    var predicateBody = buildPredicateBodyMethod!.Invoke(q, [Expression.Parameter(entityType)]);
                }
            }
        }
    }
    
        [Fact]
    public async Task CheckQueriesCanBeTranslatedToSql()
    {
        // Setup: Create a real Postgres database instance for this test run.
        // This is crucial because we need the Npgsql EF Core provider to translate the expression.
        await using var scope = new Scope();
        var ctx = await Helpers.CreateTestPostgresDatabase(scope);

        var queryTypes = Helpers.GetAllQueryTypes();
        var entityTypes = Helpers.GetAllEntityTypes();

        // Shuffle query types to ensure we test them in a random order.
        queryTypes = queryTypes.OrderBy(_ => Guid.NewGuid()).ToList();
        
        foreach (var queryType in queryTypes)
        {
            var queryName = queryType.Name.Substring(0, queryType.Name.Length - 1);
            var entityType = entityTypes.FirstOrDefault(t => t.Name == (queryName + "Dbo"));
            entityType.Should().NotBeNull($"Could not find entity type for query {queryType.Name}");

            var buildPredicateBodyMethod = queryType.GetMethod("BuildPredicateBody");
            buildPredicateBodyMethod.Should().NotBeNull($"Query type {queryType.Name} must have a 'BuildPredicateBody' method.");

            var derivedQueryTypes = Helpers.GetAllQueryConstructorTypes(queryType);
            
            // Get the generic IQueryable<T> and Where() method definitions once.
            var dbSetQueryable = ctx.Set(entityType);
            
            // Find the generic 'Where' method on the Queryable class
            var whereMethodInfo = typeof(Queryable).GetMethods()
                .First(m => m.Name == "Where" && m.GetParameters().Length == 2)
                .MakeGenericMethod(entityType);
            
            // Find the generic 'FirstOrDefaultAsync' extension method from EF Core
            var firstOrDefaultAsyncMethodInfo = typeof(EntityFrameworkQueryableExtensions).GetMethods()
                .First(m => m.Name == nameof(EntityFrameworkQueryableExtensions.FirstOrDefaultAsync) && 
                            m.GetParameters().Length == 2 && // Ensure we get the one with IQueryable and CancellationToken
                            m.GetParameters()[0].ParameterType.GetGenericTypeDefinition() == typeof(IQueryable<>))
                .MakeGenericMethod(entityType);

            foreach (var derivedQueryType in derivedQueryTypes)
            {
                testOutputHelper.WriteLine($"Testing SQL translation for: {derivedQueryType.FullName}");

                // 1. Generate a random instance of the query constructor (e.g., WithId, WithName).
                var queryInstance = Generator.Generate(derivedQueryType);
                queryInstance.Should().NotBeNull();
                
                // 2. Build the expression tree predicate: Expression<Func<TEntity, bool>>
                var parameter = Expression.Parameter(entityType, "e");
                var predicateBody = buildPredicateBodyMethod!.Invoke(queryInstance, [parameter]);
                var predicateLambda = Expression.Lambda(predicateBody as Expression, parameter);
                
                // 3. Apply the predicate to the DbSet using the generic Where() method.
                // This creates an IQueryable<TEntity> representing the full query.
                var queryable = whereMethodInfo.Invoke(null, [dbSetQueryable, predicateLambda]);

                // 4. THE ACTUAL TEST: Attempt to execute the query against the database.
                // We use FirstOrDefaultAsync() which forces EF Core to translate the expression
                // tree into SQL and send it to Postgres. We don't care about the result,
                // only that this operation doesn't throw a translation exception.
                var executionAction = async () =>
                {
                    // The result of invoking the method is a Task<TEntity>, which we can await.
                    var task = (Task)firstOrDefaultAsyncMethodInfo.Invoke(null, [queryable, CancellationToken.None])!;
                    await task;
                };

                // Assert that the database execution does not throw.
                await executionAction.Should().NotThrowAsync(
                    $"EF Core failed to translate the query from '{derivedQueryType.Name}' into valid SQL for PostgreSQL. " +
                    "Check if the logic inside its 'BuildPredicateBody' uses methods or properties that have no SQL equivalent.");
            }
        }
    }
    
    [Fact]
    public async Task CheckEntityControllers()
    {
        using var scope = new Scope();
        var ctx = await Helpers.CreateInMemorySqliteDatabase(scope);

        // Find all entity controllers
        var entityBaseType = typeof(Ethos.Workflows.Api.EntityControllerBase<,,,>);
        var entityControllers = Helpers.GetAllEntityControllers();

        foreach (var controller in entityControllers)
        {
            // RULE: Entity controller names must end with "Controller".
            controller.Name.Should().EndWith("Controller",
                $"Expected entity controller name to end with 'Controller', but found '{controller.Name}'.");
            
            var instance = controller.NewInstance(ctx);
            instance.Should().NotBeNull($"Expected to create an instance of {controller.Name}, but failed.");
            
            // Check MapToDto method
            var mapToDtoMethod = controller.MapToDtoMethod;
            mapToDtoMethod.Should().NotBeNull($"Expected {controller.Name} to have a MapToDto method, but found none.");
            
            // Call MapToDto with a generated entity
            for (int i = 0; i < 100; i++)
            {
                var entity = Generator.Generate(controller.EntityType);
                entity.Should().NotBeNull($"Expected to generate a valid instance of {controller.EntityType.Name}, but got null.");
                
                // Call MapToDto
                var dto = mapToDtoMethod!.Invoke(instance, [entity]);
                dto.Should().NotBeNull($"Expected MapToDto to return a valid DTO, but got null.");
                
                // Check that the returned DTO is of the correct type
                dto.Should().BeOfType(controller.OutputDtoType, 
                    $"Expected MapToDto to return a {controller.OutputDtoType.Name}, but got {dto.GetType().Name}.");
                
                // Validate the DTO instance
                Helpers.ValidateInstance(dto);
            }
            
            // Check CreateOrUpdateEntity method
            var createOrUpdateEntityMethod = controller.CreateOrUpdateEntityMethod;
            createOrUpdateEntityMethod.Should().NotBeNull($"Expected {controller.Name} to have a CreateOrUpdateEntity method, but found none.");
            // Call CreateOrUpdateEntity with a generated entity and input DTO
            for (int i = 0; i < 100; i++)
            {
                var entity = Generator.Generate(controller.EntityType);
                entity.Should().NotBeNull($"Expected to generate a valid instance of {controller.EntityType.Name}, but got null.");
                
                var inputDto = Generator.Generate(controller.InputDtoType);
                inputDto.Should().NotBeNull($"Expected to generate a valid instance of {controller.InputDtoType.Name}, but got null.");

                try
                {
                    var updatedEntity = createOrUpdateEntityMethod.Invoke(instance, [entity, inputDto, null]);
                    updatedEntity.Should()
                        .NotBeNull($"Expected CreateOrUpdateEntity to return a valid entity, but got null.");

                    var guid = Guid.NewGuid();
                    var createdEntity = createOrUpdateEntityMethod.Invoke(instance, [null, inputDto, guid]);
                    createdEntity.Should()
                        .NotBeNull($"Expected CreateOrUpdateEntity to return a valid entity, but got null.");
                }
                catch (TargetInvocationException ex)
                {
                    if (ex.InnerException is RequiredEntityDoesNotExistException)
                    {
                        // This is expected if the input DTO references an entity that does not exist.
                        // testOutputHelper.WriteLine($"Caught expected exception: {ex.Message}");
                        continue;
                    }
                    else
                    {
                        // Any other exception is unexpected
                        testOutputHelper.WriteLine($"Unexpected exception: {ex.InnerException?.GetType()} {ex.InnerException?.Message}");
                    }
                }
                catch (Exception ex)
                {
                    // Any other exception is unexpected
                    testOutputHelper.WriteLine($"Unexpected exception: {ex.GetType()} {ex.Message}");
                }
                // Validate the entity instance
                // Helpers.ValidateInstance(updatedEntity);
            }
        }
    }
    
    [Fact]
    public async Task ValidateEfCoreModelConfiguration()
    {
        using var scope = new Scope();
        var ctx = await Helpers.CreateInMemorySqliteDatabase(scope);

        Action scriptGeneration = () => ctx.Database.GenerateCreateScript();
        scriptGeneration.Should().NotThrow(
            "Generating the database creation script failed. This indicates a fundamental mismatch " +
            "between your C# entity classes and their configuration in OnModelCreating. " +
            "Check the inner exception for specific details about the problematic entity or property.");
    }
    
    [Fact]
    public async Task CheckIncludesAreSufficientForMapping()
    {
        // Get all concrete entity controllers in the assembly
        var entityControllers = Helpers.GetAllEntityControllers();
        entityControllers.Should().NotBeEmpty();
    
        foreach (var controller in entityControllers)
        {
            testOutputHelper.WriteLine($"Testing controller: {controller.Name}");
            
            for (int i = 0; i < 100; i++)
            {
                // 1. Create a fresh in-memory database and controller instance for each test run
                // var options = CreateInMemoryOptions();
                using var scope = new Scope();
                var ctx = await Helpers.CreateInMemorySqliteDatabase(scope);
                
                var instance = controller.NewInstance(ctx);
                instance.Should().NotBeNull();
    
                // 2. Reflect to get the generic types (TEntity, TOutputDto) and protected methods
                var tEntity = controller.EntityType;
                var tOutputDto = controller.OutputDtoType;
                
                var dbSet = ctx.Set(tEntity);
    
                var applyIncludesMethod = controller.ApplyIncludesMethod;
                applyIncludesMethod.Should().NotBeNull($"Controller {controller.Name} must have an 'ApplyIncludes' method.");
                
                var mapToDtoMethod = controller.MapToDtoMethod;
                mapToDtoMethod.Should().NotBeNull($"Controller {controller.Name} must have a 'MapToDto' method.");
    
                // 3. Generate a sample entity and save it to the database
                List<object> toAdd = new List<object>();
                Action<Generator.GenCtx, Type, object> trackEntityCallback = (genCtx, type, instance) =>
                {
                    // The callback receives the context, which holds the DbContext.
                    var dbContext = ctx;
                    if (typeof(Ethos.Model.IEntity).IsAssignableFrom(type))
                    {
                        if (dbContext.Entry(instance).State == Microsoft.EntityFrameworkCore.EntityState.Detached)
                        {
                            toAdd.Add(instance);
                        }
                    }
                };
                
                var genOpts = new Generator.GenerationOptions { OnInstanceCreated = trackEntityCallback, ReuseChancePercent = 0 };
                
                Expression buildFilterExpression(object? entityId)
                {
                    var parameter = Expression.Parameter(tEntity, "e");
                    var filterExpression = Expression.Lambda(
                        Expression.Equal(
                            Expression.Property(parameter, "Id"),
                            Expression.Constant(entityId)
                        ),
                        parameter
                    );
                    return filterExpression;
                }
    
                object? firstOrDefault(object? query, Expression expression)
                {
                    var firstOrDefaultMethod = typeof(Queryable).GetMethods()
                        .First(m => m.Name == "FirstOrDefault"
                                    && m.GetParameters().Length == 2
                                    && m.GetParameters()[1].ParameterType.IsGenericType
                                    && m.GetParameters()[1].ParameterType.GetGenericTypeDefinition() ==
                                    typeof(Expression<>))
                        .MakeGenericMethod(tEntity);
    
                    // Pass the 'queryWithIncludes' object directly. The runtime will handle the type check.
                    var retrievedEntity = firstOrDefaultMethod.Invoke(null, [query, expression]);
                    return retrievedEntity;
                }
    
                object entity = Generator.Generate(tEntity, genOpts)!;
    
                try
                {
                    foreach (var entityToAdd in toAdd)
                    {
                        // Add each entity to the context.
                        var id = Helpers.GetEntityIdUnsafe(entityToAdd);
                        if (entityToAdd.GetType().Name == "InsuranceDbo")
                        {
                            testOutputHelper.WriteLine($"Adding entity: {entityToAdd} with Id {id}");
                        }
    
                        ctx.Add(entityToAdd);
                        var newId = Helpers.GetEntityIdUnsafe(entityToAdd);
                        if (entityToAdd.GetType().Name == "InsuranceDbo")
                        {
                            testOutputHelper.WriteLine($"Added entity: {entityToAdd} with Id {newId}");
                        }
                    }
                    
                    // ctx.Add(entity);
                    await ctx.SaveChangesAsync();
                }
                catch (IndexOutOfRangeException ex)
                {
                    // Re-throw the opaque exception with helpful context.
                    throw new InvalidOperationException(
                        $"EF Core Model Mismatch Detected for entity: '{tEntity.FullName}'. " +
                        "This usually means a foreign key property is missing from the C# class " +
                        "or a relationship is misconfigured in your DbContext's OnModelCreating.", ex);
                }
    
                var entityId = Helpers.GetEntityIdUnsafe(entity);
    
                // 4. CRITICAL: Clear the change tracker to force EF to build a new object from the database,
                // thereby actually testing the Includes.
                ctx.ChangeTracker.Clear();
    
                // 5. Build the query, but DO NOT cast to IQueryable<object>.
                // The result of Invoke is 'object', but its underlying type is the required IQueryable<TEntity>.
                var queryWithIncludes = applyIncludesMethod!.Invoke(instance, [dbSet]);
                queryWithIncludes.Should().NotBeNull();
    
                // Pass the 'queryWithIncludes' object directly. The runtime will handle the type check.
                var retrievedEntity = firstOrDefault(queryWithIncludes, buildFilterExpression(entityId));
    
                retrievedEntity.Should()
                    .NotBeNull("The entity could not be retrieved after being saved. Check query logic.");
    
                // 7. THE ACTUAL TEST: Invoke MapToDto on the retrieved entity.
                Action mappingAction = () => mapToDtoMethod!.Invoke(instance, [retrievedEntity]);
    
                mappingAction.Should().NotThrow(
                    $"Mapping {tEntity.Name} to {tOutputDto.Name} in {controller.Name} failed. " +
                    "This almost certainly means 'ApplyIncludes' is missing an '.Include()' for a property accessed by 'MapToDto'.");
            }
        }
    }
}