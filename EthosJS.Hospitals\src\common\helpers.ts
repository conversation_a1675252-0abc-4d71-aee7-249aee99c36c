import { EDayWeek } from '@app/common/enums';
import { BadRequestException } from '@nestjs/common';

export const getWeekDay = (day: number): EDayWeek => {
  const date = day % 7;

  switch (date) {
    case 0:
      return EDayWeek.Sunday;
    case 1:
      return EDayWeek.Monday;
    case 2:
      return EDayWeek.Tuesday;
    case 3:
      return EDayWeek.Wednesday;
    case 4:
      return EDayWeek.Thursday;
    case 5:
      return EDayWeek.Friday;
    case 6:
      return EDayWeek.Saturday;
    default:
      throw new BadRequestException('Wrong day index');
  }
};