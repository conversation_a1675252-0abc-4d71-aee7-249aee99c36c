using System.Collections;
using System.Collections.Immutable;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using Ethos.Utilities;
using Json.Path;

namespace Ethos.Workflows.Api;

public interface IInputDto
{
    // public Guid? Id { get; set; }
}

[AttributeUsage(AttributeTargets.Property)]
public class CheckReferenceDataAttribute : Attribute
{
    public Type Name { get; }
    public CheckReferenceDataAttribute(Type name)
    {
        Name = name;
    }
}

[AttributeUsage(AttributeTargets.Property)]
public class CheckStringAttribute : Attribute
{
    public int HardMaxLength { get; }
    public int HardMinLength { get; }
    public string? HardPattern { get; }
    public CheckStringAttribute(int hardMaxLength = Int32.MaxValue, int hardMinLength = 1, string? hardPattern = null)
    {
        HardMaxLength = hardMaxLength;
        HardMinLength = hardMinLength;
        HardPattern = hardPattern;
    }
}

[AttributeUsage(AttributeTargets.Property)]
public class CheckRealNumberAttribute : Attribute
{
    public double HardMin { get; }
    public double HardMax { get; }
    public double SoftMin { get; }
    public double SoftMax { get; }
    public CheckRealNumberAttribute(
        double hardMin = Double.NegativeInfinity, 
        double hardMax = Double.PositiveInfinity, 
        double softMin = Double.NegativeInfinity, 
        double softMax = Double.PositiveInfinity)
    {
        HardMin = hardMin;
        HardMax = hardMax;
        SoftMin = softMin;
        SoftMax = softMax;
    }
}

[AttributeUsage(AttributeTargets.Property | AttributeTargets.Class)]
public class CheckExpressionAttribute : Attribute
{
    public string? Expression { get; set; }
    public CheckExpressionAttribute(string expression)
    {
        Expression = expression;
    }
}

[AttributeUsage(AttributeTargets.Class)]
public class DraftIndexAttribute : Attribute
{
    public string? Path { get; set; }
    public DraftIndexAttribute(string? path = null)
    {
        Path = path;
    }
}

[AttributeUsage(AttributeTargets.Property)]
public class IsRequiredAttribute : Attribute;

public static class DataValidator
{
    public static readonly Guid StringIsTooShort = new Guid("d8e5863b-c5d5-421f-84db-4e19cd10cc72");
    public static readonly Guid StringIsTooLong = new Guid("3547891d-71bb-4481-8cd4-a9cbab14f53d");
    public static readonly Guid StringDoesNotMatchFormat = new Guid("f0b1c8d2-3e4f-4c5a-9b6c-7d8e9f0a1b2c");
    public static readonly Guid RealValueIsTooLow = new Guid("bef9487a-bc79-4277-b771-0de3bd06b64c");
    public static readonly Guid RealValueIsTooHigh = new Guid("ab0e0218-de5c-4d54-839c-2a891ce9ac42");
    public static readonly Guid RealValueIsBelowSoftMin = new Guid("4dc486c6-c50a-4901-b2dc-419702b914a0");
    public static readonly Guid RealValueIsAboveSoftMax = new Guid("e76beb88-d374-4f14-99ad-f4cba095884b");
    public static readonly Guid ReferenceDataValueNotFound = new Guid("c1f8b2d3-4e5f-6a7b-8c9d-e0f1a2b3c4d5");
    public static readonly Guid TypeMismatch = new("4cce339b-38d3-48ad-bacf-f6fbaff5b8fb");


    private static string PascalToCamel(string name) => JsonNamingPolicy.CamelCase.ConvertName(name);

    public static Validated<TData> Validate<TData>(TData data)
    {
        Type type = typeof(TData);
        var result = Validate(type, data);
        return result.Map(d => (TData)d);
    }
    
    public static Validated<JsonObject> ValidateJson<TData>(JsonObject jsonObject, bool allowPartial)
    {
        if (jsonObject is null)
            throw new ArgumentNullException(nameof(jsonObject));

        var (errors, warnings) = ValidateNode(typeof(TData), jsonObject, "", allowPartial);

        if (errors.Count > 0)
        {
            return Validated<JsonObject>.Failure(
                errors.ToImmutableList(),
                warnings.Count > 0 ? warnings.ToImmutableList() : null);
        }

        if (warnings.Count > 0)
        {
            return Validated<JsonObject>.Success(jsonObject, warnings.ToImmutableList());
        }

        return Validated<JsonObject>.Success(jsonObject);
    }
    
    public static Validated<object> Validate(object data)
    {
        if (data is null)
            throw new ArgumentNullException(nameof(data));
        
        var type = data.GetType();
        var result = Validate(type, data);
        return result;
    }
    
    public static Validated<object> Validate(Type type, object data)
    {
        if (data is null)
            throw new ArgumentNullException(nameof(data));
        
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        
        foreach (var prop in type.GetProperties(BindingFlags.Instance | BindingFlags.Public))
        {
            //var attrs = prop.CustomAttributes.Select(a => a.Constructor.DeclaringType?.FullName).ToList();
            //Console.WriteLine($"Validating for {type.Name}.{prop.Name}: {string.Join(", ", attrs)}");
            object? value = prop.GetValue(data);
            var jsonPropName = prop.GetCustomAttribute<JsonPropertyNameAttribute>()?.Name
                ?? PascalToCamel(prop.Name); // Default to camelCase if no attribute
            
            if (value is null)
            {
                var nullability = NlCtx.Create(prop);
                if (nullability.WriteState is NullabilityState.NotNull) // Use WriteState for inputs
                {
                    errors.Add(new Issue(jsonPropName, "Property is required but is null.", TypeMismatch));
                }
                continue; // Skip further validation for this null property
            }

            if (prop.GetCustomAttribute<CheckStringAttribute>() is { } strAttr)
            {
                if (value is string strVal)
                {
                    PerformStringValidation(strVal, strAttr, jsonPropName, errors);
                }
                else
                {
                    // FIX: No longer throws exception, adds a validation error.
                    errors.Add(new Issue(jsonPropName, "Property is marked [CheckString] but is not a string.", TypeMismatch));
                }
            }
            else if (prop.GetCustomAttribute<CheckRealNumberAttribute>() is { } realAttr)
            {
                try
                {
                    double num = Convert.ToDouble(value); // Handles all numeric primitives + decimal
                    PerformRealNumberValidation(num, realAttr, jsonPropName, errors, warnings);
                }
                catch
                {
                    // FIX: No longer throws exception, adds a validation error.
                    errors.Add(new Issue(jsonPropName, "Property is marked [CheckReal] but is not numeric.", TypeMismatch));
                }
            }
            
            
            // // ───────────────────────────────────────────
            // //           REFERENCE-DATA RULES
            // // ───────────────────────────────────────────
            // if (prop.GetCustomAttribute<CheckReferenceDataAttribute>() is { } refAttr &&
            //     value is not null)
            // {
            //     // Convention: reference type exposes a public static IEnumerable<object> All { get; }
            //     PropertyInfo? allProp = refAttr.Name.GetProperty("All",
            //         BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy);
            //
            //     if (allProp?.GetValue(null) is not IEnumerable refValues)
            //     {
            //         ThrowHard($"Reference-data type {refAttr.Name.Name} does not expose static IEnumerable All.");
            //     }
            //     else if (!refValues.Cast<object>().Contains(value))
            //     {
            //         ThrowHard($"{type.Name}.{prop.Name} value “{value}” not found in reference data ({refAttr.Name.Name}).");
            //     }
            // }
            
            // Recurse
            if (value is IList enumerable)
            {
                for (int i = 0; i < enumerable.Count; i++)
                {
                    var item = enumerable[i];
                    if (item is null) continue;

                    var nestedResult = Validate(item.GetType(), item);
                    errors.AddRange(nestedResult.Errors?.Select(e => e.PrependIndex(i)) ?? []);
                    warnings.AddRange(nestedResult.Warnings?.Select(w => w.PrependIndex(i)) ?? []);
                }
            }
            // FIXME: The recursion logic is tightly coupled to the "Ethos" namespace.
            // This should be replaced with a more robust type check.
            else if (value.GetType().Namespace?.StartsWith("Ethos") == true)
            {
                var nestedResult = Validate(value.GetType(), value);
                errors.AddRange(nestedResult.Errors?.Select(e => e.PrependPath(jsonPropName)) ?? []);
                warnings.AddRange(nestedResult.Warnings?.Select(w => w.PrependPath(jsonPropName)) ?? []);
            }
        }

        if (errors.Count > 0)
        {
            return Validated<object>.Failure(
                errors.ToImmutableList(),
                warnings.Count > 0 ? warnings.ToImmutableList() : null);
        }
        
        if (warnings.Count > 0)
        {
            return Validated<object>.Success(data, warnings.ToImmutableList());
        }
        
        return Validated<object>.Success(data);
    }
    
    private static readonly NullabilityInfoContext NlCtx = new();
    
    /// <summary>
    /// The recursive core of the JSON validation logic.
    /// </summary>
    private static (List<Issue> Errors, List<Issue> Warnings) ValidateNode(Type type, JsonObject jsonObject, string pathPrefix, bool allowPartial)
    {
        var errors = new List<Issue>();
        var warnings = new List<Issue>();

        foreach (var prop in type.GetProperties(BindingFlags.Instance | BindingFlags.Public))
        {
            var jsonPropName = prop.GetCustomAttribute<JsonPropertyNameAttribute>()?.Name ?? PascalToCamel(prop.Name);
            var currentPath = string.IsNullOrEmpty(pathPrefix) ? jsonPropName : $"{pathPrefix}.{jsonPropName}";

            if (!jsonObject.TryGetPropertyValue(jsonPropName, out var jsonNode))
            {
                if (allowPartial)
                {
                    // NOTE: We ignore properties that are not present in the JSON object if allowPartial flag is set.
                    //       This is by design.
                    continue;
                }
                else
                {
                    jsonNode = null;
                }
            }
            
            if (jsonNode is null)
            {
                // If the property is not nullable, we should throw an error.
                // For value types, we can check if the property is of type Nullable<T>
                // For reference types, we can use NlCtx
                var nullability = NlCtx.Create(prop);
                if (!nullability.ReadState.HasFlag(NullabilityState.Nullable))
                {
                    errors.Add(new Issue(currentPath, "Property is required but is null.", TypeMismatch));
                    continue; // Stop further validation for this property
                }
            }

            // ───────────────────────────────────────────
            //               STRING RULES
            // ───────────────────────────────────────────
            if (prop.GetCustomAttribute<CheckStringAttribute>() is { } strAttr && jsonNode is not null)
            {
                if (jsonNode.TryGetValue<string>(out var strVal))
                {
                    PerformStringValidation(strVal, strAttr, currentPath, errors);
                }
                else
                {
                    errors.Add(new Issue(currentPath, "Property is not a valid string.", TypeMismatch));
                }
            }

            // ───────────────────────────────────────────
            //               REAL RULES
            // ───────────────────────────────────────────
            else if (prop.GetCustomAttribute<CheckRealNumberAttribute>() is { } realAttr && jsonNode is not null)
            {
                if (jsonNode.TryGetValue<double>(out var num))
                {
                    PerformRealNumberValidation(num, realAttr, currentPath, errors, warnings);
                }
                else
                {
                    errors.Add(new Issue(currentPath, "Property is not a valid number.", TypeMismatch));
                }
            }

            // ───────────────────────────────────────────
            //                RECURSION
            // ───────────────────────────────────────────
            else if (jsonNode is JsonArray jsonArray)
            {
                Type? itemType = GetEnumerableItemType(prop.PropertyType);
                if (itemType is null) continue;

                var arrStrAttr = prop.GetCustomAttribute<CheckStringAttribute>();
                var arrRealAttr = prop.GetCustomAttribute<CheckRealNumberAttribute>();

                for (int i = 0; i < jsonArray.Count; i++)
                {
                    var itemNode = jsonArray[i];
                    var itemPath = $"{currentPath}[{i}]";

                    if (itemNode is null) continue;

                    // Case 1: Array of complex objects
                    if (itemNode is JsonObject itemObject)
                    {
                        var (nestedErrors, nestedWarnings) = ValidateNode(itemType, itemObject, itemPath, allowPartial);
                        errors.AddRange(nestedErrors);
                        warnings.AddRange(nestedWarnings);
                    }
                    // Case 2: Array of primitives that need validation
                    else if (itemNode is JsonValue itemValue)
                    {
                        if (arrStrAttr is not null)
                        {
                            if (itemValue.TryGetValue<string>(out var strVal))
                                PerformStringValidation(strVal, arrStrAttr, itemPath, errors);
                            else
                                errors.Add(new Issue(itemPath, "Item is not a valid string.", TypeMismatch));
                        }
                        if (arrRealAttr is not null)
                        {
                            if (itemValue.TryGetValue<double>(out var numVal))
                                PerformRealNumberValidation(numVal, arrRealAttr, itemPath, errors, warnings);
                            else
                                errors.Add(new Issue(itemPath, "Item is not a valid number.", TypeMismatch));
                        }
                    }
                }
            }
            // FIXME: The recursion logic is tightly coupled to the "Ethos" namespace.
            else if (jsonNode is JsonObject nestedObject && prop.PropertyType.Namespace?.StartsWith("Ethos") == true)
            {
                var (nestedErrors, nestedWarnings) = ValidateNode(prop.PropertyType, nestedObject, currentPath, allowPartial);
                errors.AddRange(nestedErrors);
                warnings.AddRange(nestedWarnings);
            }
        }
        return (errors, warnings);
    }
    
    // NEW: Centralized validation logic helpers
    #region Validation Logic Helpers

    private static void PerformStringValidation(string strVal, CheckStringAttribute strAttr, string path, List<Issue> errors)
    {
        if (strVal.Length > strAttr.HardMaxLength)
            errors.Add(new Issue(path, $"String length exceeds hard max length {strAttr.HardMaxLength} (actual {strVal.Length}).", StringIsTooLong, new JsonObject { ["hardMaxLength"] = strAttr.HardMaxLength, ["actualLength"] = strVal.Length }));
        if (strVal.Length < strAttr.HardMinLength)
            errors.Add(new Issue(path, $"String length below hard min length {strAttr.HardMinLength} (actual {strVal.Length}).", StringIsTooShort, new JsonObject { ["hardMinLength"] = strAttr.HardMinLength, ["actualLength"] = strVal.Length }));
        if (!string.IsNullOrEmpty(strAttr.HardPattern) && !Regex.IsMatch(strVal, strAttr.HardPattern))
            errors.Add(new Issue(path, $"String does not match hard pattern '{strAttr.HardPattern}'.", StringDoesNotMatchFormat, new JsonObject { ["hardPattern"] = strAttr.HardPattern, ["actualValue"] = strVal }));
    }

    private static void PerformRealNumberValidation(double num, CheckRealNumberAttribute realAttr, string path, List<Issue> errors, List<Issue> warnings)
    {
        if (num < realAttr.HardMin)
            errors.Add(new Issue(path, $"Real value {num} below hard min {realAttr.HardMin}.", RealValueIsTooLow, new JsonObject { ["hardMin"] = realAttr.HardMin, ["actualValue"] = num }));
        if (num > realAttr.HardMax)
            errors.Add(new Issue(path, $"Real value {num} above hard max {realAttr.HardMax}.", RealValueIsTooHigh, new JsonObject { ["hardMax"] = realAttr.HardMax, ["actualValue"] = num }));
        if (num < realAttr.SoftMin)
            warnings.Add(new Issue(path, $"Real value {num} below soft min {realAttr.SoftMin}.", RealValueIsBelowSoftMin, new JsonObject { ["softMin"] = realAttr.SoftMin, ["actualValue"] = num }));
        if (num > realAttr.SoftMax)
            warnings.Add(new Issue(path, $"Real value {num} above soft max {realAttr.SoftMax}.", RealValueIsAboveSoftMax, new JsonObject { ["softMax"] = realAttr.SoftMax, ["actualValue"] = num }));
    }

    #endregion
    
    private static Type? GetEnumerableItemType(Type type)
    {
        if (type.IsGenericType && typeof(IEnumerable).IsAssignableFrom(type))
        {
            return type.GetGenericArguments()[0];
        }
        return type.GetInterfaces()
            .Where(t => t.IsGenericType && t.GetGenericTypeDefinition() == typeof(IEnumerable<>))
            .Select(t => t.GetGenericArguments()[0])
            .FirstOrDefault();
    }
}