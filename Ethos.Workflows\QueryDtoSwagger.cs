using Ethos.Model;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Ethos.Workflows;

public sealed class QueryDtoSchemaFilter : ISchemaFilter
{
    private static T GetOrAdd<TKey, T>(
        IDictionary<TKey, T> dict, TKey key, Func<T> valueFactory)
    {
        if (!dict.TryGetValue(key, out var value))
        {
            value = valueFactory();
            dict[key] = value;
        }
        return value;
    }
    
    private static OpenApiSchema GetOrGenerate(
        Type t,
        SchemaFilterContext ctx)
    {
        return ctx.SchemaRepository.TryLookupByType(t, out var schema)
            ? schema
            : ctx.SchemaGenerator.GenerateSchema(t, ctx.SchemaRepository);
    }
    
    // private static OpenApiSchema Ref(Type t, SchemaFilterContext ctx)
    // {
    //     var id = ctx.SchemaGenerator.SchemaIdSelector(t);          // official helper
    //     ctx.SchemaRepository.RegisterType(t, id);                  // just reserve
    //     return new OpenApiSchema
    //     {
    //         Reference = new OpenApiReference { Type = ReferenceType.Schema, Id = id }
    //     };
    // }
    
    public void Apply(OpenApiSchema schema, SchemaFilterContext ctx)
    {
        if (!ctx.Type.IsGenericType ||
            ctx.Type.GetGenericTypeDefinition() != typeof(QueryDto<>)) return;
        
        if (ctx.SchemaRepository.TryLookupByType(ctx.Type, out var already))
        {
            // we’re being re-entered – just point the caller at the ref and bail out
            schema.Reference = already.Reference;
            return;
        }

        schema.Type = "object";
        schema.Required.Add("$type");
        schema.Properties["$type"] = new OpenApiSchema
        {
            Type = "string",
            Enum = new[]
            {
                new OpenApiString("All"), new OpenApiString("Literal"),
                new OpenApiString("Not"), new OpenApiString("And"),
                new OpenApiString("Or")
            }
        };

        // Build (or reference) concrete schemas for each variant
        var repo = ctx.SchemaRepository;
        var prim  = ctx.Type.GetGenericArguments()[0];

        OpenApiSchema Variant(Type t) => GetOrGenerate(t, ctx);

        schema.OneOf = new List<OpenApiSchema>()
        {
            Variant(typeof(QueryDto<>.All).MakeGenericType(prim)),
            Variant(typeof(QueryDto<>.Literal).MakeGenericType(prim)),
            Variant(typeof(QueryDto<>.Not).MakeGenericType(prim)),
            Variant(typeof(QueryDto<>.And).MakeGenericType(prim)),
            Variant(typeof(QueryDto<>.Or).MakeGenericType(prim))
        };

        schema.Discriminator = new OpenApiDiscriminator
        {
            PropertyName = "$type",
            Mapping = schema.OneOf
                .ToDictionary(
                    s => s.Reference.Id.Replace("QueryDtoOf", "").Replace("Dto", ""),
                    s => $"#/components/schemas/{s.Reference.Id}")
        };
    }
}