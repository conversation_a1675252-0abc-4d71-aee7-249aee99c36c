using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.AspNetCore.Mvc;

using Ethos.Workflows.Database;
using Ethos.Workflows.Audit;
using Ethos.Workflows.Api;
using Ethos.Workflows.Notifications;
using Ethos.Model.Types;
using Ethos.Utilities;
using Ethos.Workflows.Workflow;

namespace Ethos.Workflows.Workflow.AddNewPatient;

[JsonPolymorphic(TypeDiscriminatorPropertyName = "$type")]
[JsonDerivedType(typeof(Committed), "Committed")]
[JsonDerivedType(typeof(AddedBasicInformation), "AddedBasicInformation")]
[JsonDerivedType(typeof(AddedContacts), "AddedContacts")]
[JsonDerivedType(typeof(AddedAddresses), "AddedAddresses")]
[JsonDerivedType(typeof(AddedInsurances), "AddedInsurances")]
[JsonDerivedType(typeof(AddedGuardians), "AddedGuardians")]
public abstract record AddNewPatientState : IWorkflowState
{
    public static WorkflowName<AddNewPatientState> Key = new("AddNewPatient");
    private AddNewPatientState() { }

    public sealed record Committed(Guid PatientId) : AddNewPatientState;
    public sealed record AddedBasicInformation(PatientInformation PatientInformation, Demographics Demographics, PhysicalMeasurements PhysicalMeasurements) : AddNewPatientState;
    public sealed record AddedContacts(PatientInformation PatientInformation, Demographics Demographics, PhysicalMeasurements PhysicalMeasurements, ContactInformation ContactInformation) : AddNewPatientState;
    public sealed record AddedAddresses(PatientInformation PatientInformation, Demographics Demographics, PhysicalMeasurements PhysicalMeasurements, ContactInformation ContactInformation, IReadOnlyList<AddressWithUseType> PhysicalAddresses, IReadOnlyList<AddressWithUseType> BillingAddresses, IReadOnlyList<AddressWithUseType> DeliveryAddresses) : AddNewPatientState;
    public sealed record AddedInsurances(PatientInformation PatientInformation, Demographics Demographics, PhysicalMeasurements PhysicalMeasurements, ContactInformation ContactInformation, IReadOnlyList<AddressWithUseType> PhysicalAddresses, IReadOnlyList<AddressWithUseType> BillingAddresses, IReadOnlyList<AddressWithUseType> DeliveryAddresses, IReadOnlyList<Insurance> Insurances) : AddNewPatientState;
    public sealed record AddedGuardians(PatientInformation PatientInformation, Demographics Demographics, PhysicalMeasurements PhysicalMeasurements, ContactInformation ContactInformation, IReadOnlyList<AddressWithUseType> PhysicalAddresses, IReadOnlyList<AddressWithUseType> BillingAddresses, IReadOnlyList<AddressWithUseType> DeliveryAddresses, IReadOnlyList<Insurance> Insurances, IReadOnlyList<Guardian> Guardians) : AddNewPatientState;
}


public interface IAddNewPatientRequest : IWorkflowTransition
{
    public sealed record AddBasicInformation(PatientInformation PatientInformation,
    Demographics Demographics,
    PhysicalMeasurements PhysicalMeasurements) : IAddNewPatientRequest;
    public sealed record AddContacts(ContactInformation ContactInformation) : IAddNewPatientRequest;
    public sealed record AddAddresses(IReadOnlyList<AddressWithUseType> PhysicalAddresses,
    IReadOnlyList<AddressWithUseType> BillingAddresses,
    IReadOnlyList<AddressWithUseType> DeliveryAddresses) : IAddNewPatientRequest;
    public sealed record AddInsurances(IReadOnlyList<Insurance> Insurances) : IAddNewPatientRequest;
    public sealed record AddGuardians(IReadOnlyList<Guardian> Guardians) : IAddNewPatientRequest;
    public sealed record AddClinicalInformation(IReadOnlyList<int> ClinicalConsiderations,
    SchedulingPreferences SchedulingPreferences,
    string? AdditionalPatientNotes,
    string? CaregiverInformation) : IAddNewPatientRequest;
}


public sealed record StartAddNewPatientRequest();
public sealed record StartAddNewPatientResponse(Guid WorkflowId, Guid PatientId);


public interface IAddNewPatientFlow : IAbstractFlow
{
    Task<AddNewPatientState.AddedBasicInformation> AddBasicInformation(FlowContext ctx, PatientInformation patientInformation, Demographics demographics, PhysicalMeasurements physicalMeasurements)
    {
        return Task.FromResult<AddNewPatientState.AddedBasicInformation>(new AddNewPatientState.AddedBasicInformation(
            PatientInformation : patientInformation, 
            Demographics : demographics, 
            PhysicalMeasurements : physicalMeasurements
        ));
    }
    Task<AddNewPatientState.AddedContacts> AddContacts(FlowContext<AddNewPatientState.AddedBasicInformation> ctx, ContactInformation contactInformation)
    {
        return Task.FromResult<AddNewPatientState.AddedContacts>(new AddNewPatientState.AddedContacts(
            PatientInformation : ctx.OldState.PatientInformation, 
            Demographics : ctx.OldState.Demographics, 
            PhysicalMeasurements : ctx.OldState.PhysicalMeasurements, 
            ContactInformation : contactInformation
        ));
    }
    Task<AddNewPatientState.AddedAddresses> AddAddresses(FlowContext<AddNewPatientState.AddedContacts> ctx, IReadOnlyList<AddressWithUseType> physicalAddresses, IReadOnlyList<AddressWithUseType> billingAddresses, IReadOnlyList<AddressWithUseType> deliveryAddresses)
    {
        return Task.FromResult<AddNewPatientState.AddedAddresses>(new AddNewPatientState.AddedAddresses(
            PatientInformation : ctx.OldState.PatientInformation, 
            Demographics : ctx.OldState.Demographics, 
            PhysicalMeasurements : ctx.OldState.PhysicalMeasurements, 
            ContactInformation : ctx.OldState.ContactInformation, 
            PhysicalAddresses : physicalAddresses, 
            BillingAddresses : billingAddresses, 
            DeliveryAddresses : deliveryAddresses
        ));
    }
    Task<AddNewPatientState.AddedInsurances> AddInsurances(FlowContext<AddNewPatientState.AddedAddresses> ctx, IReadOnlyList<Insurance> insurances)
    {
        return Task.FromResult<AddNewPatientState.AddedInsurances>(new AddNewPatientState.AddedInsurances(
            PatientInformation : ctx.OldState.PatientInformation, 
            Demographics : ctx.OldState.Demographics, 
            PhysicalMeasurements : ctx.OldState.PhysicalMeasurements, 
            ContactInformation : ctx.OldState.ContactInformation, 
            PhysicalAddresses : ctx.OldState.PhysicalAddresses, 
            BillingAddresses : ctx.OldState.BillingAddresses, 
            DeliveryAddresses : ctx.OldState.DeliveryAddresses, 
            Insurances : insurances
        ));
    }
    Task<AddNewPatientState.AddedGuardians> AddGuardians(FlowContext<AddNewPatientState.AddedInsurances> ctx, IReadOnlyList<Guardian> guardians)
    {
        return Task.FromResult<AddNewPatientState.AddedGuardians>(new AddNewPatientState.AddedGuardians(
            PatientInformation : ctx.OldState.PatientInformation, 
            Demographics : ctx.OldState.Demographics, 
            PhysicalMeasurements : ctx.OldState.PhysicalMeasurements, 
            ContactInformation : ctx.OldState.ContactInformation, 
            PhysicalAddresses : ctx.OldState.PhysicalAddresses, 
            BillingAddresses : ctx.OldState.BillingAddresses, 
            DeliveryAddresses : ctx.OldState.DeliveryAddresses, 
            Insurances : ctx.OldState.Insurances, 
            Guardians : guardians
        ));
    }
    Task<AddNewPatientState.Committed> AddClinicalInformation(FlowContext<AddNewPatientState.AddedGuardians> ctx, IReadOnlyList<int> clinicalConsiderations, SchedulingPreferences schedulingPreferences, string? additionalPatientNotes, string? caregiverInformation);
}


[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AddNewPatientController : ControllerBase
{
    private readonly AppDbContext _dbContext;
    private readonly IWorkflowEngine _engine;
    private readonly IAuditService _auditService;
    private readonly IAddNewPatientFlow _flow;
    private readonly INotificationService _notificationService;
    private readonly ILogger<AddNewPatientController> _logger;

    public AddNewPatientController(AppDbContext dbContext, IWorkflowEngine engine, INotificationService _notificationService, IAuditService auditService, ILogger<AddNewPatientController> logger, IAddNewPatientFlow flow)
    {
        _dbContext = dbContext;
        _engine = engine;
        _notificationService = _notificationService;
        _auditService = auditService;
        _logger = logger;
        _flow = flow;
    }

    [HttpGet("validation-rules")]
    public Dictionary<string, List<ValidationRule>> GetValidationRules()
    {
        var rules = new Dictionary<string, List<ValidationRule>>();
        // NonEmptyString
        var nonEmptyStringRules = new List<ValidationRule>();
        rules.Add("NonEmptyString", nonEmptyStringRules);
        nonEmptyStringRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        // SSN
        var sSNRules = new List<ValidationRule>();
        rules.Add("SSN", sSNRules);
        sSNRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        sSNRules.Add(new ValidationRule("InvalidSSN", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[0-9]{3}-[0-9]{2}-[0-9]{4}$")]), "Invalid SSN format"));
        // SomeMRN
        var someMRNRules = new List<ValidationRule>();
        rules.Add("SomeMRN", someMRNRules);
        someMRNRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        someMRNRules.Add(new ValidationRule("InvalidMRN", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z0-9]{1,20}$")]), "Invalid MRN format"));
        // Email
        var emailRules = new List<ValidationRule>();
        rules.Add("Email", emailRules);
        emailRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        emailRules.Add(new ValidationRule("Email", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")]), "Invalid email address"));
        // BirthDate
        var birthDateRules = new List<ValidationRule>();
        rules.Add("BirthDate", birthDateRules);
        birthDateRules.Add(new ValidationRule("DateOfBirthMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.Call("age", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(150)), "Date of birth is unrealistic"));
        birthDateRules.Add(new ValidationRule("DateOfBirthIsInTheFuture", new ValidationExpr.BinOp(new ValidationExpr.Variable("value"), "<", new ValidationExpr.Call("nowdate", [])), "Date of birth is in the future"));
        // NamePart
        var namePartRules = new List<ValidationRule>();
        rules.Add("NamePart", namePartRules);
        namePartRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        namePartRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(50)), "Name part is too long"));
        namePartRules.Add(new ValidationRule("TooShort", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">=", new ValidationExpr.IntegerLiteral(2)), "Name part is too short"));
        namePartRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z-]+$")]), "Name part contains non-latin characters"));
        // CityName
        var cityNameRules = new List<ValidationRule>();
        rules.Add("CityName", cityNameRules);
        cityNameRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        cityNameRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(50)), "City name is too long"));
        cityNameRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z-]+$")]), "City name contains non-latin characters"));
        // PostalCode
        var postalCodeRules = new List<ValidationRule>();
        rules.Add("PostalCode", postalCodeRules);
        postalCodeRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        postalCodeRules.Add(new ValidationRule("TooShort", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">=", new ValidationExpr.IntegerLiteral(3)), "Postal code is too short"));
        postalCodeRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(16)), "Postal code is too long"));
        postalCodeRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z0-9- ]+$")]), "Postal code contains non-latin characters"));
        // Address
        var addressRules = new List<ValidationRule>();
        rules.Add("Address", addressRules);
        addressRules.Add(new ValidationRule("NoAddressLine", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("line1")]), ">", new ValidationExpr.IntegerLiteral(0)), "At least one address line is required."));
        // PhoneNumber
        var phoneNumberRules = new List<ValidationRule>();
        rules.Add("PhoneNumber", phoneNumberRules);
        phoneNumberRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        phoneNumberRules.Add(new ValidationRule("InvalidPhoneNumber", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[0-9]{3}-[0-9]{3}-[0-9]{4}$")]), "Invalid phone number format"));
        // TimeOfDay
        var timeOfDayRules = new List<ValidationRule>();
        rules.Add("TimeOfDay", timeOfDayRules);
        timeOfDayRules.Add(new ValidationRule("InvalidHour", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("hour"), ">=", new ValidationExpr.IntegerLiteral(0)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("hour"), "<", new ValidationExpr.IntegerLiteral(24))), "Hour must be between 0 and 23"));
        timeOfDayRules.Add(new ValidationRule("InvalidMinute", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("minute"), ">=", new ValidationExpr.IntegerLiteral(0)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("minute"), "<", new ValidationExpr.IntegerLiteral(60))), "Minute must be between 0 and 59"));
        // NamePartOrInitial
        var namePartOrInitialRules = new List<ValidationRule>();
        rules.Add("NamePartOrInitial", namePartOrInitialRules);
        namePartOrInitialRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        namePartOrInitialRules.Add(new ValidationRule("TooLong", new ValidationExpr.BinOp(new ValidationExpr.Call("len", [new ValidationExpr.Variable("value")]), "<=", new ValidationExpr.IntegerLiteral(50)), "Name part is too long"));
        namePartOrInitialRules.Add(new ValidationRule("ContainsNonLatinCharacters", new ValidationExpr.Call("matches", [new ValidationExpr.Variable("value"), new ValidationExpr.StringLiteral("^[a-zA-Z-]+$")]), "Name part contains non-latin characters"));
        // EmergencyContactRelationship
        var emergencyContactRelationshipRules = new List<ValidationRule>();
        rules.Add("EmergencyContactRelationship", emergencyContactRelationshipRules);
        emergencyContactRelationshipRules.Add(new ValidationRule("ValueIsEmpty", new ValidationExpr.BinOp(new ValidationExpr.Variable("value"), ">", new ValidationExpr.IntegerLiteral(0)), "Value is required"));
        // PhysicalMeasurements
        var physicalMeasurementsRules = new List<ValidationRule>();
        rules.Add("PhysicalMeasurements", physicalMeasurementsRules);
        physicalMeasurementsRules.Add(new ValidationRule("HeightIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("heightInches"), ">", new ValidationExpr.IntegerLiteral(0)), "Height must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("HeightMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.Variable("heightInches"), "<", new ValidationExpr.IntegerLiteral(200)), "Height should be less than 200 inches"));
        physicalMeasurementsRules.Add(new ValidationRule("WeightIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("weightPounds"), ">", new ValidationExpr.IntegerLiteral(0)), "Weight must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("WeightMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.Variable("weightPounds"), "<", new ValidationExpr.IntegerLiteral(1000)), "Weight should be less than 1000 lbs"));
        physicalMeasurementsRules.Add(new ValidationRule("NeckSizeIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("neckSize"), ">", new ValidationExpr.IntegerLiteral(0)), "Neck size must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("NeckSizeMightBeInvalid", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("neckSize"), ">", new ValidationExpr.IntegerLiteral(5)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("neckSize"), "<", new ValidationExpr.IntegerLiteral(50))), "Neck size should be between 5 and 50 inches"));
        physicalMeasurementsRules.Add(new ValidationRule("BMIIsInvalid", new ValidationExpr.BinOp(new ValidationExpr.Variable("bmi"), ">", new ValidationExpr.IntegerLiteral(0)), "BMI must be greater than 0"));
        physicalMeasurementsRules.Add(new ValidationRule("BMIMightBeUnrealistic", new ValidationExpr.BinOp(new ValidationExpr.BinOp(new ValidationExpr.Variable("bmi"), ">", new ValidationExpr.IntegerLiteral(10)), "&&", new ValidationExpr.BinOp(new ValidationExpr.Variable("bmi"), "<", new ValidationExpr.IntegerLiteral(100))), "BMI should be between 10 and 100"));
        return rules;
    }

    [HttpGet("state/{id}")]
    public async Task<ActionResult<AddNewPatientState>> Get(Guid id)
    {
        var result = await _engine.GetInstanceAsync<AddNewPatientState>(AddNewPatientState.Key, id);
        return result == null ? NotFound() : Ok(result);
    }

    [HttpPost("start")]
    public async Task<ActionResult<StartAddNewPatientResponse>> StartNewFlow([FromBody] StartAddNewPatientRequest request)
    {
        var resultId = Guid.NewGuid();
        var workflowId = await _engine.StartNewWorkflowAsync<AddNewPatientState>(AddNewPatientState.Key, new Dictionary<string, Guid> { ["Patient"] = resultId });
        await _dbContext.SaveChangesAsync();
        return Ok(new StartAddNewPatientResponse(workflowId, resultId));
    }

    [HttpPost("rewind")]
    public async Task<ActionResult> RewindFlow(Guid id, string stateName)
    {
        await _engine.RewindState<AddNewPatientState>(AddNewPatientState.Key, id, stateName);
        await _dbContext.SaveChangesAsync();
        return Ok();
    }

    [HttpPost("list")]
    public async Task<ActionResult<IReadOnlyList<Guid>>> ListInstances(IReadOnlyDictionary<string, Guid>? entityLinks = null)
    {
        return Ok(await _engine.GetInstancesAsync(AddNewPatientState.Key, entityLinks));
    }

    [HttpPost("add-basic-information")]
    public async Task<ActionResult<WorkflowResponse<AddNewPatientState.AddedBasicInformation?>>> AddBasicInformation(WorkflowRequest<IAddNewPatientRequest.AddBasicInformation> request)
    {
        using (_logger.BeginScope(new {Transition = "AddBasicInformation"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewPatientState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewPatientState.AddedBasicInformation newState;
                try
                {
                    var ctx = new FlowContext(instanceId, rawOldState.EntityLinks);
                    newState = await _flow.AddBasicInformation(ctx, request.InputData.PatientInformation, request.InputData.Demographics, request.InputData.PhysicalMeasurements);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    throw;
                }
                var endInstanceId = await _engine.CreateOrUpdateInstanceAsync<AddNewPatientState>(AddNewPatientState.Key, instanceId, new WorkflowTransitionName<object>("AddBasicInformation"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewPatientState>("AddedBasicInformation"), newState);

                await _auditService.LogAsync(endInstanceId, "None", "AddNewPatientState.AddedBasicInformation", username, "AddBasicInformation", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewPatientState.AddedBasicInformation>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-basic-information/draft")]
    public async Task<ActionResult<Guid>> AddBasicInformationDraft(DraftRequest<IAddNewPatientRequest.AddBasicInformation> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewPatientState.Key, request.InstanceId, "AddBasicInformation", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-basic-information/validate")]
    public async Task<ActionResult<ValidationResult>> AddBasicInformationDraft(WorkflowRequest<IAddNewPatientRequest.AddBasicInformation> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        if (request.InputData.PatientInformation.Prefix != null)
        {
        }
        if (!(request.InputData.PatientInformation.FirstName.Length > 0))
        {
            errors.Add(new Issue("", "Value is required"));
        }
        if (!(request.InputData.PatientInformation.FirstName.Length <= 50))
        {
            warnings.Add(new Issue("", "Name part is too long"));
        }
        if (!(request.InputData.PatientInformation.FirstName.Length >= 2))
        {
            warnings.Add(new Issue("", "Name part is too short"));
        }
        if (!(Regex.IsMatch(request.InputData.PatientInformation.FirstName, "^[a-zA-Z-]+$")))
        {
            warnings.Add(new Issue("", "Name part contains non-latin characters"));
        }
        if (request.InputData.PatientInformation.MiddleName != null)
        {
            if (!(request.InputData.PatientInformation.MiddleName!.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(request.InputData.PatientInformation.MiddleName!.Length <= 50))
            {
                warnings.Add(new Issue("", "Name part is too long"));
            }
            if (!(Regex.IsMatch(request.InputData.PatientInformation.MiddleName!, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "Name part contains non-latin characters"));
            }
        }
        if (!(request.InputData.PatientInformation.LastName.Length > 0))
        {
            errors.Add(new Issue("", "Value is required"));
        }
        if (!(request.InputData.PatientInformation.LastName.Length <= 50))
        {
            warnings.Add(new Issue("", "Name part is too long"));
        }
        if (!(request.InputData.PatientInformation.LastName.Length >= 2))
        {
            warnings.Add(new Issue("", "Name part is too short"));
        }
        if (!(Regex.IsMatch(request.InputData.PatientInformation.LastName, "^[a-zA-Z-]+$")))
        {
            warnings.Add(new Issue("", "Name part contains non-latin characters"));
        }
        if (request.InputData.PatientInformation.Suffix != null)
        {
        }
        if (!(request.InputData.PatientInformation.Ssn.Length > 0))
        {
            errors.Add(new Issue("", "Value is required"));
        }
        if (!(Regex.IsMatch(request.InputData.PatientInformation.Ssn, "^[0-9]{3}-[0-9]{2}-[0-9]{4}$")))
        {
            errors.Add(new Issue("", "Invalid SSN format"));
        }
        if (request.InputData.PatientInformation.Mrn != null)
        {
            if (!(request.InputData.PatientInformation.Mrn!.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(Regex.IsMatch(request.InputData.PatientInformation.Mrn!, "^[a-zA-Z0-9]{1,20}$")))
            {
                errors.Add(new Issue("", "Invalid MRN format"));
            }
        }
        if (!((DateTime.Now - request.InputData.Demographics.DateOfBirth.ToDateTime(TimeOnly.Parse("12:00"))).Days / 365 <= 150))
        {
            warnings.Add(new Issue("", "Date of birth is unrealistic"));
        }
        if (!(request.InputData.Demographics.DateOfBirth < DateOnly.FromDateTime(DateTime.Now)))
        {
            errors.Add(new Issue("", "Date of birth is in the future"));
        }
        if (!(request.InputData.PhysicalMeasurements.HeightInches > 0))
        {
            errors.Add(new Issue("", "Height must be greater than 0"));
        }
        if (!(request.InputData.PhysicalMeasurements.HeightInches < 200))
        {
            warnings.Add(new Issue("", "Height should be less than 200 inches"));
        }
        if (!(request.InputData.PhysicalMeasurements.WeightPounds > 0))
        {
            errors.Add(new Issue("", "Weight must be greater than 0"));
        }
        if (!(request.InputData.PhysicalMeasurements.WeightPounds < 1000))
        {
            warnings.Add(new Issue("", "Weight should be less than 1000 lbs"));
        }
        if (!(request.InputData.PhysicalMeasurements.NeckSize > 0))
        {
            errors.Add(new Issue("", "Neck size must be greater than 0"));
        }
        if (!(request.InputData.PhysicalMeasurements.NeckSize > 5 && request.InputData.PhysicalMeasurements.NeckSize < 50))
        {
            warnings.Add(new Issue("", "Neck size should be between 5 and 50 inches"));
        }
        if (!(request.InputData.PhysicalMeasurements.Bmi > 0))
        {
            errors.Add(new Issue("", "BMI must be greater than 0"));
        }
        if (!(request.InputData.PhysicalMeasurements.Bmi > 10 && request.InputData.PhysicalMeasurements.Bmi < 100))
        {
            warnings.Add(new Issue("", "BMI should be between 10 and 100"));
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("add-contacts")]
    public async Task<ActionResult<WorkflowResponse<AddNewPatientState.AddedContacts?>>> AddContacts(WorkflowRequest<IAddNewPatientRequest.AddContacts> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "AddContacts"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewPatientState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewPatientState.AddedBasicInformation) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewPatientState.AddedBasicInformation) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewPatientState.AddedContacts newState;
                try
                {
                    var ctx = new FlowContext<AddNewPatientState.AddedBasicInformation>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.AddContacts(ctx, request.InputData.ContactInformation);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewPatientState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewPatientState>(AddNewPatientState.Key, instanceId, new WorkflowTransitionName<object>("AddContacts"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewPatientState>("AddedContacts"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewPatientState.AddedBasicInformation", "AddNewPatientState.AddedContacts", username, "AddContacts", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewPatientState.AddedContacts>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-contacts/draft")]
    public async Task<ActionResult<Guid>> AddContactsDraft(DraftRequest<IAddNewPatientRequest.AddContacts> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewPatientState.Key, request.InstanceId, "AddContacts", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-contacts/validate")]
    public async Task<ActionResult<ValidationResult>> AddContactsDraft(WorkflowRequest<IAddNewPatientRequest.AddContacts> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        foreach (var item1 in request.InputData.ContactInformation.PhoneNumbers)
        {
            if (!(item1.Value.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(Regex.IsMatch(item1.Value, "^[0-9]{3}-[0-9]{3}-[0-9]{4}$")))
            {
                errors.Add(new Issue("", "Invalid phone number format"));
            }
        }
        foreach (var item2 in request.InputData.ContactInformation.Emails)
        {
            if (!(item2.Value.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(Regex.IsMatch(item2.Value, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")))
            {
                errors.Add(new Issue("", "Invalid email address"));
            }
        }
        foreach (var item3 in request.InputData.ContactInformation.EmergencyContacts)
        {
            if (item3.Prefix != null)
            {
            }
            if (!(item3.FirstName.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item3.FirstName.Length <= 50))
            {
                warnings.Add(new Issue("", "Name part is too long"));
            }
            if (!(item3.FirstName.Length >= 2))
            {
                warnings.Add(new Issue("", "Name part is too short"));
            }
            if (!(Regex.IsMatch(item3.FirstName, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "Name part contains non-latin characters"));
            }
            if (item3.MiddleName != null)
            {
                if (!(item3.MiddleName!.Length > 0))
                {
                    errors.Add(new Issue("", "Value is required"));
                }
                if (!(item3.MiddleName!.Length <= 50))
                {
                    warnings.Add(new Issue("", "Name part is too long"));
                }
                if (!(Regex.IsMatch(item3.MiddleName!, "^[a-zA-Z-]+$")))
                {
                    warnings.Add(new Issue("", "Name part contains non-latin characters"));
                }
            }
            if (!(item3.LastName.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item3.LastName.Length <= 50))
            {
                warnings.Add(new Issue("", "Name part is too long"));
            }
            if (!(item3.LastName.Length >= 2))
            {
                warnings.Add(new Issue("", "Name part is too short"));
            }
            if (!(Regex.IsMatch(item3.LastName, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "Name part contains non-latin characters"));
            }
            if (item3.Suffix != null)
            {
            }
            if (!(item3.Relationship > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item3.ContactInformation.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(Regex.IsMatch(item3.ContactInformation, "^[0-9]{3}-[0-9]{3}-[0-9]{4}$")))
            {
                errors.Add(new Issue("", "Invalid phone number format"));
            }
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("add-addresses")]
    public async Task<ActionResult<WorkflowResponse<AddNewPatientState.AddedAddresses?>>> AddAddresses(WorkflowRequest<IAddNewPatientRequest.AddAddresses> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "AddAddresses"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewPatientState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewPatientState.AddedContacts) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewPatientState.AddedContacts) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewPatientState.AddedAddresses newState;
                try
                {
                    var ctx = new FlowContext<AddNewPatientState.AddedContacts>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.AddAddresses(ctx, request.InputData.PhysicalAddresses, request.InputData.BillingAddresses, request.InputData.DeliveryAddresses);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewPatientState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewPatientState>(AddNewPatientState.Key, instanceId, new WorkflowTransitionName<object>("AddAddresses"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewPatientState>("AddedAddresses"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewPatientState.AddedContacts", "AddNewPatientState.AddedAddresses", username, "AddAddresses", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewPatientState.AddedAddresses>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-addresses/draft")]
    public async Task<ActionResult<Guid>> AddAddressesDraft(DraftRequest<IAddNewPatientRequest.AddAddresses> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewPatientState.Key, request.InstanceId, "AddAddresses", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-addresses/validate")]
    public async Task<ActionResult<ValidationResult>> AddAddressesDraft(WorkflowRequest<IAddNewPatientRequest.AddAddresses> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        foreach (var item1 in request.InputData.PhysicalAddresses)
        {
            if (!(item1.Address.Line1.Length > 0))
            {
                errors.Add(new Issue("", "At least one address line is required."));
            }
            if (item1.Address.Line2 != null)
            {
            }
            if (!(item1.Address.City.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item1.Address.City.Length <= 50))
            {
                warnings.Add(new Issue("", "City name is too long"));
            }
            if (!(Regex.IsMatch(item1.Address.City, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "City name contains non-latin characters"));
            }
            if (!(item1.Address.PostalCode.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item1.Address.PostalCode.Length >= 3))
            {
                warnings.Add(new Issue("", "Postal code is too short"));
            }
            if (!(item1.Address.PostalCode.Length <= 16))
            {
                warnings.Add(new Issue("", "Postal code is too long"));
            }
            if (!(Regex.IsMatch(item1.Address.PostalCode, "^[a-zA-Z0-9- ]+$")))
            {
                warnings.Add(new Issue("", "Postal code contains non-latin characters"));
            }
        }
        foreach (var item2 in request.InputData.BillingAddresses)
        {
            if (!(item2.Address.Line1.Length > 0))
            {
                errors.Add(new Issue("", "At least one address line is required."));
            }
            if (item2.Address.Line2 != null)
            {
            }
            if (!(item2.Address.City.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item2.Address.City.Length <= 50))
            {
                warnings.Add(new Issue("", "City name is too long"));
            }
            if (!(Regex.IsMatch(item2.Address.City, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "City name contains non-latin characters"));
            }
            if (!(item2.Address.PostalCode.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item2.Address.PostalCode.Length >= 3))
            {
                warnings.Add(new Issue("", "Postal code is too short"));
            }
            if (!(item2.Address.PostalCode.Length <= 16))
            {
                warnings.Add(new Issue("", "Postal code is too long"));
            }
            if (!(Regex.IsMatch(item2.Address.PostalCode, "^[a-zA-Z0-9- ]+$")))
            {
                warnings.Add(new Issue("", "Postal code contains non-latin characters"));
            }
        }
        foreach (var item3 in request.InputData.DeliveryAddresses)
        {
            if (!(item3.Address.Line1.Length > 0))
            {
                errors.Add(new Issue("", "At least one address line is required."));
            }
            if (item3.Address.Line2 != null)
            {
            }
            if (!(item3.Address.City.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item3.Address.City.Length <= 50))
            {
                warnings.Add(new Issue("", "City name is too long"));
            }
            if (!(Regex.IsMatch(item3.Address.City, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "City name contains non-latin characters"));
            }
            if (!(item3.Address.PostalCode.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item3.Address.PostalCode.Length >= 3))
            {
                warnings.Add(new Issue("", "Postal code is too short"));
            }
            if (!(item3.Address.PostalCode.Length <= 16))
            {
                warnings.Add(new Issue("", "Postal code is too long"));
            }
            if (!(Regex.IsMatch(item3.Address.PostalCode, "^[a-zA-Z0-9- ]+$")))
            {
                warnings.Add(new Issue("", "Postal code contains non-latin characters"));
            }
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("add-insurances")]
    public async Task<ActionResult<WorkflowResponse<AddNewPatientState.AddedInsurances?>>> AddInsurances(WorkflowRequest<IAddNewPatientRequest.AddInsurances> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "AddInsurances"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewPatientState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewPatientState.AddedAddresses) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewPatientState.AddedAddresses) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewPatientState.AddedInsurances newState;
                try
                {
                    var ctx = new FlowContext<AddNewPatientState.AddedAddresses>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.AddInsurances(ctx, request.InputData.Insurances);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewPatientState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewPatientState>(AddNewPatientState.Key, instanceId, new WorkflowTransitionName<object>("AddInsurances"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewPatientState>("AddedInsurances"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewPatientState.AddedAddresses", "AddNewPatientState.AddedInsurances", username, "AddInsurances", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewPatientState.AddedInsurances>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-insurances/draft")]
    public async Task<ActionResult<Guid>> AddInsurancesDraft(DraftRequest<IAddNewPatientRequest.AddInsurances> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewPatientState.Key, request.InstanceId, "AddInsurances", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-insurances/validate")]
    public async Task<ActionResult<ValidationResult>> AddInsurancesDraft(WorkflowRequest<IAddNewPatientRequest.AddInsurances> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        foreach (var item1 in request.InputData.Insurances)
        {
            if (item1.InsuranceId != null)
            {
            }
            if (!(item1.PolicyId.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item1.GroupNumber.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item1.MemberId.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (item1.InsuranceHolder != null)
            {
                if (!(item1.InsuranceHolder!.Name.Length > 0))
                {
                    errors.Add(new Issue("", "Value is required"));
                }
                if (!(item1.InsuranceHolder!.Name.Length <= 50))
                {
                    warnings.Add(new Issue("", "Name part is too long"));
                }
                if (!(item1.InsuranceHolder!.Name.Length >= 2))
                {
                    warnings.Add(new Issue("", "Name part is too short"));
                }
                if (!(Regex.IsMatch(item1.InsuranceHolder!.Name, "^[a-zA-Z-]+$")))
                {
                    warnings.Add(new Issue("", "Name part contains non-latin characters"));
                }
                if (!((DateTime.Now - item1.InsuranceHolder!.DateOfBirth.ToDateTime(TimeOnly.Parse("12:00"))).Days / 365 <= 150))
                {
                    warnings.Add(new Issue("", "Date of birth is unrealistic"));
                }
                if (!(item1.InsuranceHolder!.DateOfBirth < DateOnly.FromDateTime(DateTime.Now)))
                {
                    errors.Add(new Issue("", "Date of birth is in the future"));
                }
            }
            if (item1.PhoneNumber != null)
            {
                if (!(item1.PhoneNumber!.PhoneNumber.Length > 0))
                {
                    errors.Add(new Issue("", "Value is required"));
                }
                if (!(Regex.IsMatch(item1.PhoneNumber!.PhoneNumber, "^[0-9]{3}-[0-9]{3}-[0-9]{4}$")))
                {
                    errors.Add(new Issue("", "Invalid phone number format"));
                }
                if (item1.PhoneNumber!.AllowsSMS != null)
                {
                }
                if (item1.PhoneNumber!.AllowsVoice != null)
                {
                }
                if (item1.PhoneNumber!.AllowsCommunication != null)
                {
                }
                if (item1.PhoneNumber!.Extension != null)
                {
                    if (!(item1.PhoneNumber!.Extension!.Length > 0))
                    {
                        errors.Add(new Issue("", "Value is required"));
                    }
                }
            }
            if (item1.Email != null)
            {
                if (!(item1.Email!.Email.Length > 0))
                {
                    errors.Add(new Issue("", "Value is required"));
                }
                if (!(Regex.IsMatch(item1.Email!.Email, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")))
                {
                    errors.Add(new Issue("", "Invalid email address"));
                }
            }
            if (item1.Address != null)
            {
                if (!(item1.Address!.Address.Line1.Length > 0))
                {
                    errors.Add(new Issue("", "At least one address line is required."));
                }
                if (item1.Address!.Address.Line2 != null)
                {
                }
                if (!(item1.Address!.Address.City.Length > 0))
                {
                    errors.Add(new Issue("", "Value is required"));
                }
                if (!(item1.Address!.Address.City.Length <= 50))
                {
                    warnings.Add(new Issue("", "City name is too long"));
                }
                if (!(Regex.IsMatch(item1.Address!.Address.City, "^[a-zA-Z-]+$")))
                {
                    warnings.Add(new Issue("", "City name contains non-latin characters"));
                }
                if (!(item1.Address!.Address.PostalCode.Length > 0))
                {
                    errors.Add(new Issue("", "Value is required"));
                }
                if (!(item1.Address!.Address.PostalCode.Length >= 3))
                {
                    warnings.Add(new Issue("", "Postal code is too short"));
                }
                if (!(item1.Address!.Address.PostalCode.Length <= 16))
                {
                    warnings.Add(new Issue("", "Postal code is too long"));
                }
                if (!(Regex.IsMatch(item1.Address!.Address.PostalCode, "^[a-zA-Z0-9- ]+$")))
                {
                    warnings.Add(new Issue("", "Postal code contains non-latin characters"));
                }
            }
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("add-guardians")]
    public async Task<ActionResult<WorkflowResponse<AddNewPatientState.AddedGuardians?>>> AddGuardians(WorkflowRequest<IAddNewPatientRequest.AddGuardians> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "AddGuardians"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewPatientState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewPatientState.AddedInsurances) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewPatientState.AddedInsurances) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewPatientState.AddedGuardians newState;
                try
                {
                    var ctx = new FlowContext<AddNewPatientState.AddedInsurances>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.AddGuardians(ctx, request.InputData.Guardians);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewPatientState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewPatientState>(AddNewPatientState.Key, instanceId, new WorkflowTransitionName<object>("AddGuardians"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewPatientState>("AddedGuardians"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewPatientState.AddedInsurances", "AddNewPatientState.AddedGuardians", username, "AddGuardians", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewPatientState.AddedGuardians>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-guardians/draft")]
    public async Task<ActionResult<Guid>> AddGuardiansDraft(DraftRequest<IAddNewPatientRequest.AddGuardians> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewPatientState.Key, request.InstanceId, "AddGuardians", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-guardians/validate")]
    public async Task<ActionResult<ValidationResult>> AddGuardiansDraft(WorkflowRequest<IAddNewPatientRequest.AddGuardians> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        foreach (var item1 in request.InputData.Guardians)
        {
            if (item1.GuardianBasicInformation.Prefix != null)
            {
            }
            if (!(item1.GuardianBasicInformation.FirstName.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item1.GuardianBasicInformation.FirstName.Length <= 50))
            {
                warnings.Add(new Issue("", "Name part is too long"));
            }
            if (!(item1.GuardianBasicInformation.FirstName.Length >= 2))
            {
                warnings.Add(new Issue("", "Name part is too short"));
            }
            if (!(Regex.IsMatch(item1.GuardianBasicInformation.FirstName, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "Name part contains non-latin characters"));
            }
            if (item1.GuardianBasicInformation.MiddleName != null)
            {
                if (!(item1.GuardianBasicInformation.MiddleName!.Length > 0))
                {
                    errors.Add(new Issue("", "Value is required"));
                }
                if (!(item1.GuardianBasicInformation.MiddleName!.Length <= 50))
                {
                    warnings.Add(new Issue("", "Name part is too long"));
                }
                if (!(Regex.IsMatch(item1.GuardianBasicInformation.MiddleName!, "^[a-zA-Z-]+$")))
                {
                    warnings.Add(new Issue("", "Name part contains non-latin characters"));
                }
            }
            if (!(item1.GuardianBasicInformation.LastName.Length > 0))
            {
                errors.Add(new Issue("", "Value is required"));
            }
            if (!(item1.GuardianBasicInformation.LastName.Length <= 50))
            {
                warnings.Add(new Issue("", "Name part is too long"));
            }
            if (!(item1.GuardianBasicInformation.LastName.Length >= 2))
            {
                warnings.Add(new Issue("", "Name part is too short"));
            }
            if (!(Regex.IsMatch(item1.GuardianBasicInformation.LastName, "^[a-zA-Z-]+$")))
            {
                warnings.Add(new Issue("", "Name part contains non-latin characters"));
            }
            if (item1.GuardianBasicInformation.Suffix != null)
            {
            }
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

    [HttpPost("add-clinical-information")]
    public async Task<ActionResult<WorkflowResponse<AddNewPatientState.Committed?>>> AddClinicalInformation(WorkflowRequest<IAddNewPatientRequest.AddClinicalInformation> request)
    {
        using (_logger.BeginScope(new {InstanceId = request.InstanceId, Transition = "AddClinicalInformation"}))
        {
            // Get the current state id
            if (request.InstanceId == null) return BadRequest("InstanceId is required");
            if (request.InstanceId == Guid.Empty) return BadRequest("InstanceId is required");
            Guid instanceId = (Guid) request.InstanceId;

            // Get the current user and their roles
            var username = User.FindFirst(JwtRegisteredClaimNames.Sub)?.Value;

            // Get the old state
            var rawOldState = await _engine.GetInstanceAsync(AddNewPatientState.Key, instanceId);
            if (rawOldState == null) return NotFound("Instance not found");
            if (rawOldState.StateData is not AddNewPatientState.AddedGuardians) return BadRequest("Invalid state type: " + rawOldState.StateData.GetType().Name);
            var oldState = (AddNewPatientState.AddedGuardians) rawOldState.StateData;

            // Call the flow
            var retryCount = 0;
            while (true)
            {
                AddNewPatientState.Committed newState;
                try
                {
                    var ctx = new FlowContext<AddNewPatientState.AddedGuardians>(instanceId, oldState, rawOldState.EntityLinks);
                    newState = await _flow.AddClinicalInformation(ctx, request.InputData.ClinicalConsiderations, request.InputData.SchedulingPreferences, request.InputData.AdditionalPatientNotes, request.InputData.CaregiverInformation);
                }
                catch (Exception ex)
                {
                    if (retryCount++ < 3) continue;
                    await _engine.AttachTransientErrorAsync(AddNewPatientState.Key, instanceId, ex);
                    throw;
                }
                await _engine.UpdateInstanceAsync<AddNewPatientState>(AddNewPatientState.Key, instanceId, new WorkflowTransitionName<object>("AddClinicalInformation"), System.Text.Json.JsonSerializer.Serialize(request.InputData, JsonSerializerOptions.Web), new WorkflowStateName<AddNewPatientState>("Committed"), newState);
                var endInstanceId = instanceId;

                await _auditService.LogAsync(endInstanceId, "AddNewPatientState.AddedGuardians", "AddNewPatientState.Committed", username, "AddClinicalInformation", new {});
                await _dbContext.SaveChangesAsync();

                return Ok(new WorkflowResponse<AddNewPatientState.Committed>(endInstanceId, newState));
            }
        }
    }

    [HttpPost("add-clinical-information/draft")]
    public async Task<ActionResult<Guid>> AddClinicalInformationDraft(DraftRequest<IAddNewPatientRequest.AddClinicalInformation> request)
    {
        await _engine.CreateOrAttachDraftAsync(AddNewPatientState.Key, request.InstanceId, "AddClinicalInformation", request.InputData);
        await _dbContext.SaveChangesAsync();
        return Ok(request.InstanceId);
    }

    [HttpPost("add-clinical-information/validate")]
    public async Task<ActionResult<ValidationResult>> AddClinicalInformationDraft(WorkflowRequest<IAddNewPatientRequest.AddClinicalInformation> request)
    {
        var warnings = new List<Issue>();
        var errors = new List<Issue>();
        foreach (var item1 in request.InputData.ClinicalConsiderations)
        {
        }
        if (request.InputData.SchedulingPreferences.TechnicianPreference != null)
        {
        }
        foreach (var item2 in request.InputData.SchedulingPreferences.PreferredDaysOfWeek)
        {
        }
        if (request.InputData.AdditionalPatientNotes != null)
        {
        }
        if (request.InputData.CaregiverInformation != null)
        {
        }
        return Ok(new ValidationResult(errors.Count == 0, warnings, errors));
    }

}
