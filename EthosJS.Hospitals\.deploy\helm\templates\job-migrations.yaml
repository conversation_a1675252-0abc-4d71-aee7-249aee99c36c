apiVersion: batch/v1
kind: Job
metadata:
  name: migrations
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
spec:
  completions: 1
  parallelism: 1
  template:
    metadata:
      labels:
        app: migrations
    spec:
      containers:
      - name: migrations
        image: {{ required "Variable 'image' is required" .Values.image }}
        imagePullPolicy: Always
        args:
          - migration:run
        envFrom:
          - configMapRef:
              name: {{ .Values.chart }}
          - configMapRef:
              name: common
          - secretRef:
              name: {{ .Values.chart }}
      restartPolicy: Never
      imagePullSecrets:
        - name: registry-secret
