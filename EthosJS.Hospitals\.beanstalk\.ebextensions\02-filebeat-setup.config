files:
   "/etc/filebeat/filebeat.yml":
      mode: "000755"
      owner: root
      group: root
      content: |
         filebeat.config:
           modules:
           path: /etc/filebeat/modules.d/*.yml
           reload.enabled: false
         filebeat.inputs:
         - type: container
           paths:
             - /var/lib/docker/containers/*/*.log
         processors:
           - add_fields:
               target: labels
               fields:
                 env: dev
                 project: common
           - add_fields:
               target: kubernetes
               fields:
                 namespace: regenesis-%ENVIRONMENT_NAME%
           - add_tags:
               tags: [k8s]
           - add_cloud_metadata: ~
           - add_host_metadata: ~
           - add_docker_metadata:
               host: "unix:///var/run/docker.sock"
           - decode_json_fields:
               fields: ["message"]
               target: "payload"
               overwrite_keys: true
           - drop_event.when.or:
               - regexp:
                   "container.name": "stunnel"
         output.logstash:
           hosts: [ "localhost:5044" ]

packages:
  rpm:
    filebeat: https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-7.17.6-x86_64.rpm

services:
  sysvinit:
    filebeat:
      enabled: true
      ensureRunning: true