﻿
using System.Text.Json.Serialization;
using Ethos.Model.Types;
using Newtonsoft.Json;

namespace Ethos.PlatformManager
{
    /// <summary>
    /// 
    /// </summary>
    public class EthosUserDto
    {
        public Guid? Id { get; set; }
        public string DisplayName { get; set; } = null!;
        public EthosUserNameDto Name { get; set; } = null!;
        public bool Active { get; set; }
        public string? Title { get; set; }
        public string? Department { get; set; }
        public DateTimeOffset? StartDate { get; set; }
        public DateTimeOffset? EndDate { get; set; }
        public string? UserType { get; set; }
        public List<Identifier> Identifiers { get; set; } = [];
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosUserNameDto
    {
        public string? Nickname { get; set; }
        public string? Prefix { get; set; }
        public string? Suffix { get; set; }
        public string? GivenName { get; set; }
        public string? Surname { get; set; }
        public string? MiddleName { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class AzureBeforeUserCreateRequest
    {
        public string? Email { get; set; }
        public string Step { get; set; } = null!;

        // B2C sends plaintext password here for this connector
        public string? Password { get; set; }
        public string? DisplayName { get; set; }
        public string? GivenName { get; set; }
        public string? Surname { get; set; }
        public string? JobTitle { get; set; }
        public string? ObjectId { get; set; }

        // B2C sends all collected user attributes. You can add more properties here
        // for any other claims you collect in your user flow (e.g., givenName, familyName).
        // Example for a custom attribute:
        // [JsonPropertyName("extension_yourcustomattribute")]
        // public string YourCustomAttribute { get; set; }

        [JsonProperty("ui_locales")]
        [JsonPropertyName("ui_locales")]
        public string? Locales { get; set; }

        [JsonProperty("client_id")]
        [JsonPropertyName("client_id")]
        public string? ClientId { get; set; }

        public ICollection<AzureAuthIdentity> Identities { get; set; } = [];
    }

    public class AzureBeforeUserCreateResponse : AzureUserFlowApiConnectorResponse
    { 
    }

    public class AzureAuthIdentity
    {
        public string SignInType { get; set; } = null!;
        public string Issuer { get; set; } = null!;
        public string IssuerAssignedId { get; set; } = null!;
    }

    /// <summary>
    /// 
    /// </summary>
    public class AzureTokenEnrichmentRequest
    {
        /***
         {
                "email": "<EMAIL>",
                "identities": [
                     {
                     "signInType":"federated",
                     "issuer":"facebook.com",
                     "issuerAssignedId":"0123456789"
                     }
                ],
                "displayName": "John Smith",
                "objectId": "123e4567-e89b-12d3-a456-426614174000",
                "client_id": "00001111-aaaa-2222-bbbb-3333cccc4444",
                "step": "PreTokenIssuance",
                "ui_locales":"en-US"
          }
        ***/

        public string? Email { get; set; }
        public string? DisplayName { get; set; }
        public string? ObjectId { get; set; }

        [JsonPropertyName("client_id")]
        [JsonProperty("client_id")]
        public string? ClientId { get; set; }
        public string? Step { get; set; }

        public ICollection<AzureAuthIdentity> Identities { get; set; } = [];

        [JsonPropertyName("ui_locales")]
        [JsonProperty("ui_locales")]
        public string? Locales { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosAzureTokenEnrichmentResponse : AzureUserFlowApiConnectorResponse
    {
        [JsonPropertyName("extension_ethosTenantId")]
        [JsonProperty("extension_ethosTenantId")]
        public string? TenantId { get; set; }

        [JsonPropertyName("extension_ethosScopes")]
        [JsonProperty("extension_ethosScopes")]
        public string? Scopes { get; set; }

        [JsonPropertyName("extension_products")]
        [JsonProperty("extension_products")]
        public string? Products { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class AzureUserFlowApiConnectorResponse
    {
        public const string ValidationError = nameof(ValidationError);
        public const string Continue = nameof(Continue);
        public const string ShowBlockPage = nameof(ShowBlockPage);

        /// <summary>
        /// 
        /// </summary>
        public string Version
        {
            get
            {
                return "1.0.0";
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public string Action { get; set; } = Continue;

        /// <summary>
        /// 
        /// </summary>
        public void SetValidationError()
        {
            Action = ValidationError;
        }

        /// <summary>
        /// 
        /// </summary>
        public void SetContinue()
        {
            Action = Continue;
        }

        /// <summary>
        /// 
        /// </summary>
        public void SetBlock()
        {
            Action = ShowBlockPage;
        }

        public string? UserMessage { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class EthosUserSummaryDto
    {
        public string DisplayName { get; set; } = null!;
        public Guid Id { get; set; }
        public Guid TenantId { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ScimMultiValuedAttribute
    {
        [JsonPropertyName("value")]
        public string? Value { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; } // e.g., "work", "home"

        [JsonPropertyName("primary")]
        public bool? Primary { get; set; } // Indicates if this is the primary value

        [JsonPropertyName("display")]
        public string? Display { get; set; } // Human-readable name for the value
    }

    // Class for the 'name' complex attribute
    public class ScimName
    {
        [JsonPropertyName("formatted")]
        public string? Formatted { get; set; }

        [JsonPropertyName("familyName")]
        public string? FamilyName { get; set; }

        [JsonPropertyName("givenName")]
        public string? GivenName { get; set; }

        [JsonPropertyName("middleName")]
        public string? MiddleName { get; set; }

        [JsonPropertyName("honorificPrefix")]
        public string? HonorificPrefix { get; set; }

        [JsonPropertyName("honorificSuffix")]
        public string? HonorificSuffix { get; set; }
    }

    // Class for the 'address' complex attribute
    public class ScimAddress : ScimMultiValuedAttribute // Addresses are also multi-valued
    {
        [JsonPropertyName("streetAddress")]
        public string? StreetAddress { get; set; }

        [JsonPropertyName("locality")]
        public string? Locality { get; set; } // City

        [JsonPropertyName("region")]
        public string? Region { get; set; } // State/Province

        [JsonPropertyName("postalCode")]
        public string? PostalCode { get; set; }

        [JsonPropertyName("country")]
        public string? Country { get; set; }

        [JsonPropertyName("formatted")]
        public string? Formatted { get; set; } // Full formatted address
    }

    // Class for the 'meta' attribute
    public class ScimMeta
    {
        [JsonPropertyName("resourceType")]
        public string? ResourceType { get; set; }

        [JsonPropertyName("created")]
        public DateTime? Created { get; set; }

        [JsonPropertyName("lastModified")]
        public DateTime? LastModified { get; set; }

        [JsonPropertyName("location")]
        public string? Location { get; set; }

        [JsonPropertyName("version")]
        public string? Version { get; set; } // ETag
    }

    // Main SCIM User Entity class
    public class ScimUser
    {
        // Standard SCIM attributes
        [JsonPropertyName("schemas")]
        public List<string> Schemas { get; set; } = [];

        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("externalId")]
        public string? ExternalId { get; set; }

        [JsonPropertyName("userName")]
        public string? UserName { get; set; }

        [JsonPropertyName("name")]
        public ScimName? Name { get; set; }

        [JsonPropertyName("displayName")]
        public string? DisplayName { get; set; }

        [JsonPropertyName("nickName")]
        public string? NickName { get; set; }

        [JsonPropertyName("profileUrl")]
        public string? ProfileUrl { get; set; }

        [JsonPropertyName("title")]
        public string? Title { get; set; }

        [JsonPropertyName("userType")]
        public string? UserType { get; set; }

        [JsonPropertyName("preferredLanguage")]
        public string? PreferredLanguage { get; set; }

        [JsonPropertyName("locale")]
        public string? Locale { get; set; }

        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        [JsonPropertyName("active")]
        public bool? Active { get; set; }

        // Multi-valued complex attributes
        [JsonPropertyName("emails")]
        public List<ScimMultiValuedAttribute> Emails { get; set; } = [];

        [JsonPropertyName("phoneNumbers")]
        public List<ScimMultiValuedAttribute> PhoneNumbers { get; set; } = [];

        [JsonPropertyName("addresses")]
        public List<ScimAddress> Addresses { get; set; } = [];

        // Meta attribute
        [JsonPropertyName("meta")]
        public ScimMeta? Meta { get; set; }
    }
}