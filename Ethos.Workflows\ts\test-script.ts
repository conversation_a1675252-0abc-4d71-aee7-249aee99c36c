import axios from 'axios';
import { QueryDto } from './ethos-common';
import * as Common from './ethos-common-types';

import { LoginApi, LoginRequestDto } from './login-api';
import { ProviderApi, CreateProviderDto } from './provider-api';
import { FacilityApi, CreateFacilityDto } from './facility-api';
import { CareLocationApi, CreateCareLocationDto, CareLocationQ } from './care-location-api';
import { PhysicianApi, CreatePhysicianDto } from './physician-api';
import { RoomApi, CreateRoomDto } from './room-api';
import { EquipmentApi, CreateEquipmentDto } from './equipment-api';


async function main() {
    const session = axios.create({
        baseURL: 'http://localhost:4000/',
        headers: { 'Content-Type': 'application/json' }
    });

    const loginClient = new LoginApi(session);
    const token = await loginClient.login({ username: 'test', password: 'password' });
    console.log('Logged in successfully.');

    // API clients are automatically configured with the token
    const providers = new ProviderApi(session);
    const facilities = new FacilityApi(session);
    const careLocations = new CareLocationApi(session);
    const roomClient = new RoomApi(session);
    const equipmentClient = new EquipmentApi(session);
    const physicianClient = new PhysicianApi(session);

    // Example Usage:
    const newProvider: CreateProviderDto = { name: 'New Provider TS' };
    const { data: createdProvider, etag: providerEtag } = await providers.create(newProvider);
    console.log(`Created provider:`, createdProvider);

    if (!providerEtag) throw new Error("Provider creation failed to return ETag");

    const newFacility: CreateFacilityDto = {
        name: 'New Facility TS',
        providerId: createdProvider.id,
        npi: '0*********',
        address: Common.AddressDto.fromJSON({
            line1: '456 TS Ave',
            line2: 'Apt 2',
            city: 'TypeScriptVille',
            state: 'CA', // Assuming state is a string enum or similar
            postalCode: '54321',
            country: 'USA'
        })
    };
    const { data: createdFacility } = await facilities.create(newFacility);
    console.log(`Created facility:`, createdFacility);

    const newCareLocation: CreateCareLocationDto = {
        name: 'New Care Location TS',
        parentId: null,
        facilityId: createdFacility.id
    };
    const { data: createdCareLocation } = await careLocations.create(newCareLocation);
    console.log(`Created care location:`, createdCareLocation);

    const query: QueryDto<CareLocationQ> = {
        $type: 'Literal',
        value: CareLocationQ.fromJSON({ $type: 'WithId', item: createdCareLocation.id })
    };
    const searchResult = await careLocations.search(query);
    console.log(`Searched care location:`, searchResult.items[0]);

    const newRoom: CreateRoomDto = {
        name: 'Room 202',
        careLocationId: createdCareLocation.id
    };
    const { data: createdRoom } = await roomClient.create(newRoom);
    console.log(`Created room:`, createdRoom);

    // Note: Assuming equipmentTypeId is a known UUID or number from your system
    const newEquipment: CreateEquipmentDto = {
        roomId: createdRoom.id,
        careLocationId: createdCareLocation.id,
        equipmentTypeId: "f3a4b4c1-949f-4e4b-8321-72f04925762c",
        equipmentData: {
            serialNumber: 'SN-TS-654321',
            manufacturer: 'TS Corp',
            model: 'Model TS-Y'
        }
    };
    const { data: createdEquipment } = await equipmentClient.create(newEquipment);
    console.log(`Created equipment:`, createdEquipment);

    const newPhysician: CreatePhysicianDto = {
        names: [Common.HumanNameDto.fromJSON({
            prefix: 'Dr',
            firstName: 'Jane',
            middleName: 'T',
            lastName: 'Script',
            suffix: 'MD'
        })],
        npi: '**********',
        taxId: '*********',
        careLocationIds: [createdCareLocation.id]
    };
    const { data: createdPhysician } = await physicianClient.create(newPhysician);
    console.log(`Created physician:`, createdPhysician);
}

main().catch(error => {
    console.error('Test script failed:', error.isAxiosError ? error.message : error);
    process.exit(1);
});
