#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status

CONNECTION_STRING=$ConnectionStrings__DefaultConnection

# Initialize variables
DB_HOST=""
DB_DATABASE=""
DB_USERNAME=""
DB_PASSWORD=""

echo "Parsing connection string: $CONNECTION_STRING"

# Set Internal Field Separator to semicolon to split key-value pairs
IFS=';' read -ra ADDR <<< "$CONNECTION_STRING"

for i in "${ADDR[@]}"; do
    KEY=$(echo "$i" | cut -d'=' -f1)
    VALUE=$(echo "$i" | cut -d'=' -f2-) # Use -f2- to handle cases where value might contain '='

    case "$KEY" in
        "Host")
            DB_HOST="$VALUE"
            ;;
        "Database")
            DB_DATABASE="$VALUE"
            ;;
        "Username")
            DB_USERNAME="$VALUE"
            ;;
        "Password")
            DB_PASSWORD="$VALUE"
            ;;
        *)
            # Handle unexpected keys if necessary
            echo "Warning: Unknown key '$KEY' found in connection string."
            ;;
    esac
done

PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USERNAME -d $DB_DATABASE -c "DROP SCHEMA IF EXISTS RefData CASCADE;"

dotnet Ethos.ReferenceData.dll