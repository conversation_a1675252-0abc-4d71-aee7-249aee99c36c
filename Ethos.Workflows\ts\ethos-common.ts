/**
 * ethos-common.ts – shared helpers for all autogenerated clients.
 * Requires TypeScript 5.0+ • axios
 */

import axios, { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios';
import { Buffer } from 'buffer';

// -------------------------------------------------------------------------------------------------
//  TYPE ALIASES
// -------------------------------------------------------------------------------------------------
export type UUID = string;
export type DateOnly = string; // ISO 8601 format, e.g., "2023-10-01"
export type TimeOnly = string; // ISO 8601 format, e.g., "12:34:56"
export type DateTime = string; // ISO 8601 format, e.g., "2023-10-01T12:34:56Z"
export type DateTimeOffset = string; // ISO 8601 format with offset, e.g., "2023-10-01T12:34:56+00:00"

export type Json = string | number | boolean | null | Json[] | { [key: string]: Json };
export type JsonDict = { [key: string]: Json };

// -------------------------------------------------------------------------------------------------
//  GENERIC HELPERS
// -------------------------------------------------------------------------------------------------

export interface PagingParameters {
    offset?: number;
    limit?: number;
}

export interface PagedResponse<TOutput> {
    items: TOutput[];
    total?: number;
}

export interface ETagged<T> {
    data: T;
    etag: string | null;
}

export interface Issue {
    paths: string[];
    message: string;
    issueId: UUID;
    data?: JsonDict;
}

export interface DraftDto {
    id: UUID;
    entityType: string;
    entityId: UUID;
    data: JsonDict;
}

export interface ValidatedDraftDto {
    id: UUID;
    entityType: string;
    entityId: UUID;
    data: JsonDict;
    errors?: Issue[];
    warnings?: Issue[];
}

function toJSON(obj: any): any {
    if (obj === null || obj === undefined) {
        return null;
    }
    // Assumes custom types have a .toJSON method if needed.
    if (typeof obj.toJSON === 'function') {
        return obj.toJSON();
    }
    return obj;
}


function buildQueryString(p?: PagingParameters, q?: any): string {
    const params = new URLSearchParams();
    if (p?.offset !== undefined) params.append('offset', p.offset.toString());
    if (p?.limit !== undefined) params.append('limit', p.limit.toString());
    if (q) {
        const jsonPayload = JSON.stringify(toJSON(q));
        params.append('queryBase64', Buffer.from(jsonPayload).toString('base64'));
    }
    const qs = params.toString();
    return qs ? `?${qs}` : '';
}


// -------------------------------------------------------------------------------------------------
//  GENERIC QueryDto ADT (All / Literal / Not / And / Or)
// -------------------------------------------------------------------------------------------------

export interface QueryDto_All { $type: 'All'; }
export interface QueryDto_Literal<TPrim> { $type: 'Literal'; value: TPrim; }
export interface QueryDto_Not<TPrim> { $type: 'Not'; expr: QueryDto<TPrim>; }
export interface QueryDto_And<TPrim> { $type: 'And'; exprs: QueryDto<TPrim>[]; }
export interface QueryDto_Or<TPrim> { $type: 'Or'; exprs: QueryDto<TPrim>[]; }

export type QueryDto<TPrim> =
    | QueryDto_All
    | QueryDto_Literal<TPrim>
    | QueryDto_Not<TPrim>
    | QueryDto_And<TPrim>
    | QueryDto_Or<TPrim>;

export namespace QueryDto {
    export function toJSON(q: QueryDto<any>): any {
        switch (q.$type) {
            case 'All': return { $type: 'All' };
            case 'Literal': return { $type: 'Literal', value: toJSON(q.value) };
            case 'Not': return { $type: 'Not', expr: toJSON(q.expr) };
            case 'And': return { $type: 'And', exprs: q.exprs.map(toJSON) };
            case 'Or': return { $type: 'Or', exprs: q.exprs.map(toJSON) };
        }
    }
}


// -------------------------------------------------------------------------------------------------
//  BASE ASYNC API CLIENT (axios)
// -------------------------------------------------------------------------------------------------
export abstract class EntityHttpClientBase<TInput, TOutput, TQuery> {
    protected readonly session: AxiosInstance;
    protected readonly path: string;

    constructor(session: AxiosInstance, route: string) {
        if (!session.defaults.baseURL) {
            throw new Error('AxiosInstance must be created with a baseURL.');
        }
        this.session = session;
        this.path = `/api/${route.replace(/^\/|\/$/g, '')}`;
    }

    private async request<T>(config: AxiosRequestConfig): Promise<ETagged<T>> {
        try {
            const response = await this.session.request<T>(config);
            const etag = response.headers['etag']?.replace(/"/g, '') || null;
            return { data: response.data, etag };
        } catch (error) {
            if (axios.isAxiosError(error)) {
                console.error(`HTTP ${config.method} ${config.url} -> ${error.response?.status}`);
                console.error('Error details:', error.response?.data);
            } else {
                console.error('An unexpected error occurred:', error);
            }
            throw error;
        }
    }

    private static getETagged<T>(response: AxiosResponse<T>): ETagged<T> {
        const etag = response.headers['etag']?.replace(/"/g, '') || null;
        return { data: response.data, etag };
    }

    // Set bearer token on the axios instance for all subsequent requests
    setBearerToken(token: string): void {
        this.session.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    async getById(id: UUID): Promise<ETagged<TOutput>> {
        return this.request<TOutput>({
            method: 'GET',
            url: `${this.path}/${id}`
        });
    }

    async get(query?: QueryDto<TQuery>, paging?: PagingParameters): Promise<PagedResponse<TOutput>> {
        const qs = buildQueryString(paging, query ? QueryDto.toJSON(query) : undefined);
        const result = await this.request<PagedResponse<TOutput>>({
            method: 'GET',
            url: this.path + qs
        });
        return result.data; // Paged responses don't typically have an ETag for the collection itself
    }

    async search(query: QueryDto<TQuery>, paging?: PagingParameters): Promise<PagedResponse<TOutput>> {
        const qs = buildQueryString(paging);
        const result = await this.request<PagedResponse<TOutput>>({
            method: 'POST',
            url: `${this.path}/search${qs}`,
            data: QueryDto.toJSON(query)
        });
        return result.data;
    }

    async create(dto: TInput): Promise<ETagged<TOutput>> {
        return this.request<TOutput>({
            method: 'POST',
            url: this.path,
            data: toJSON(dto)
        });
    }

    async put(id: UUID, dto: TInput, etag?: string): Promise<ETagged<TOutput> & { created: boolean }> {
        const config: AxiosRequestConfig = {
            method: 'PUT',
            url: `${this.path}/${id}`,
            data: toJSON(dto),
            headers: etag ? { 'If-Match': `"${etag}"` } : {}
        };
        const response = await this.session.request(config);
        const newEtag = response.headers['etag']?.replace(/"/g, '') || null;
        return {
            data: response.data,
            etag: newEtag,
            created: response.status === 201
        };
    }

    async patch(id: UUID, dto: Partial<TInput>, etag: string): Promise<ETagged<TOutput>> {
        return this.request<TOutput>({
            method: 'PATCH',
            url: `${this.path}/${id}`,
            data: toJSON(dto),
            headers: { 'If-Match': `"${etag}"` }
        });
    }

    // --- Drafts ---

    /**
     * Creates a new draft. Can be created from raw data (for a new entity)
     * or from an existing entity's ID.
     * @param args An object containing either `data` or `entityId`.
     * @returns The created draft with validation results.
     */
    async createDraft(args: { data: JsonDict, entityId?: never } | { data?: never, entityId: UUID }): Promise<ETagged<ValidatedDraftDto>> {
        if (args.data && args.entityId) {
            throw new Error("Cannot provide both 'data' and 'entityId' to createDraft.");
        }

        const qs = args.entityId ? `?entityId=${encodeURIComponent(args.entityId)}` : '';
        const response = await this.request<ValidatedDraftDto>({
            method: 'POST',
            url: `${this.path}/draft${qs}`,
            data: args.data // Will be undefined if entityId is passed, which is correct
        });
        return EntityHttpClientBase.getETagged(response);
    }

    /**
     * Retrieves a draft by its unique ID.
     * @param draftId The UUID of the draft to retrieve.
     */
    async getDraftById(draftId: UUID): Promise<ETagged<DraftDto>> {
        const response = await this.request<DraftDto>({
            method: 'GET',
            url: `${this.path}/draft/${draftId}`
        });
        return EntityHttpClientBase.getETagged(response);
    }

    /**
     * Modifies an existing draft's data.
     * @param draftId The UUID of the draft to modify.
     * @param data The new JSON data for the draft.
     * @param etag The current ETag of the draft for concurrency control.
     * @returns The modified draft with validation results.
     */
    async modifyDraft(draftId: UUID, data: JsonDict, etag: string): Promise<ETagged<ValidatedDraftDto>> {
        const response = await this.request<ValidatedDraftDto>({
            method: 'PUT',
            url: `${this.path}/draft/${draftId}`,
            data: toJSON(data),
            headers: { 'If-Match': `"${etag}"` }
        });
        return EntityHttpClientBase.getETagged(response);
    }

    /**
     * Validates a draft's data.
     * @param data The JSON data for the draft.
     * @returns The draft with validation results.
     */
    async validateUnsavedDraft(data: JsonDict): Promise<ETagged<ValidatedDraftDto>> {
        const response = await this.request<ValidatedDraftDto>({
            method: 'POST',
            url: `${this.path}/draft/validate`,
            data: toJSON(data)
        });
        return EntityHttpClientBase.getETagged(response);
    }

    /**
     * Validates a saved draft's data.
     * @param draftId The UUID of the draft to validate.
     * @param etag The current ETag of the draft for concurrency control.
     * @returns The draft with validation results.
     */
    async validateDraft(draftId: UUID, etag: string): Promise<ETagged<ValidatedDraftDto>> {
        const response = await this.request<ValidatedDraftDto>({
            method: 'POST',
            url: `${this.path}/draft/${draftId}/validate`,
            headers: {'If-Match': `"${etag}"`}
        });
        return EntityHttpClientBase.getETagged(response);
    }

    /**
     * Commits a draft, which attempts to create or update the real entity.
     * @param draftId The UUID of the draft to commit.
     * @param etag The current ETag of the draft for concurrency control.
     * @returns The final, committed entity and whether it was newly created.
     */
    async commitDraft(draftId: UUID, etag: string): Promise<ETagged<TOutput> & { created: boolean }> {
        const response = await this.request<TOutput>({
            method: 'POST',
            url: `${this.path}/draft/${draftId}/commit`,
            headers: { 'If-Match': `"${etag}"` }
        });
        return {
            ...EntityHttpClientBase.getETagged(response),
            created: response.status === 201
        };
    }
}