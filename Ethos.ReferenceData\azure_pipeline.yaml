name: Ethos.ReferenceData Pipeline

trigger:
  branches:
    include:
      - '*'
  paths:
    include:
      - Ethos.ReferenceData/*

pool:
  name: 'Default'  # Self-hosted agent pool

variables:
  - group: DatabaseCredentials
  - name: acrName
    value: 'ethoscrdev.azurecr.io'
  # Tag will be read from .env-dev file

stages:
  - stage: Build
    displayName: "Build and Test Projects"
    jobs:
      - job: Build_Ethos_ReferenceData
        displayName: "Build and Test Ethos.ReferenceData"
        steps:
          - script: |
              echo "Reading tag from .env-dev file..."
              
              # Check if .env-dev file exists
              if [ ! -f "Ethos.ReferenceData/External-assets/k8s-manifests/.env-dev" ]; then
                echo "⚠️  .env-dev file not found. Using default tag for first-time setup."
                echo "##vso[task.setvariable variable=tag]latest"
              else
                # Read TAG from .env-dev file
                TAG_VALUE=$(grep "^TAG=" Ethos.ReferenceData/External-assets/k8s-manifests/.env-dev | cut -d'=' -f2 | tr -d '"' | tr -d "'")
              
                if [ -z "$TAG_VALUE" ]; then
                  echo "⚠️  TAG not found in .env-dev file. Using default tag."
                  echo "##vso[task.setvariable variable=tag]latest"
                else
                  echo "✅ Tag found: $TAG_VALUE"
                  echo "##vso[task.setvariable variable=tag]$TAG_VALUE"
                fi
              fi
              
              echo "Using tag: $(tag)"
            displayName: 'Read Tag from .env-dev File'

          - task: UseDotNet@2
            inputs:
              packageType: 'sdk'
              version: '8.x'
            displayName: 'Install .NET SDK'

          - script: |
              dotnet restore ./Ethos.ReferenceData/Ethos.ReferenceData.csproj
              dotnet build ./Ethos.ReferenceData/Ethos.ReferenceData.csproj --configuration Release
              dotnet test ./Ethos.ReferenceData/Ethos.ReferenceData.csproj --configuration Release
            displayName: 'Restore, Build, and Test Ethos.ReferenceData'

          - script: |
              REPORT_TIME=$(date +"%Y_%m_%d_%H_%M_%S")
              echo "##vso[task.setvariable variable=REPORT_TIME]$REPORT_TIME"
            displayName: 'Set SAST Report Timestamp'

          - script: |
              dotnet tool install --global Microsoft.CST.DevSkim.CLI
              export PATH="$PATH:$HOME/.dotnet/tools"
              devskim analyze -I Ethos.ReferenceData/ --output-format sarifv2 --output-file "SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).sarif"
            displayName: 'Run DevSkim SAST Scan'

          - script: |
              sudo pip install sarif-tools
              python3 Ethos.ReferenceData/External-assets/Scripts/convert_to_html_sast.py "SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).sarif" "SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).html"
            displayName: 'Convert SARIF to HTML'

          - publish: SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).html
            artifact: devskim-html-results
            displayName: 'Publish DevSkim SAST Results in HTML'

          - publish: SAST_Scan_Report_EthosReferenceData_$(REPORT_TIME).sarif
            artifact: devskim-sast-results
            displayName: 'Publish DevSkim SAST Results'

          - script: |
              echo "Building Docker image with tag: $(tag)"
            displayName: 'Display Tag Information'

          - task: Docker@2
            inputs:
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethos-referencedata'
              command: 'buildAndPush'
              Dockerfile: './Ethos.ReferenceData/Dockerfile'
              buildContext: '.'
              tags: |
                $(tag)
                latest
            displayName: 'Build and Push Ethos.ReferenceData Image'

          - script: |
              # Install Trivy
              wget https://github.com/aquasecurity/trivy/releases/download/v0.63.0/trivy_0.63.0_Linux-64bit.deb
              sudo dpkg -i trivy_0.63.0_Linux-64bit.deb
              trivy --version
            displayName: 'Install Trivy Scanner'

          - script: |
              # Scan dependencies (NuGet packages, etc.)
              echo "Scanning dependencies with Trivy..."
              trivy fs --format json --output "Trivy_Dependencies_SCAN_Report_EthosReferenceData_$(REPORT_TIME).json" ./Ethos.ReferenceData/
              echo "Dependencies scan completed"
            displayName: 'Trivy Dependencies Scan'

          - script: |
              # Scan the built Docker image
              echo "Scanning Docker image with Trivy..."
              trivy image --scanners vuln --format json  --output "Trivy_Image_SCAN_Report_EthosReferenceData_$(REPORT_TIME).json" ethoscrdev.azurecr.io/ethos-referencedata:$(tag)
              echo "Docker image scan completed"
            displayName: 'Trivy Docker Image Scan'

          - script: |
              # Convert Trivy JSON reports to HTML
              pip install pandas
              python3 Ethos.ReferenceData/External-assets/Scripts/convert_to_html_trivy.py  "Trivy_Dependencies_SCAN_Report_EthosReferenceData_$(REPORT_TIME).json" "Trivy_Dependencies_SCAN_Report_EthosReferenceData_$(REPORT_TIME).html" "Trivy Dependencies SCAN"
              echo "Trivy dependencies HTML report generated"
              python3 Ethos.ReferenceData/External-assets/Scripts/convert_to_html_trivy.py "Trivy_Image_SCAN_Report_EthosReferenceData_$(REPORT_TIME).json" "Trivy_Image_SCAN_Report_EthosReferenceData_$(REPORT_TIME).html" "Trivy Docker IMAGE SCAN"
              echo "Trivy Docker HTML report generated"
              ls -l *.sarif *.json *.html
            displayName: 'Convert Trivy Reports to HTML'

          - publish: Trivy_Dependencies_SCAN_Report_EthosReferenceData_$(REPORT_TIME).html
            artifact: trivy-dependencies-html-results
            displayName: 'Publish Trivy Dependencies HTML Results'

          - publish: Trivy_Dependencies_SCAN_Report_EthosReferenceData_$(REPORT_TIME).json
            artifact: trivy-dependencies-json-results
            displayName: 'Publish Trivy Dependencies JSON Results'

          - publish: Trivy_Image_SCAN_Report_EthosReferenceData_$(REPORT_TIME).html
            artifact: trivy-image-html-results
            displayName: 'Publish Trivy Image HTML Results'

          - publish: Trivy_Image_SCAN_Report_EthosReferenceData_$(REPORT_TIME).json
            artifact: trivy-image-json-results
            displayName: 'Publish Trivy Image JSON Results'

  # - stage: PreDeploy
  #   displayName: "Prepare Database for Deployment"
  #   condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  #   jobs:
  #     # - job: Clean_Database
  #     #   displayName: "Cleanup Database"
  #     #   steps:
  #     #     - script: |
  #     #         # Use a Docker container with PostgreSQL client pre-installed
  #     #         docker run --rm -e PGPASSWORD='$(WORKFLOW_DEV_DB_PASSWORD)' postgres:14-alpine psql -h api-dev.ethos.net -p 30101 -U postgres -d postgres -c "
  #     #         DO \$\$
  #     #         DECLARE
  #     #             r RECORD;
  #     #         BEGIN
  #     #             FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
  #     #                 EXECUTE 'DROP TABLE public.' || quote_ident(r.tablename) || ' CASCADE;';
  #     #             END LOOP;
  #     #         END \$\$;
  #     #         "
  #     #       displayName: 'Drop Schema Tables with Docker PostgreSQL client'
  #     - job: Apply_EF_Migrations
  #       displayName: "Apply EF Core Migrations"
  #       # dependsOn: Clean_Database
  #       steps:
  #         - task: UseDotNet@2
  #           inputs:
  #             packageType: 'sdk'
  #             version: '8.x'
  #           displayName: 'Install .NET SDK'

  #         - script: dotnet tool install --global dotnet-ef
  #           displayName: 'Install dotnet-ef CLI'

  #         - script: |
  #             export PATH="$PATH:$HOME/.dotnet/tools"
  #             dotnet restore ./Ethos.ReferenceData/Ethos.ReferenceData.csproj
  #             dotnet ef database update --project ./Ethos.ReferenceData/Ethos.ReferenceData.csproj
  #           env:
  #             ConnectionStrings__DefaultConnection: 'Host=api-dev.ethos.net;Port=30101;Database=postgres;Username=postgres;Password=$(WORKFLOW_DEV_DB_PASSWORD)'
  #           displayName: 'Apply EF Migrations to Database'

  - stage: Deploy
    displayName: "Deploy to Kubernetes"
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
    jobs:
      - job: Deploy_to_AKS
        displayName: "Deploy Updated Services"
        steps:
          - script: |
              echo "Reading tag from .env-dev file for deployment..."
              
              # Check if .env-dev file exists  
              if [ ! -f "Ethos.ReferenceData/External-assets/k8s-manifests/.env-dev" ]; then
                echo "⚠️  .env-dev file not found. Using default tag."
                echo "##vso[task.setvariable variable=deployTag]latest"
              else
                # Read TAG from .env-dev file
                TAG_VALUE=$(grep "^TAG=" Ethos.ReferenceData/External-assets/k8s-manifests/.env-dev | cut -d'=' -f2 | tr -d '"' | tr -d "'")
              
                if [ -z "$TAG_VALUE" ]; then
                  echo "⚠️  TAG not found in .env-dev file. Using default tag."
                  echo "##vso[task.setvariable variable=deployTag]latest"
                else
                  echo "✅ Deploying with tag: $TAG_VALUE"
                  echo "##vso[task.setvariable variable=deployTag]$TAG_VALUE"
                fi
              fi
            displayName: 'Read Tag for Deployment'

          - script: |
              echo "Updating deployment.yaml with dynamic tag..."
              
              # Update the image tag in deployment.yaml
              sed -i "s|ethoscrdev.azurecr.io/ethos-referencedata:.*|ethoscrdev.azurecr.io/ethos-referencedata:$(deployTag)|g" Ethos.ReferenceData/External-assets/k8s-manifests/deployment.yaml
              
              echo "Updated deployment.yaml:"
              cat Ethos.ReferenceData/External-assets/k8s-manifests/deployment.yaml | grep "image:"

            displayName: 'Update Deployment Manifest with Dynamic Tag'

          - script: |
              echo "Checking if kubectl is installed..."
              if ! command -v kubectl &> /dev/null; then
                echo "kubectl not found! Installing..."
                sudo apt-get update && sudo apt-get install -y kubectl
              else
                echo "kubectl is already installed.."
              fi
            displayName: 'Ensure kubectl is Installed'

          - script: |
              echo "Validating kubectl version..."
              kubectl version --client
            displayName: 'Validate kubectl Installation'

          - task: KubernetesManifest@1
            inputs:
              action: 'deploy'
              namespace: 'ethos-ns-dev'
              manifests: |
                ./Ethos.ReferenceData/External-assets/k8s-manifests/deployment.yaml
                ./Ethos.ReferenceData/External-assets/k8s-manifests/service.yaml
              kubernetesServiceEndpoint: 'aks-service-connection'
            displayName: 'Deploy Application to AKS'

          - task: Kubernetes@1
            displayName: 'Restart Deployment'
            inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceEndpoint: 'aks-service-connection'
              command: 'rollout'
              arguments: 'restart deployment ethos-referencedata -n ethos-ns-dev'

          - script: |
              echo "Cleaning up local Docker images..."
              docker images | grep ethos-referencedata | awk '{print $3}' | xargs -r docker rmi || true
            displayName: 'Clean Up Local Docker Images'
            continueOnError: true

          - script: |
              echo "============================================"
              echo "🚀 DEPLOYMENT COMPLETED SUCCESSFULLY! 🚀"
              echo "============================================"
              echo "Deployed Tag: $(deployTag)"
              echo "Environment: ethos-ns-dev"
              echo "Application: ethos-referencedata"
              echo "============================================"
            displayName: 'Deployment Summary'