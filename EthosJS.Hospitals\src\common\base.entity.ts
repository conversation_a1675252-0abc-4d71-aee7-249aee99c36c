import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  DeleteDateColumn,
  UpdateDateColumn,
  BaseEntity as TypeormBaseEntity,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export abstract class BaseEntity extends TypeormBaseEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('increment', { type: 'int' })
  @Expose()
  id: number;

  @ApiProperty()
  @CreateDateColumn({ type: 'timestamp without time zone' })
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @UpdateDateColumn({ type: 'timestamp without time zone' })
  @Expose()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}
