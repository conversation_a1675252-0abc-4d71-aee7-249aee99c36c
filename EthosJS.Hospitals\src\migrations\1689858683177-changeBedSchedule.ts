import { MigrationInterface, QueryRunner } from 'typeorm';

export class changeBedSchedule1689858683177 implements MigrationInterface {
    name = 'changeBedSchedule1689858683177'

    public async up(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "bed_schedules" DROP COLUMN "number_of_beds"');
      await queryRunner.query('ALTER TABLE "bed_schedules" DROP COLUMN "shift"');
      await queryRunner.query('DROP TYPE "public"."bed_schedules_shift_enum"');
      await queryRunner.query('ALTER TABLE "bed_schedules" ADD "day_shift_beds" integer NOT NULL');
      await queryRunner.query('ALTER TABLE "bed_schedules" ADD "night_shift_beds" integer NOT NULL');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query('ALTER TABLE "bed_schedules" DROP COLUMN "night_shift_beds"');
      await queryRunner.query('ALTER TABLE "bed_schedules" DROP COLUMN "day_shift_beds"');
      await queryRunner.query('CREATE TYPE "public"."bed_schedules_shift_enum" AS ENUM(\'day\', \'night\')');
      await queryRunner.query('ALTER TABLE "bed_schedules" ADD "shift" "bed_schedules_shift_enum" NOT NULL');
      await queryRunner.query('ALTER TABLE "bed_schedules" ADD "number_of_beds" integer NOT NULL');
    }

}
