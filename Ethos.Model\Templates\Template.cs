namespace Ethos.Model.Templates;

public abstract record TemplateFormat
{
    public sealed record DateTime(string Format) : TemplateFormat;
    public sealed record Number(string Format) : TemplateFormat;
    public sealed record RefData(string Name, string Attribute) : TemplateFormat;
}

public sealed record TextFormat(
    bool? IsBold = null,
    bool? IsItalic = null,
    bool? IsUnderline = null,
    decimal? FontSize = null,
    string? FontFamily = null,
    string? Color = null
);

public abstract record VariableSource
{
    public sealed record DataPath(string Path) : VariableSource;
    public sealed record Manual(string Value) : VariableSource;
}

public abstract record TemplateSpan
{
    public sealed record TextSpan(string Text, TextFormat? TextFormat) : TemplateSpan;
    public sealed record Field(VariableSource? Source, TemplateFormat Format, TextFormat? TextFormat) : TemplateSpan;
    public sealed record Foreach(string Path, string Variable, IReadOnlyList<TemplateSpan> Paragraphs) : TemplateSpan;
}

public abstract record TemplateBlock
{
    public sealed record Paragraph(IReadOnlyList<TemplateSpan> Spans) : TemplateBlock;
    public sealed record Foreach(string Path, string Variable, List<TemplateBlock> Paragraphs) : TemplateBlock;
}

public sealed record Template(List<TemplateBlock> Paragraphs);