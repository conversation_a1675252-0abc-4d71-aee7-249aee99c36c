using System.Diagnostics;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Ethos.Workflows.HttpClient;
using Ethos.Workflows.Workflow;
using Ethos.Workflows.Workflow.AddNewPatient;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using Xunit.Abstractions;

namespace Ethos.Workflows.Tests;

// public class XUnitLoggerProvider : ILoggerProvider
// {
//     private readonly ITestOutputHelper _out;
//     public XUnitLoggerProvider(ITestOutputHelper output) => _out = output;
//     public ILogger CreateLogger(string category) => new XUnitLogger(_out, category);
//     public void Dispose() { }
// }

public class CustomWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
{
    public CustomWebApplicationFactory()
    {
        // Raw output during construction - does *this* ever show?
        Console.WriteLine("-----> CustomWebApplicationFactory CONSTRUCTOR Running <-----");
        Debug.WriteLine("-----> CustomWebApplicationFactory CONSTRUCTOR Running (Debug) <-----");
    }
    
    private static void Remove<T>(IServiceCollection services)
    {
        var descriptor = services.SingleOrDefault(
            d => d.ServiceType == typeof(T));
        if (descriptor != null) services.Remove(descriptor);
    }
    
    protected override IHostBuilder CreateHostBuilder()
    {
        // Create the default builder BUT DO NOT CALL UseSerilog() here.
        // We want the factory's ConfigureWebHost to handle all logging setup.
        var builder = Host.CreateDefaultBuilder()
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<TStartup>();
                // Point to test-specific appsettings if needed, e.g.:
                // webBuilder.UseSetting("ENVIRONMENT", "Development");
                // webBuilder.ConfigureAppConfiguration((context, conf) => {
                //     conf.AddJsonFile("appsettings.Testing.json", optional: true, reloadOnChange: true);
                // });
            });
        return builder;
    }
    
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        Console.WriteLine("-----> CustomWebApplicationFactory ConfigureWebHost Running <-----");
        Debug.WriteLine("-----> CustomWebApplicationFactory ConfigureWebHost Running (Debug) <-----");
        
        builder.UseEnvironment("Development");
        
        builder.ConfigureLogging(logging =>
        {
            Console.WriteLine("-----> ConfigureLogging Lambda Running <-----");
            logging.ClearProviders();
            logging.SetMinimumLevel(LogLevel.Trace);

            var loggerConfiguration = new LoggerConfiguration()
                // Set Serilog's minimum level (Verbose is equivalent to Trace)
                .MinimumLevel.Verbose()
                // Enrich logs with more context if needed (optional)
                .Enrich.FromLogContext()
                // Write logs to the Console (often visible in Rider/VS test output)
                .WriteTo.Console(
                    outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}",
                    restrictedToMinimumLevel: LogEventLevel.Verbose // Ensure console gets everything
                )
                // Write logs to the Debug output window (another place to check)
                .WriteTo.Debug(
                     outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}",
                    restrictedToMinimumLevel: LogEventLevel.Verbose // Ensure Debug gets everything
                );

            var serilogLogger = loggerConfiguration.CreateLogger();
            logging.AddSerilog(serilogLogger, dispose: true);
            logging.AddConsole();
            logging.AddDebug();
            
            try
            {
                var sp = logging.Services.BuildServiceProvider();
                var logger = sp.GetService<ILogger<CustomWebApplicationFactory<TStartup>>>();
                logger?.LogWarning("-----> Logger obtained inside ConfigureLogging <-----"); // Use Warning to stand out
            }
            catch (Exception ex)
            {
                Console.WriteLine($"XXXXX ERROR building ServiceProvider/getting logger in ConfigureLogging: {ex.Message} XXXXX");
            }
        });
        
        builder.ConfigureServices(services =>
        {
            // Optionally remove the real database context registrations and use an in-memory DB
            // If your app registers something like:
            // services.AddDbContext<AppDbContext>(options => {...});
            //
            // you can override it here:
            //
            // var descriptor = services.SingleOrDefault(
            //     d => d.ServiceType == typeof(DbContextOptions<AppDbContext>));
            // if (descriptor != null) services.Remove(descriptor);
            Remove<DbContextOptions<AppDbContext>>(services);
            Remove<DbContextOptions>(services);
            Remove<IDbContextOptionsConfiguration<AppDbContext>>(services);
            Remove<AppDbContext>(services);
            
            // Console.Error.WriteLine("Services:");
            // for (var i = 0; i < services.Count; i++)
            // {
            //     Console.Error.WriteLine(services[i].ServiceType);
            // }
            
            // Remove<IDatabaseProvider>(services);
            // throw new Exception("Test");
            // Remove<Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure.NpgsqlDbContextOptionsBuilder<AppDbContext>>(services);
            var connection = new SqliteConnection("DataSource=:memory:");
            connection.Open(); // Keep the connection open for the duration of the test scope

            
            services.AddDbContext<AppDbContext>(options =>
            {
                options.UseSqlite(connection);
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Optionally seed test data, etc.

            var sp = services.BuildServiceProvider();
            using (var scope = sp.CreateScope())
            {
                var scopedServices = scope.ServiceProvider;
                var logger = scopedServices.GetRequiredService<ILogger<CustomWebApplicationFactory<TStartup>>>();
                var workflowEngine = scope.ServiceProvider.GetRequiredService<IWorkflowEngine>();

                // Optionally run migrations or seed the in-memory database
            }
            
            var factoryLogger = sp.GetService<ILogger<CustomWebApplicationFactory<TStartup>>>();
            factoryLogger?.LogWarning("##### FACTORY: ConfigureServices completed! In-memory DB should be set up. #####");
        });
    }
}