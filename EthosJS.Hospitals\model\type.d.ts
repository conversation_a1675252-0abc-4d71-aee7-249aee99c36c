declare namespace Components {
    namespace Schemas {
        export interface AvailableScheduleCollectionDto {
            count: number;
            data: AvailableScheduleDto[];
        }
        export interface AvailableScheduleDto {
            facilityId: number;
            studyId: number;
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
            shift: "day" | "night";
        }
        export interface AvailableScheduleFiltersDto {
            facilityId: number;
            studyId: number;
            /**
             * example:
             * 2020-01-01
             */
            dateFrom: string; // date-time
            /**
             * example:
             * 2024-01-01
             */
            dateTo: string; // date-time
            limit?: number;
            shift: "day" | "night";
            equipments?: CreateScheduleEquipmentDto[];
        }
        export interface AvailableStudyCollectionDto {
            count: number;
            data: AvailableStudyDto[];
        }
        export interface AvailableStudyDayCollectionDto {
            count: number;
            data: AvailableStudyDayDto[];
        }
        export interface AvailableStudyDayDto {
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
            isAvailable: boolean;
        }
        export interface AvailableStudyDayFiltersDto {
            facilityId: number;
            /**
             * example:
             * 2020-01-01
             */
            dateFrom: string; // date-time
            /**
             * example:
             * 2024-01-01
             */
            dateTo: string; // date-time
            shift: "day" | "night";
        }
        export interface AvailableStudyDto {
            studyId: number;
            name: string;
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
            shift: "day" | "night";
            isAvailable: boolean;
        }
        export interface AvailableStudyFiltersDto {
            facilityId: number;
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
            shift: "day" | "night";
        }
        export interface BedScheduleCollectionDto {
            count: number;
            data: BedScheduleDto[];
        }
        export interface BedScheduleDto {
            id: number; // int
            createdAt: string; // date-time
            updatedAt: string; // date-time
            facilityId: number;
            facilityName: string;
            dayShiftBeds: number;
            nightShiftBeds: number;
            date: string;
            equipments: BedScheduleEquipmentDto[];
        }
        export interface BedScheduleEquipmentDto {
            equipmentId: number;
            bedScheduleId: number;
            equipmentName: string;
            count: number;
        }
        export interface CityCollectionDto {
            count: number;
            data: CityEntity[];
        }
        export interface CityEntity {
            id: number;
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
            stateId: number;
        }
        export interface ClinicCollectionDto {
            count: number;
            data: ClinicEntity[];
        }
        export interface ClinicEntity {
            id: number;
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
        }
        export interface CreateBedScheduleDto {
            facilityId: number;
            dayShiftBeds: number;
            nightShiftBeds: number;
            equipments?: CreateBedScheduleEquipmentDto[];
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
        }
        export interface CreateBedScheduleEquipmentDto {
            equipmentId: number;
            count: number;
        }
        export interface CreateCityDto {
            name: string;
            stateId: number;
        }
        export interface CreateClinicDto {
            name: string;
        }
        export interface CreateCredentialDto {
            name: string;
            code: string;
            issuedBy?: string;
        }
        export interface CreateEquipmentDto {
            name: string;
        }
        export interface CreateFacilityDto {
            name: string;
            clinicId: number;
            cityId: number;
            capacity: number;
            addressLine1?: string;
            addressLine2?: string;
            zip?: string;
            phone?: string;
            fax?: string;
            equipments?: CreateFacilityEquipmentDto[];
        }
        export interface CreateFacilityEquipmentDto {
            equipmentId: number;
            count: number;
        }
        export interface CreateScheduleDto {
            facilityId: number;
            patient: SchedulePatientDto;
            studyId: number;
            shift: string;
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
            equipments?: CreateScheduleEquipmentDto[];
        }
        export interface CreateScheduleEquipmentDto {
            equipmentId: number;
            count: number;
        }
        export interface CreateStudyCredentialDto {
            credentials: number[];
            stateId?: number;
        }
        export interface CreateStudyDto {
            name: string;
            credentials?: CreateStudyCredentialDto[];
            equipments?: CreateStudyEquipmentDto[];
        }
        export interface CreateStudyEquipmentDto {
            /**
             * id equipment
             */
            equipmentId: number;
            count: number;
        }
        export interface CreateTechnicianCredentialDto {
            credentialId: number;
            validUntil?: string;
        }
        export interface CreateTechnicianDto {
            name: string;
            clinicId: number;
            capacity: number;
            credentials?: CreateTechnicianCredentialDto[];
            standardSchedule?: TechnicianStandardScheduleDto;
        }
        export interface CreateTechnicianScheduleDto {
            technicianId: number;
            facilityId?: number;
            shift: "day" | "night" | "day_off";
            capacity?: number;
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
        }
        export interface CredentialCollectionDto {
            count: number;
            data: CredentialEntity[];
        }
        export interface CredentialEntity {
            id: number;
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
            code: string;
            issuedBy?: string;
        }
        export interface DeleteBedScheduleDto {
            id: number;
        }
        export interface DeleteClinicDto {
            id: number;
        }
        export interface DeleteCredentialDto {
            id: number;
        }
        export interface DeleteEquipmentDto {
            id: number;
        }
        export interface DeleteFacilityDto {
            id: number;
        }
        export interface DeleteScheduleDto {
            id: number;
        }
        export interface DeleteStudyDto {
            id: number;
        }
        export interface DeleteTechnicianDto {
            id: number;
        }
        export interface DeleteTechnicianScheduleDto {
            id: number;
        }
        export interface EquipmentCollectionDto {
            count: number;
            data: EquipmentEntity[];
        }
        export interface EquipmentEntity {
            id: number;
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
        }
        export interface FacilityCollectionDto {
            count: number;
            data: FacilityDto[];
        }
        export interface FacilityDto {
            id: number;
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
            addressLine1?: string;
            addressLine2?: string;
            zip?: string;
            phone?: string;
            fax?: string;
            capacity: number;
            cityId: number;
            cityName: string;
            clinicId: number;
            clinicName: string;
            equipments: FacilityEquipmentDto[];
        }
        export interface FacilityEquipmentDto {
            equipmentId: number;
            facilityId: number;
            equipmentName: string;
            count: number;
        }
        export interface GetConflictsFacilityDto {
            id: number;
            capacity?: number;
            equipments?: CreateFacilityEquipmentDto[];
        }
        export interface GetConflictsTechnicianDto {
            id: number;
            capacity?: number;
            credentials?: CreateTechnicianCredentialDto[];
            standardSchedule?: TechnicianStandardScheduleDto;
        }
        export interface LoginRequest {
            email: string;
            password: string;
        }
        export interface LoginResponse {
            id: string;
            uid: number;
            access_token: string;
            token_type: string;
            rights: string[];
            name: string;
            expires_in?: unknown;
            refresh_token?: unknown;
        }
        export interface RefreshRequest {
            refreshToken: string;
        }
        export interface ScheduleCollectionDto {
            count: number;
            data: ScheduleDto[];
        }
        export interface ScheduleDto {
            id: number; // int
            createdAt: string; // date-time
            updatedAt: string; // date-time
            technicianId: number;
            technicianName: string;
            patientId: number;
            patientName: string;
            studyId: number;
            studyName: string;
            facilityId: number;
            facilityName: string;
            shift: "day" | "night";
            date: string;
            equipments: ScheduleEquipmentDto[];
        }
        export interface ScheduleEquipmentDto {
            scheduleId: number;
            equipmentId: number;
            equipmentName: string;
            count: number;
            studyCount: number;
        }
        export interface SchedulePatientDto {
            id?: number;
            name: string;
        }
        export interface StateCollectionDto {
            count: number;
            data: StateEntity[];
        }
        export interface StateEntity {
            id: number;
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
            code: string;
        }
        export interface StudyCollectionDto {
            count: number;
            data: StudyCollectionItemDto[];
        }
        export interface StudyCollectionItemDto {
            id: number; // int
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
        }
        export interface StudyCredentialDto {
            credentials: StudyCredentialItemDto[];
            studyId: number;
            studyName: string;
            stateId?: number;
            stateName?: string;
        }
        export interface StudyCredentialItemDto {
            credentialId: number;
            credentialName: string;
            credentialCode: string;
        }
        export interface StudyDto {
            id: number; // int
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
            credentials: StudyCredentialDto[];
            equipments: StudyEquipmentDto[];
        }
        export interface StudyEquipmentDto {
            studyId: number;
            equipmentId: number;
            equipmentName: string;
            count: number;
        }
        export interface TechnicianCollectionDto {
            count: number;
            data: TechnicianDto[];
        }
        export interface TechnicianCredentialDto {
            credentialId: number;
            credentialName: string;
            technicianId: number;
            validUntil?: string;
        }
        export interface TechnicianDto {
            id: number; // int
            createdAt: string; // date-time
            updatedAt: string; // date-time
            name: string;
            capacity: number;
            standardSchedule: TechnicianStandardScheduleDto;
            clinicId: number;
            clinicName: string;
            credentials: TechnicianCredentialDto[];
        }
        export interface TechnicianScheduleCollectionDto {
            count: number;
            data: TechnicianScheduleDto[];
        }
        export interface TechnicianScheduleDto {
            id: number; // int
            createdAt: string; // date-time
            updatedAt: string; // date-time
            technicianId: number;
            technicianName: string;
            facilityId?: number;
            shift: "day" | "night" | "day_off";
            capacity?: number;
            date: string;
        }
        export interface TechnicianStandardScheduleDto {
            mon?: TechnicianStandardScheduleItemDto;
            tue?: TechnicianStandardScheduleItemDto;
            wed?: TechnicianStandardScheduleItemDto;
            thu?: TechnicianStandardScheduleItemDto;
            fri?: TechnicianStandardScheduleItemDto;
            sat?: TechnicianStandardScheduleItemDto;
            sun?: TechnicianStandardScheduleItemDto;
        }
        export interface TechnicianStandardScheduleItemDto {
            shift: "day" | "night" | "day_off";
            capacity?: number;
            facilityId?: number;
        }
        export interface UpdateClinicDto {
            id: number;
            name: string;
        }
        export interface UpdateCredentialDto {
            id: number;
            name?: string;
            code?: string;
            issuedBy?: string;
        }
        export interface UpdateEquipmentDto {
            id: number;
            name: string;
        }
        export interface UpdateFacilityDto {
            id: number;
            name?: string;
            capacity?: number;
            cityId?: number;
            addressLine1?: string;
            addressLine2?: string;
            zip?: string;
            phone?: string;
            fax?: string;
            equipments?: CreateFacilityEquipmentDto[];
        }
        export interface UpdateStudyDto {
            id: number;
            name?: string;
            credentials?: CreateStudyCredentialDto[];
            equipments?: CreateStudyEquipmentDto[];
        }
        export interface UpdateTechnicianDto {
            id: number;
            name?: string;
            capacity?: number;
            credentials?: CreateTechnicianCredentialDto[];
            standardSchedule?: TechnicianStandardScheduleDto;
        }
        export interface UpsertBedScheduleDto {
            items: UpsertBedScheduleItemDto[];
        }
        export interface UpsertBedScheduleItemDto {
            id?: number;
            facilityId: number;
            dayShiftBeds: number;
            nightShiftBeds: number;
            equipments?: CreateBedScheduleEquipmentDto[];
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
        }
        export interface UpsertBedScheduleResultDto {
            items: UpsertBedScheduleResultItemDto[];
        }
        export interface UpsertBedScheduleResultItemDto {
            item?: BedScheduleDto;
            sourceItem?: UpsertBedScheduleItemDto;
            error?: string;
        }
        export interface UpsertTechnicianScheduleDto {
            items: UpsertTechnicianScheduleItemDto[];
        }
        export interface UpsertTechnicianScheduleItemDto {
            id?: number;
            technicianId: number;
            facilityId?: number;
            shift: "day" | "night" | "day_off";
            capacity?: number;
            /**
             * example:
             * 2020-01-01
             */
            date: string; // date-time
        }
        export interface UpsertTechnicianScheduleResultDto {
            items: UpsertTechnicianScheduleResultItemDto[];
        }
        export interface UpsertTechnicianScheduleResultItemDto {
            item?: TechnicianScheduleDto;
            sourceItem?: UpsertTechnicianScheduleItemDto;
            error?: string;
        }
    }
}
declare namespace Paths {
    namespace AuthControllerLogin {
        export type RequestBody = Components.Schemas.LoginRequest;
        namespace Responses {
            export type $200 = Components.Schemas.LoginResponse;
            export interface $403 {
            }
            export interface $406 {
            }
        }
    }
    namespace AuthControllerRefreshTokens {
        export type RequestBody = Components.Schemas.RefreshRequest;
        namespace Responses {
            export type $200 = Components.Schemas.LoginResponse;
            export interface $401 {
            }
            export interface $403 {
            }
        }
    }
    namespace AuthControllerUserLogin {
        export type RequestBody = Components.Schemas.LoginRequest;
        namespace Responses {
            export type $200 = Components.Schemas.LoginResponse;
            export interface $403 {
            }
            export interface $406 {
            }
        }
    }
    namespace AuthControllerUserLogout {
        namespace Responses {
            export interface $204 {
            }
            export interface $401 {
            }
        }
    }
    namespace AvailableControllerGetAvailableSchedules {
        export type RequestBody = Components.Schemas.AvailableScheduleFiltersDto;
        namespace Responses {
            export type $200 = Components.Schemas.AvailableScheduleCollectionDto;
        }
    }
    namespace AvailableControllerGetAvailableStudies {
        export type RequestBody = Components.Schemas.AvailableStudyFiltersDto;
        namespace Responses {
            export type $200 = Components.Schemas.AvailableStudyCollectionDto;
        }
    }
    namespace AvailableControllerGetAvailableStudyDays {
        export type RequestBody = Components.Schemas.AvailableStudyDayFiltersDto;
        namespace Responses {
            export type $200 = Components.Schemas.AvailableStudyDayCollectionDto;
        }
    }
    namespace BedScheduleControllerCreate {
        export type RequestBody = Components.Schemas.CreateBedScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.BedScheduleDto;
        }
    }
    namespace BedScheduleControllerDelete {
        export type RequestBody = Components.Schemas.DeleteBedScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.BedScheduleDto;
        }
    }
    namespace BedScheduleControllerGetById {
        namespace Parameters {
            export type BedScheduleId = number;
        }
        export interface PathParameters {
            bedScheduleId: Parameters.BedScheduleId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.BedScheduleDto;
        }
    }
    namespace BedScheduleControllerList {
        namespace Parameters {
            export type ClinicId = number;
            export type DateFrom = string; // date-time
            export type DateTo = string; // date-time
            export type FacilityIds = string;
            export type Limit = number;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "date";
            export type Shift = "day" | "night";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            facilityIds?: Parameters.FacilityIds;
            clinicId?: Parameters.ClinicId;
            shift?: Parameters.Shift;
            dateFrom?: Parameters.DateFrom /* date-time */;
            dateTo?: Parameters.DateTo /* date-time */;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.BedScheduleCollectionDto;
        }
    }
    namespace BedScheduleControllerUpsert {
        export type RequestBody = Components.Schemas.UpsertBedScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.UpsertBedScheduleResultDto;
        }
    }
    namespace CityControllerCreate {
        export type RequestBody = Components.Schemas.CreateCityDto;
        namespace Responses {
            export type $200 = Components.Schemas.CityEntity;
        }
    }
    namespace CityControllerGetById {
        namespace Parameters {
            export type CityId = number;
        }
        export interface PathParameters {
            cityId: Parameters.CityId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CityEntity;
        }
    }
    namespace CityControllerList {
        namespace Parameters {
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
            export type StateId = number;
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            stateId?: Parameters.StateId;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CityCollectionDto;
        }
    }
    namespace ClinicControllerCreate {
        export type RequestBody = Components.Schemas.CreateClinicDto;
        namespace Responses {
            export type $200 = Components.Schemas.ClinicEntity;
        }
    }
    namespace ClinicControllerDelete {
        export type RequestBody = Components.Schemas.DeleteClinicDto;
        namespace Responses {
            export type $200 = Components.Schemas.ClinicEntity;
        }
    }
    namespace ClinicControllerGetById {
        namespace Parameters {
            export type ClinicId = number;
        }
        export interface PathParameters {
            clinicId: Parameters.ClinicId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ClinicEntity;
        }
    }
    namespace ClinicControllerList {
        namespace Parameters {
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ClinicCollectionDto;
        }
    }
    namespace ClinicControllerUpdate {
        export type RequestBody = Components.Schemas.UpdateClinicDto;
        namespace Responses {
            export type $200 = Components.Schemas.ClinicEntity;
        }
    }
    namespace CredentialControllerCreate {
        export type RequestBody = Components.Schemas.CreateCredentialDto;
        namespace Responses {
            export type $200 = Components.Schemas.CredentialEntity;
        }
    }
    namespace CredentialControllerDelete {
        export type RequestBody = Components.Schemas.DeleteCredentialDto;
        namespace Responses {
            export type $200 = Components.Schemas.CredentialEntity;
        }
    }
    namespace CredentialControllerGetById {
        namespace Parameters {
            export type CredentialId = number;
        }
        export interface PathParameters {
            credentialId: Parameters.CredentialId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CredentialEntity;
        }
    }
    namespace CredentialControllerList {
        namespace Parameters {
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.CredentialCollectionDto;
        }
    }
    namespace CredentialControllerUpdate {
        export type RequestBody = Components.Schemas.UpdateCredentialDto;
        namespace Responses {
            export type $200 = Components.Schemas.CredentialEntity;
        }
    }
    namespace EquipmentControllerCreate {
        export type RequestBody = Components.Schemas.CreateEquipmentDto;
        namespace Responses {
            export type $200 = Components.Schemas.EquipmentEntity;
        }
    }
    namespace EquipmentControllerDelete {
        export type RequestBody = Components.Schemas.DeleteEquipmentDto;
        namespace Responses {
            export type $200 = Components.Schemas.EquipmentEntity;
        }
    }
    namespace EquipmentControllerGetById {
        namespace Parameters {
            export type EquipmentId = number;
        }
        export interface PathParameters {
            equipmentId: Parameters.EquipmentId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.EquipmentEntity;
        }
    }
    namespace EquipmentControllerList {
        namespace Parameters {
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.EquipmentCollectionDto;
        }
    }
    namespace EquipmentControllerUpdate {
        export type RequestBody = Components.Schemas.UpdateEquipmentDto;
        namespace Responses {
            export type $200 = Components.Schemas.EquipmentEntity;
        }
    }
    namespace FacilityControllerCreate {
        export type RequestBody = Components.Schemas.CreateFacilityDto;
        namespace Responses {
            export type $200 = Components.Schemas.FacilityDto;
        }
    }
    namespace FacilityControllerDelete {
        export type RequestBody = Components.Schemas.DeleteFacilityDto;
        namespace Responses {
            export type $200 = Components.Schemas.FacilityDto;
        }
    }
    namespace FacilityControllerGetById {
        namespace Parameters {
            export type FacilityId = number;
        }
        export interface PathParameters {
            facilityId: Parameters.FacilityId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.FacilityDto;
        }
    }
    namespace FacilityControllerGetConflicts {
        export type RequestBody = Components.Schemas.GetConflictsFacilityDto;
        namespace Responses {
            export type $200 = Components.Schemas.FacilityDto;
        }
    }
    namespace FacilityControllerList {
        namespace Parameters {
            export type ClinicId = number;
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            clinicId?: Parameters.ClinicId;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.FacilityCollectionDto;
        }
    }
    namespace FacilityControllerUpdate {
        export type RequestBody = Components.Schemas.UpdateFacilityDto;
        namespace Responses {
            export type $200 = Components.Schemas.FacilityDto;
        }
    }
    namespace HealthControllerCheckHealth {
        namespace Responses {
            export interface $200 {
            }
        }
    }
    namespace ScheduleControllerCreate {
        export type RequestBody = Components.Schemas.CreateScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.ScheduleDto;
        }
    }
    namespace ScheduleControllerDelete {
        export type RequestBody = Components.Schemas.DeleteScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.ScheduleDto;
        }
    }
    namespace ScheduleControllerGetById {
        namespace Parameters {
            export type ScheduleId = number;
        }
        export interface PathParameters {
            scheduleId: Parameters.ScheduleId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ScheduleDto;
        }
    }
    namespace ScheduleControllerList {
        namespace Parameters {
            export type ClinicId = number;
            export type DateFrom = string; // date-time
            export type DateTo = string; // date-time
            export type Dates = string;
            export type FacilityIds = string;
            export type Limit = number;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "date";
            export type PatientId = number;
            export type Shift = "day" | "night";
            export type StudyId = number;
            export type TechnicianId = number;
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            facilityIds?: Parameters.FacilityIds;
            dates?: Parameters.Dates;
            clinicId?: Parameters.ClinicId;
            technicianId?: Parameters.TechnicianId;
            patientId?: Parameters.PatientId;
            studyId?: Parameters.StudyId;
            shift?: Parameters.Shift;
            dateFrom?: Parameters.DateFrom /* date-time */;
            dateTo?: Parameters.DateTo /* date-time */;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.ScheduleCollectionDto;
        }
    }
    namespace StateControllerGetById {
        namespace Parameters {
            export type StateId = number;
        }
        export interface PathParameters {
            stateId: Parameters.StateId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.StateEntity;
        }
    }
    namespace StateControllerList {
        namespace Parameters {
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.StateCollectionDto;
        }
    }
    namespace StudyControllerCreate {
        export type RequestBody = Components.Schemas.CreateStudyDto;
        namespace Responses {
            export type $200 = Components.Schemas.StudyDto;
        }
    }
    namespace StudyControllerDelete {
        export type RequestBody = Components.Schemas.DeleteStudyDto;
        namespace Responses {
            export type $200 = Components.Schemas.StudyDto;
        }
    }
    namespace StudyControllerGetById {
        namespace Parameters {
            export type StudyId = number;
        }
        export interface PathParameters {
            studyId: Parameters.StudyId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.StudyDto;
        }
    }
    namespace StudyControllerList {
        namespace Parameters {
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.StudyCollectionDto;
        }
    }
    namespace StudyControllerUpdate {
        export type RequestBody = Components.Schemas.UpdateStudyDto;
        namespace Responses {
            export type $200 = Components.Schemas.StudyDto;
        }
    }
    namespace TechnicianControllerCreate {
        export type RequestBody = Components.Schemas.CreateTechnicianDto;
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianDto;
        }
    }
    namespace TechnicianControllerDelete {
        export type RequestBody = Components.Schemas.DeleteTechnicianDto;
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianDto;
        }
    }
    namespace TechnicianControllerGetById {
        namespace Parameters {
            export type TechnicianId = number;
        }
        export interface PathParameters {
            technicianId: Parameters.TechnicianId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianDto;
        }
    }
    namespace TechnicianControllerGetConflicts {
        export type RequestBody = Components.Schemas.GetConflictsTechnicianDto;
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianDto;
        }
    }
    namespace TechnicianControllerList {
        namespace Parameters {
            export type ClinicId = number;
            export type Limit = number;
            export type Name = string;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt" | "name";
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            name?: Parameters.Name;
            clinicId?: Parameters.ClinicId;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianCollectionDto;
        }
    }
    namespace TechnicianControllerUpdate {
        export type RequestBody = Components.Schemas.UpdateTechnicianDto;
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianDto;
        }
    }
    namespace TechnicianScheduleControllerCreate {
        export type RequestBody = Components.Schemas.CreateTechnicianScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianScheduleDto;
        }
    }
    namespace TechnicianScheduleControllerDelete {
        export type RequestBody = Components.Schemas.DeleteTechnicianScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianScheduleDto;
        }
    }
    namespace TechnicianScheduleControllerGetById {
        namespace Parameters {
            export type TechnicianScheduleId = number;
        }
        export interface PathParameters {
            technicianScheduleId: Parameters.TechnicianScheduleId;
        }
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianScheduleDto;
        }
    }
    namespace TechnicianScheduleControllerList {
        namespace Parameters {
            export type ClinicId = number;
            export type DateFrom = string; // date-time
            export type DateTo = string; // date-time
            export type FacilityIds = string;
            export type Limit = number;
            export type Offset = number;
            export type OrderDirection = "ASC" | "DESC";
            export type OrderField = "createdAt" | "updatedAt";
            export type Shift = "day" | "night" | "day_off";
            export type TechnicianId = number;
        }
        export interface QueryParameters {
            offset?: Parameters.Offset;
            limit?: Parameters.Limit;
            orderDirection?: Parameters.OrderDirection;
            technicianId?: Parameters.TechnicianId;
            facilityIds?: Parameters.FacilityIds;
            clinicId?: Parameters.ClinicId;
            shift?: Parameters.Shift;
            dateFrom?: Parameters.DateFrom /* date-time */;
            dateTo?: Parameters.DateTo /* date-time */;
            orderField?: Parameters.OrderField;
        }
        namespace Responses {
            export type $200 = Components.Schemas.TechnicianScheduleCollectionDto;
        }
    }
    namespace TechnicianScheduleControllerUpsert {
        export type RequestBody = Components.Schemas.UpsertTechnicianScheduleDto;
        namespace Responses {
            export type $200 = Components.Schemas.UpsertTechnicianScheduleResultDto;
        }
    }
}
