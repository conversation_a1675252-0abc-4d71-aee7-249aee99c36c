using System.Collections.Immutable;
using System.Text.Json.Nodes;
using Ethos.Model.Scheduling;
using Ethos.ReferenceData.Client;

namespace Ethos.Workflows.Api.Analysis;

using System.Collections;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

// NOTE: Assume all your custom attributes (CheckString, CheckRealNumber, etc.) 
// and the IInputDto interface are accessible from this file.

/// <summary>
/// Represents a single, extracted validation rule for a property.
/// </summary>
/// <param name="JsonPath">The JSON path to the property (e.g., 'customer.address.street').</param>
/// <param name="DataType">The C# data type of the property.</param>
/// <param name="Rule">A human-readable description of the validation rule.</param>
/// <param name="IsHard">True for hard validation (errors), False for soft validation (warnings).</param>
public record ValidationRuleInfo(string JsonPath, string DataType, string Rule, bool IsHard);

public static class ValidationRuleExtractor
{
    private static readonly NullabilityInfoContext NullabilityCtx = new();

    /// <summary>
    /// Extracts all validation rules from a given root type.
    /// </summary>
    /// <param name="rootType">The class type to analyze.</param>
    /// <returns>A list of validation rule information records.</returns>
    public static List<ValidationRuleInfo> ExtractValidationRules(Type rootType, Action<string> print)
    {
        var rules = new List<ValidationRuleInfo>();
        ExtractRulesRecursive(rootType, "", rules, print);
        return rules;
    }

    private static void ExtractRulesRecursive(Type type, string currentPath, List<ValidationRuleInfo> rules, Action<string> print)
    {
        if (!DataModel.IsDtoType(type)) return;
        
        // print(type.ToString());
        // Thread.Sleep(100);
        
        foreach (var prop in type.GetProperties(BindingFlags.Instance | BindingFlags.Public))
        {
            // Determine the JSON property name
            var jsonPropName = prop.GetCustomAttribute<JsonPropertyNameAttribute>()?.Name
                ?? JsonNamingPolicy.CamelCase.ConvertName(prop.Name);
            
            var fullPath = string.IsNullOrEmpty(currentPath) ? jsonPropName : $"{currentPath}.{jsonPropName}";
            
            var rulesFoundForProperty = false;
            
            // --- Rule: Nullability (Required) ---
            var nullabilityInfo = NullabilityCtx.Create(prop);
            if (nullabilityInfo.WriteState == NullabilityState.NotNull)
            {
                rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), "value != null", true));
                rulesFoundForProperty = true;
            }

            // --- Rule: CheckStringAttribute ---
            if (prop.GetCustomAttribute<CheckStringAttribute>() is { } strAttr)
            {
                if (strAttr.HardMinLength > 1) // Default is 1, so only show if it's more restrictive
                {
                    rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"len(value) >= {strAttr.HardMinLength}", true));
                    rulesFoundForProperty = true;
                }
                if (strAttr.HardMaxLength < int.MaxValue)
                {
                    rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"len(value) <= {strAttr.HardMaxLength}", true));
                    rulesFoundForProperty = true;
                }
                if (!string.IsNullOrEmpty(strAttr.HardPattern))
                {
                    var escapedPattern = strAttr.HardPattern.Replace("'", "\\'");
                    rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"matches(value, '{escapedPattern}')", true));
                    rulesFoundForProperty = true;
                }
            }

            // --- Rule: CheckRealNumberAttribute ---
            if (prop.GetCustomAttribute<CheckRealNumberAttribute>() is { } realAttr)
            {
                if (realAttr.HardMin > double.NegativeInfinity)
                {
                    rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"value >= {realAttr.HardMin}", true));
                    rulesFoundForProperty = true;
                }
                if (realAttr.HardMax < double.PositiveInfinity)
                {
                    rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"value <= {realAttr.HardMax}", true));
                    rulesFoundForProperty = true;
                }
                if (realAttr.SoftMin > double.NegativeInfinity)
                {
                    rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"value >= {realAttr.SoftMin}", false));
                    rulesFoundForProperty = true;
                }
                if (realAttr.SoftMax < double.PositiveInfinity)
                {
                    rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"value <= {realAttr.SoftMax}", false));
                    rulesFoundForProperty = true;
                }
            }
            
            // --- Rule: CheckReferenceDataAttribute ---
            if (prop.GetCustomAttribute<CheckReferenceDataAttribute>() is { } refAttr)
            {
                var refName = refAttr.Name.Name;
                if (refName.EndsWith("Entity", StringComparison.OrdinalIgnoreCase))
                {
                    refName = refName[..^"Entity".Length]; // Remove "Entity" suffix
                }
                rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), $"isRefData(value, '{refName}')", true));
                rulesFoundForProperty = true;
            }
            
            // --- Add placeholder for properties with no rules ---
            if (!rulesFoundForProperty)
            {
                rules.Add(new ValidationRuleInfo(fullPath, PrettyTypeName(prop.PropertyType), "true", false));
            }
            
            // --- Recursion for Complex Types and Collections ---
            Type propertyType = prop.PropertyType;
            Type? itemType = GetEnumerableItemType(propertyType);

            if (itemType != null) // It's a collection
            {
                // Note: We only recurse if the item type is a complex object.
                // We assume primitive lists (e.g., List<string>) have validation attributes on the property itself.
                if (IsComplexType(itemType))
                {
                    ExtractRulesRecursive(itemType, $"{fullPath}[*]", rules, print);
                }
            }
            else if (IsComplexType(propertyType)) // It's a nested object
            {
                 ExtractRulesRecursive(propertyType, fullPath, rules, print);
            }
        }
    }

    #region Helper Methods
    
    private static Type? GetEnumerableItemType(Type type)
    {
        if (type == typeof(string)) return null; // string is an IEnumerable<char>, but we treat it as a primitive.
        return type.IsGenericType && typeof(IEnumerable).IsAssignableFrom(type) 
            ? type.GetGenericArguments()[0] 
            : null;
    }

    private static bool IsComplexType(Type type)
    {
        // Considers a type "complex" if it's a class/struct that is not a string or primitive.
        // This is more robust than checking for a specific namespace.
        return type.IsClass && type != typeof(string) || type.IsValueType && !type.IsPrimitive && !type.IsEnum;
    }

    private static string PrettyTypeName(Type t)
    {
        if (!t.IsGenericType)
            return t.Name;

        if (t.GetGenericTypeDefinition() == typeof(IReadOnlyList<>))
        {
            return $"[{PrettyTypeName(t.GetGenericArguments()[0])}]";
        } else if (t.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            return $"{PrettyTypeName(t.GetGenericArguments()[0])}?";
        }

        var sb = new StringBuilder();
        sb.Append(t.Name.AsSpan(0, t.Name.IndexOf('`')));
        sb.Append('<');
        sb.Append(string.Join(", ", t.GetGenericArguments().Select(PrettyTypeName)));
        sb.Append('>');
        return sb.ToString();
    }
    
    #endregion
}