namespace Ethos.Workflows.Files;

/// <summary>
/// Interface for file validation logic (e.g., magic byte checks).
/// </summary>
public interface IFileValidationService
{
    /// <summary>
    /// Verifies if the file content matches the expected MIME type based on its signature.
    /// </summary>
    /// <param name="fileStream">A readable stream of the file content. The stream position may be modified.</param>
    /// <param name="declaredMimeType">The MIME type declared by the client.</param>
    /// <param name="allowedMimeTypes">The list of permitted MIME types for this upload context.</param>
    /// <returns>True if the file signature is valid and matches an allowed type, false otherwise.</returns>
    Task<bool> IsSignatureValidAsync(Stream fileStream, string declaredMimeType, IEnumerable<string> allowedMimeTypes);
}

public class FileValidationService : IFileValidationService
{
    private readonly ILogger<FileValidationService> _logger;

    // Dictionary mapping magic bytes (hex representation) to MIME types.
    // Add more entries as needed. Keys are hex strings of byte sequences.
    private static readonly IReadOnlyDictionary<string, string> _fileSignatures =
        new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "25504446", "application/pdf" },   // %PDF
            { "FFD8FF", "image/jpeg" },          // JPEG
            { "89504E470D0A1A0A", "image/png" }, // PNG
            { "47494638", "image/gif" },         // GIF (GIF87a or GIF89a)
            { "424D", "image/bmp" },             // BMP
            { "49492A00", "image/tiff" },        // TIFF (Little Endian)
            { "4D4D002A", "image/tiff" },        // TIFF (Big Endian)
            { "504B0304", "application/zip" }    // ZIP PK format (used by DOCX, XLSX, etc.)
            // Note: DOCX/XLSX are ZIP files. More specific validation requires inspecting ZIP contents.
            // For simplicity here, we identify them as application/zip.
            // A more robust implementation would unzip and check internal structures/rels.
        };

    // Maximum header size to read for signature validation. Adjust if needed.
    private const int MaxHeaderBytes = 20;

    public FileValidationService(ILogger<FileValidationService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Verifies if the file content signature matches an allowed MIME type.
    /// </summary>
    /// <param name="fileStream">A readable and preferably seekable stream of the file content.</param>
    /// <param name="declaredMimeType">The MIME type declared by the client (used for logging comparison).</param>
    /// <param name="allowedMimeTypes">The list of permitted MIME types for this upload context.</param>
    /// <returns>True if the file signature matches a known and allowed type, false otherwise.</returns>
    public async Task<bool> IsSignatureValidAsync(Stream fileStream, string declaredMimeType, IEnumerable<string> allowedMimeTypes)
    {
        if (!fileStream.CanRead)
        {
            _logger.LogError("Cannot validate file signature: Stream is not readable.");
            return false; // Cannot validate if not readable
        }

        if (!fileStream.CanSeek)
        {
            // Security Risk: Cannot perform magic byte check on non-seekable stream.
            // Falling back to trusting the declared MIME type is less secure.
            // For higher security, consider rejecting non-seekable streams or buffering them first.
            _logger.LogWarning("Cannot perform signature validation: Stream is not seekable. Falling back to validating declared MIME type {DeclaredMimeType} only.", declaredMimeType);
            return allowedMimeTypes.Contains(declaredMimeType.ToLowerInvariant());
        }

        string? detectedMimeType = null;
        long originalPosition = fileStream.Position;
        byte[] headerBytes = new byte[MaxHeaderBytes];
        int bytesRead = 0;

        try
        {
            // Read header bytes
            bytesRead = await fileStream.ReadAsync(headerBytes, 0, MaxHeaderBytes);
            if (bytesRead == 0)
            {
                _logger.LogWarning("Cannot validate file signature: Stream is empty.");
                return false; // Empty file
            }

            // Convert header to hex string for dictionary lookup
            string headerHex = BitConverter.ToString(headerBytes, 0, bytesRead).Replace("-", "");

            // Find matching signature
            foreach (var signature in _fileSignatures)
            {
                if (headerHex.StartsWith(signature.Key, StringComparison.OrdinalIgnoreCase))
                {
                    detectedMimeType = signature.Value;
                    _logger.LogDebug("Detected file signature for MIME type: {DetectedMimeType}", detectedMimeType);
                    break;
                }
            }

            if (detectedMimeType == null)
            {
                _logger.LogWarning("File signature validation failed: Unknown file type signature. Header hex: {HeaderHex}", headerHex);
                return false; // Fail if signature is not recognized
            }

            // Log if declared type mismatches detected type
            if (!string.Equals(declaredMimeType, detectedMimeType, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("Declared MIME type '{DeclaredMimeType}' mismatches detected signature type '{DetectedMimeType}'.", declaredMimeType, detectedMimeType);
                // Note: We proceed based on the *detected* type for security.
            }

            // Check if the *detected* type is allowed
            bool isAllowed = allowedMimeTypes.Contains(detectedMimeType.ToLowerInvariant());
            if (!isAllowed)
            {
                 _logger.LogWarning("File signature validation failed: Detected type '{DetectedMimeType}' is not in the allowed list: [{AllowedList}]",
                    detectedMimeType, string.Join(", ", allowedMimeTypes));
            }

            // Handle specific case: Detected as ZIP, check if allowed types include DOCX/XLSX etc.
            // A more robust check would involve inspecting the ZIP contents.
            if (detectedMimeType == "application/zip" && !isAllowed)
            {
                var officeTypes = new[] { "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" };
                if (allowedMimeTypes.Any(at => officeTypes.Contains(at.ToLowerInvariant())))
                {
                    _logger.LogInformation("Detected ZIP signature, and Office document types are allowed. Allowing file based on declared type '{DeclaredMimeType}' potentially being DOCX/XLSX.", declaredMimeType);
                    // Allow if declared type is an allowed Office type, even though detected as zip
                    isAllowed = officeTypes.Contains(declaredMimeType.ToLowerInvariant()) && allowedMimeTypes.Contains(declaredMimeType.ToLowerInvariant());
                }
            }


            return isAllowed;

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during file signature validation.");
            return false; // Fail on error
        }
        finally
        {
            // IMPORTANT: Reset stream position to original
            if (fileStream.CanSeek)
            {
                fileStream.Position = originalPosition;
            }
        }
    }
}