apiVersion: apps/v1
kind: Deployment
metadata:
  name: ethos-workflows
  namespace: ethos-ns-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ethos-workflows
  template:
    metadata:
      labels:
        app: ethos-workflows
    spec:
      containers:
      - name: ethos-workflows
        image: ethoscrdev.azurecr.io/ethos-workflows:2025.05.1
        imagePullPolicy: Always
        ports:
        - containerPort: 8080 
        env:
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              key: connection-string
              name: db-connection-secret
              optional: false
        - name: ASPNETCORE_URLS
          value: "http://*:8080"
        - name: VITE_API_URL
          value: "http://localhost:4000"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
      restartPolicy: Always