﻿namespace Ethos.ReferenceData
{
    /// <summary>
    /// Type of the value in a reference list.
    /// </summary>
    public enum ReferenceDataListType
    {
        /// <summary>
        /// Type is string (stored as a <see cref="string"/>).
        /// </summary>
        String,

        /// <summary>
        /// Type is boolean (stored as a <see cref="bool"/>).
        /// </summary>
        <PERSON><PERSON><PERSON>,

        /// <summary>
        /// Type is date/time (stored as a <see cref="DateTimeOffset"/>).
        /// </summary>
        DateTime,

        /// <summary>
        /// Type is an integer (stored as a <see cref="long"/>).
        /// </summary>
        Integer,

        /// <summary>
        /// Type is float (stored as a <see cref="double"/>).
        /// </summary>
        Float
    }

    /// <summary>
    /// Determines the creation context for a specific key in a data set.
    /// </summary>
    public enum CreationContext
    {
        /// <summary>
        /// The system created the key.
        /// </summary>
        System,

        /// <summary>
        /// The key was imported from a file.
        /// </summary>
        Import,

        /// <summary>
        /// The key was created via an API call.
        /// </summary>
        Update,

        /// <summary>
        /// The key was created from a trigger associated with another key in the same or a different data set.
        /// </summary>
        Trigger
    }

    /// <summary>
    /// Determines special behavior for a key in a data set when the key is selected as an option.
    /// </summary>
    public enum KeyTriggerBehavior
    {
        /// <summary>
        /// No special behavior.
        /// </summary>
        None,

        /// <summary>
        /// The key allows a new value to be added to the set but does not require it.
        /// </summary>
        Allow,

        /// <summary>
        /// The key requires that a new value be added to the set.
        /// </summary>
        Require
    }

    /// <summary>
    /// Determines the fields presented to the user when creating a new key in a data set.
    /// </summary>
    public enum SchemaBehavior
    {
        /// <summary>
        /// All fields present on any key within the set will be presented to the user.
        /// </summary>
        All,

        /// <summary>
        /// Only the raw value of the key will be presented to the user.
        /// </summary>
        KeyOnly,
    }
}
