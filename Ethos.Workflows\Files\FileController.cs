using System.Collections.Immutable;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using Ethos.Model;
using Ethos.Utilities;
using Ethos.Workflows.Api;
using Ethos.Workflows.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace Ethos.Workflows.Files;

public sealed record RequestUploadTokenDto
{
    public required EntityType ContextEntityType { get; set; }
    public required Guid ContextEntityId { get; set; }
    public required string Purpose { get; set; }
}

public sealed record UploadTokenResponseDto
{
    public required string UploadToken { get; set; }
    public required string UploadUrl { get; set; } // e.g., "/api/file/upload"
}

public sealed record UploadResponseDto
{
    public required Guid FileId { get; set; }
    public required FileStatus Status { get; set; }
    public required string OriginalFileName { get; set; }
    public required long FileSize { get; set; }
    public required string MimeType { get; set; }
}

public sealed record FileStatusResponseDto
{
    public required Guid FileId { get; set; }
    public required FileStatus Status { get; set; }
    public required string OriginalFileName { get; set; }
    public required long FileSize { get; set; }
    public required string MimeType { get; set; }
    public required string? ThumbnailUrl { get; set; } // URL to fetch the thumbnail
    public required JsonNode? Failure { get; set; }
    public required DateTime UploadTimestamp { get; set; }
    public required DateTime LastUpdateTimestamp { get; set; }
    public required EntityType ContextEntityType { get; set; }
    public required Guid ContextEntityId { get; set; }
    public required string Purpose { get; set; }
    public required bool? MalwareScanPassed { get; set; }
}

public class FileUploadOptions
{
    public const string SectionName = "FileUpload";
    // Example: Define allowed types per purpose or globally
    public Dictionary<string, List<string>> AllowedMimeTypesByPurpose { get; set; } = new();
    public List<string> DefaultAllowedMimeTypes { get; set; } = ["application/pdf", "image/jpeg", "image/png"];
    public long DefaultMaxFileSize { get; set; } = 10 * 1024 * 1024; // 10 MB default
    public int TokenExpiryMinutes { get; set; } = 5; // Short expiry for upload tokens
    public Dictionary<string, long> MaxFileSizeByPurpose { get; set; } = new();
}

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FileController : ControllerBase
{
    private readonly IUploadTokenService _tokenService;
    private readonly IFileStorageService _storageService;
    private readonly AppDbContext _dbContext;
    private readonly ILogger<FileController> _logger;
    private readonly IOptions<FileUploadOptions> _fileUploadOptions;
    private readonly IFileValidationService _fileValidationService;

    public FileController(
        IUploadTokenService tokenService,
        IFileStorageService storageService,
        AppDbContext dbContext,
        IOptions<FileUploadOptions> fileUploadOptions,
        IFileValidationService fileValidationService,
        ILogger<FileController> logger)
    {
        _tokenService = tokenService;
        _storageService = storageService;
        _dbContext = dbContext;
        _fileUploadOptions = fileUploadOptions;
        _fileValidationService = fileValidationService;
        _logger = logger;
    }

    [HttpPost("request-upload-token")]
    [ProducesResponseType(typeof(UploadTokenResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> RequestUploadToken([FromBody] RequestUploadTokenDto request)
    {
        // This is a prototypical upload controller.
        // In reality, you would need a more specific upload token endpoint for each purpose.
        // FIXME: Remove this whole endpoint!
        
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized("User identifier not found in token.");
        }

        bool isAuthorized = await CheckUploadPermissionAsync(userId, request.ContextEntityType, request.ContextEntityId, request.Purpose);
        if (!isAuthorized)
        {
            _logger.LogWarning("User {UserId} forbidden to request upload token for {EntityType}/{EntityId}/{Purpose}",
                userId, request.ContextEntityType, request.ContextEntityId, request.Purpose);
            return Forbid();
        }

        var options = _fileUploadOptions.Value;
        var allowedMimeTypes = options.AllowedMimeTypesByPurpose.TryGetValue(request.Purpose, out var purposeTypes)
            ? purposeTypes : options.DefaultAllowedMimeTypes;
        var maxFileSize = options.MaxFileSizeByPurpose.TryGetValue(request.Purpose, out var purposeSize)
            ? purposeSize : options.DefaultMaxFileSize;

        var claims = new UploadTokenClaims
        {
            UserId = userId,
            ContextEntityType = request.ContextEntityType,
            ContextEntityId = request.ContextEntityId,
            Purpose = request.Purpose,
            AllowedMimeTypes = allowedMimeTypes,
            MaxFileSize = maxFileSize
        };

        // Access expiry from the specific implementation's stored options
        var tokenExpiry = TimeSpan.FromMinutes(_fileUploadOptions.Value.TokenExpiryMinutes);

        var token = _tokenService.GenerateToken(claims, tokenExpiry);

        var response = new UploadTokenResponseDto
        {
            UploadToken = token,
            UploadUrl = Url.Action(nameof(UploadFile), "File", null, Request.Scheme) ?? "/api/file/upload"
        };

        return Ok(response);
    }

    [HttpPost("upload")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(UploadResponseDto), StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status413PayloadTooLarge)]
    [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
    [RequestSizeLimit(1024 * 1024 * 1024)]
    [RequestFormLimits(MultipartBodyLengthLimit = 1024 * 1024 * 1024)]
    public async Task<IActionResult> UploadFile([FromHeader(Name = "Upload-Token")][Required] string uploadToken)
    {
        var uploadTimestamp = DateTime.UtcNow;
        
        var claims = _tokenService.ValidateToken(uploadToken);
        if (claims == null)
        {
            _logger.LogWarning("Upload rejected: Invalid or expired upload token provided.");
            return Unauthorized(new ProblemDetails { Title = "Invalid or expired upload token.", Status = StatusCodes.Status401Unauthorized });
        }

        if (!Request.Form.Files.Any())
            return BadRequest(new ProblemDetails { Title = "No file found in the request.", Status = StatusCodes.Status400BadRequest });
        
        if (Request.Form.Files.Count > 1)
             return BadRequest(new ProblemDetails { Title = "Only single file uploads are supported.", Status = StatusCodes.Status400BadRequest });
         
        var file = Request.Form.Files[0];
        if (file.Length == 0)
        {
            return BadRequest(new ProblemDetails { Title = "Received empty file.", Status = StatusCodes.Status400BadRequest });
        }

        if (file.Length > claims.MaxFileSize)
        {
            _logger.LogWarning("Upload rejected for User {UserId}: File size {FileSize} exceeds limit {MaxFileSize} for purpose {Purpose}",
                claims.UserId, file.Length, claims.MaxFileSize, claims.Purpose);
            return StatusCode(StatusCodes.Status413PayloadTooLarge, new ProblemDetails
            {
                Title = "File exceeds maximum allowed size.",
                Detail = $"Maximum size allowed: {claims.MaxFileSize / 1024.0 / 1024.0:F1} MB.",
                Status = StatusCodes.Status413PayloadTooLarge
            });
        }

        // Note: Initial check based on declared type is still useful for quick rejection.
        var declaredMimeType = file.ContentType.ToLowerInvariant();
        if (!claims.AllowedMimeTypes.Contains(declaredMimeType))
        {
             _logger.LogWarning("Upload rejected for User {UserId}: Declared MIME type {DeclaredMimeType} not allowed for purpose {Purpose}. Allowed: {AllowedTypes}",
                claims.UserId, declaredMimeType, claims.Purpose, string.Join(", ", claims.AllowedMimeTypes));
            return StatusCode(StatusCodes.Status415UnsupportedMediaType, new ProblemDetails
            {
                Title = "Unsupported file type.",
                Detail = $"Declared MIME type {declaredMimeType} is not allowed for this purpose. Allowed types: {string.Join(", ", claims.AllowedMimeTypes)}.",
                Status = StatusCodes.Status415UnsupportedMediaType
            });
        }
        
        var fileId = Guid.NewGuid();
        var fileExtension = GetSanitizedFileExtension(file.FileName);
        var storagePath = GenerateStoragePath(claims.ContextEntityType, claims.ContextEntityId, fileId, fileExtension);
        
        var fileMetadata = FileMetadataDbo.Initial(
            id: fileId,
            originalFileName: file.FileName,
            storagePath: storagePath,
            uploadFileSize: file.Length,
            uploadTimestamp: uploadTimestamp,
            mimeType: declaredMimeType,
            userId: claims.UserId,
            contextEntityType: claims.ContextEntityType,
            contextEntityId: claims.ContextEntityId,
            purpose: claims.Purpose);
        
        // STEP 1: Save initial metadata to the database
        try
        {
            // We save the initial metadata record to the database so that if we fail later,
            // we can still clean up and track the upload attempt.
            _dbContext.Set<FileMetadataDbo>().Add(fileMetadata);
            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("Created FileMetadata record {FileId} for User {UserId}, Status: {Status}",
                fileMetadata.Id, fileMetadata.UserId, fileMetadata.Status);
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Failed to create FileMetadata record for potential upload {FileName} by User {UserId}. Upload aborted before storage.",
                fileMetadata.UploadFileName, fileMetadata.UserId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Failed to initiate upload process.",
                Detail = "Could not record file metadata.",
                Status = StatusCodes.Status500InternalServerError
            });
        }

        using var stream = file.OpenReadStream();
        
        // --- File Signature Validation ---
        // IMPORTANT: This validation reads the start of the stream. Ensure the stream is either
        // seekable OR handle the non-seekable case appropriately in the validation service.
        // The storage service needs the stream starting from the beginning.
        bool isSignatureValid = await _fileValidationService.IsSignatureValidAsync(stream, declaredMimeType, claims.AllowedMimeTypes);
        if (!isSignatureValid)
        {
            _logger.LogWarning("Upload rejected for User {UserId}: File signature validation failed or detected type not allowed for declared type {DeclaredMimeType}, purpose {Purpose}.",
                claims.UserId, declaredMimeType, claims.Purpose);
            fileMetadata.Status = FileStatus.ValidationFailed;
            fileMetadata.Failure = new JsonObject
            {
                ["error"] = "File signature validation failed or detected type not allowed.",
                ["declaredMimeType"] = declaredMimeType,
                ["allowedMimeTypes"] = new JsonArray(
                    claims.AllowedMimeTypes.Select(str => JsonValue.Create(str)).ToArray<JsonNode?>()),
            };
            fileMetadata.LastUpdatedAt = DateTime.UtcNow;
            _dbContext.Update(fileMetadata);
            await _dbContext.SaveChangesAsync();
            
            return StatusCode(StatusCodes.Status415UnsupportedMediaType, new ProblemDetails
            {
                Title = "Unsupported or invalid file type.",
                Detail = "File content does not match allowed types or is invalid.",
                Status = StatusCodes.Status415UnsupportedMediaType
            });
        }
        // At this point, we are reasonably sure the file content matches an allowed type.
        
        fileMetadata.Status = FileStatus.Uploading;
        fileMetadata.LastUpdatedAt = DateTime.UtcNow;
        _dbContext.Update(fileMetadata);
        await _dbContext.SaveChangesAsync();

        // STEP 2: Save the file to storage
        try
        {
            using (var streamToSave = file.OpenReadStream())
                await _storageService.SaveFileAsync(streamToSave, storagePath, declaredMimeType);

            // FIXME: Should first go through "Processing" before "Ready".
            fileMetadata.Status = FileStatus.Ready;
            _logger.LogInformation("Successfully uploaded file {FileId} to storage path {StoragePath}. Awaiting background processing.", fileMetadata.Id, storagePath);
        }
        catch (IOException ioEx)
        {
            _logger.LogError(ioEx, "Storage IO Error during upload for FileId {FileId}, Path {StoragePath}", fileMetadata.Id, storagePath);
            fileMetadata.Status = FileStatus.UploadFailed;
            fileMetadata.Failure = ioEx.ToJsonObject();
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Failed to save uploaded file.",
                Detail = ioEx.Message,
                Status = StatusCodes.Status500InternalServerError
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected Error during upload stream to storage for FileId {FileId}, Path {StoragePath}", fileMetadata.Id, storagePath);
            fileMetadata.Status = FileStatus.UploadFailed;
            fileMetadata.Failure = ex.ToJsonObject();
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Failed to save uploaded file.",
                Detail = ex.Message,
                Status = StatusCodes.Status500InternalServerError
            });
        }
        finally
        {
            fileMetadata.LastUpdatedAt = DateTime.UtcNow;
            _dbContext.Update(fileMetadata);
            await _dbContext.SaveChangesAsync();
        }
        
        _logger.LogInformation("File {FileId} uploaded successfully. Status: {Status}, Size: {FileSize} bytes, Path: {StoragePath}",
            fileMetadata.Id, fileMetadata.Status, fileMetadata.UploadFileSize, storagePath);

        var response = new UploadResponseDto
        {
            FileId = fileMetadata.Id,
            Status = fileMetadata.Status,
            OriginalFileName = fileMetadata.UploadFileName,
            FileSize = fileMetadata.UploadFileSize,
            MimeType = fileMetadata.UploadMimeType
        };
        return Accepted(response);
    }

    [HttpGet("status/{fileId:guid}")]
    [ProducesResponseType(typeof(FileStatusResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetFileStatus([FromRoute] Guid fileId)
    {
        var fileMetadata = await _dbContext.Set<FileMetadataDbo>().FindAsync(fileId);
        if (fileMetadata == null)
        {
            return NotFound(new ProblemDetails { Title = "File not found.", Status = StatusCodes.Status404NotFound });
        }

        var response = fileMetadata.ToDto();
        return Ok(response);
    }

    private async Task<bool> CheckUploadPermissionAsync(string userId, EntityType entityType, Guid entityId, string purpose)
    {
        _logger.LogWarning("STUB: Permission check skipped for User {UserId}, {EntityType}/{EntityId}/{Purpose}. Assuming authorized.",
            userId, entityType, entityId, purpose);
        await Task.Delay(10);
        return true;
    }

    private string GenerateStoragePath(EntityType entityType, Guid entityId, Guid fileId, string fileExtension)
    {
        var safeEntityType = SanitizePathPart(entityType.ToString());
        var safeEntityId = SanitizePathPart(entityId.ToString());
        var safeExtension = SanitizePathPart(fileExtension);
        return Path.Combine(safeEntityType, safeEntityId, $"{fileId}{safeExtension}").Replace('\\', '/');
    }

    private static readonly char[] InvalidPathOrFileNameChars = 
        Path.GetInvalidFileNameChars().Concat(Path.GetInvalidPathChars()).Distinct().ToArray();
    private static readonly ImmutableSortedSet<char> InvalidPathOrFileNameCharSet = 
        InvalidPathOrFileNameChars.ToImmutableSortedSet();
    
    private string GetSanitizedFileExtension(string pathPart)
    {
        if (string.IsNullOrWhiteSpace(pathPart)) return string.Empty;
        var lastDotIndex = pathPart.LastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == pathPart.Length - 1)
            return string.Empty;
        var extension = pathPart.Substring(lastDotIndex + 1);
        
        var sb = new StringBuilder();
        foreach (var c in extension)
        {
            if (InvalidPathOrFileNameCharSet.Contains(c))
                continue;
            // Allow only alphanumeric characters and a few special characters
            if (char.IsAsciiLetterOrDigit(c) || c == '-' || c == '_')
            {
                sb.Append(c);
            }
        }
        
        var sanitized = sb.ToString().Trim();
        return string.IsNullOrWhiteSpace(sanitized) ? string.Empty : ("." + sanitized);
    }
    
    private string SanitizePathPart(string pathPart)
    {
        if (string.IsNullOrWhiteSpace(pathPart)) return "_";
        
        var sanitized = pathPart.Replace("..", "");
        sanitized = string.Concat(sanitized.Split(InvalidPathOrFileNameChars, StringSplitOptions.RemoveEmptyEntries));
        sanitized = sanitized.Trim().Replace(" ", "_");
        return string.IsNullOrWhiteSpace(sanitized) ? "_" : sanitized;
    }

    private string SanitizeFileName(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName)) return "untitled";
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = string.Concat(fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        sanitized = sanitized.Trim();
        const int MaxLength = 100;
        if (sanitized.Length > MaxLength)
        {
            sanitized = sanitized.Substring(0, MaxLength);
        }
        return string.IsNullOrWhiteSpace(sanitized) ? "untitled" : sanitized;
    }
}