using System.Reflection;
using Ethos.Workflows.Api.Analysis;
using FluentAssertions;
using Xunit.Abstractions;

namespace Ethos.Workflows.Tests;


public class GeneratorSpec(ITestOutputHelper output)
{
    private static Generator.GenerationOptions genZeroOpts = new Generator.GenerationOptions
    {
        MaxNodes = 0,
        MaxDepth = 0,
    };

    public sealed record Bar
    {
        public required Foo? Foo { get; set; }
        public required string Name { get; set; }
    }
    public sealed record Foo
    {
        public required Bar Bar { get; set; }
        public required string Name { get; set; }
    }
    
    [Fact]
    public void GenerateZeroNodes()
    {
        for (int i = 0; i < 100; i++)
        {
            var r = Generator.Generate<Bar>(genZeroOpts);
            r.Should().NotBeNull();
            Helpers.ValidateInstance(r);
            output.WriteLine($"Generated: {r.Name} with Foo: {r.Foo}");
        }
        
        for (int i = 0; i < 100; i++)
        {
            var r = Generator.Generate<Foo>(genZeroOpts);
            r.Should().NotBeNull();
            Helpers.ValidateInstance(r);
            output.WriteLine($"Generated: {r.Name} with Bar: {r.Bar}");
        }
    }
}