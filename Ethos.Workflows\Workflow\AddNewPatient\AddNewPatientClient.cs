// THIS FILE IS AUTO‑GENERATED
// DO NOT EDIT

using System.Net;
using System.Net.Http.Json;
using Ethos.Model.Types;
using Ethos.Workflows.Api;
using Ethos.Workflows.Workflow.AddNewPatient;

namespace Ethos.Workflows.Workflow.AddNewPatient;

public sealed class AddNewPatientHttpClient
{
    private readonly System.Net.Http.HttpClient _http;
    public AddNewPatientHttpClient(System.Net.Http.HttpClient http) => _http = http;

    private static async Task<T> ReadOrThrowAsync<T>(HttpResponseMessage rsp)
    {
        if (rsp.IsSuccessStatusCode)
            return (await rsp.Content.ReadFromJsonAsync<T>())!;
        var txt = await rsp.Content.ReadAsStringAsync();
        throw new WorkflowApiException(rsp.StatusCode, txt);
    }
    
    private static async Task ThrowIfNotSuccessAsync(HttpResponseMessage rsp)
    {
        if (!rsp.IsSuccessStatusCode)
            throw new WorkflowApiException(rsp.StatusCode,
                                        await rsp.Content.ReadAsStringAsync());
    }

    public async Task<StartAddNewPatientResponse> StartAsync(StartAddNewPatientRequest body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<StartAddNewPatientResponse>(await _http.PostAsJsonAsync("api/add-new-patient/start", body, ct));
    }

    public async Task<AddNewPatientState?> GetStateAsync(Guid id, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<AddNewPatientState?>(await _http.GetAsync($"api/add-new-patient/state/{id}", ct));
    }

    public async Task RewindAsync(Guid id, string state, CancellationToken ct = default)
    {
        await ThrowIfNotSuccessAsync(await _http.PostAsync($"api/add-new-patient/rewind?id={id}&stateName={state}", null, ct));
    }

    public async Task<IReadOnlyList<Guid>> ListAsync(IReadOnlyDictionary<string, Guid>? links = null, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<IReadOnlyList<Guid>>(await _http.PostAsJsonAsync($"api/add-new-patient/list", links, ct));
    }

    public async Task<WorkflowResponse<AddNewPatientState.AddedBasicInformation>> AddBasicInformationAsync(WorkflowRequest<IAddNewPatientRequest.AddBasicInformation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewPatientState.AddedBasicInformation>>(await _http.PostAsJsonAsync($"api/add-new-patient/add-basic-information", body, ct));
    }

    public async Task<Guid> AddBasicInformationDraftAsync(DraftRequest<IAddNewPatientRequest.AddBasicInformation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-patient/add-basic-information/draft", body, ct));
    }

    public async Task<ValidationResult> AddBasicInformationValidateAsync(WorkflowRequest<IAddNewPatientRequest.AddBasicInformation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-patient/add-basic-information/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewPatientState.AddedContacts>> AddContactsAsync(WorkflowRequest<IAddNewPatientRequest.AddContacts> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewPatientState.AddedContacts>>(await _http.PostAsJsonAsync($"api/add-new-patient/add-contacts", body, ct));
    }

    public async Task<Guid> AddContactsDraftAsync(DraftRequest<IAddNewPatientRequest.AddContacts> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-patient/add-contacts/draft", body, ct));
    }

    public async Task<ValidationResult> AddContactsValidateAsync(WorkflowRequest<IAddNewPatientRequest.AddContacts> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-patient/add-contacts/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewPatientState.AddedAddresses>> AddAddressesAsync(WorkflowRequest<IAddNewPatientRequest.AddAddresses> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewPatientState.AddedAddresses>>(await _http.PostAsJsonAsync($"api/add-new-patient/add-addresses", body, ct));
    }

    public async Task<Guid> AddAddressesDraftAsync(DraftRequest<IAddNewPatientRequest.AddAddresses> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-patient/add-addresses/draft", body, ct));
    }

    public async Task<ValidationResult> AddAddressesValidateAsync(WorkflowRequest<IAddNewPatientRequest.AddAddresses> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-patient/add-addresses/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewPatientState.AddedInsurances>> AddInsurancesAsync(WorkflowRequest<IAddNewPatientRequest.AddInsurances> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewPatientState.AddedInsurances>>(await _http.PostAsJsonAsync($"api/add-new-patient/add-insurances", body, ct));
    }

    public async Task<Guid> AddInsurancesDraftAsync(DraftRequest<IAddNewPatientRequest.AddInsurances> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-patient/add-insurances/draft", body, ct));
    }

    public async Task<ValidationResult> AddInsurancesValidateAsync(WorkflowRequest<IAddNewPatientRequest.AddInsurances> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-patient/add-insurances/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewPatientState.AddedGuardians>> AddGuardiansAsync(WorkflowRequest<IAddNewPatientRequest.AddGuardians> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewPatientState.AddedGuardians>>(await _http.PostAsJsonAsync($"api/add-new-patient/add-guardians", body, ct));
    }

    public async Task<Guid> AddGuardiansDraftAsync(DraftRequest<IAddNewPatientRequest.AddGuardians> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-patient/add-guardians/draft", body, ct));
    }

    public async Task<ValidationResult> AddGuardiansValidateAsync(WorkflowRequest<IAddNewPatientRequest.AddGuardians> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-patient/add-guardians/validate", body, ct));
    }

    public async Task<WorkflowResponse<AddNewPatientState.Committed>> AddClinicalInformationAsync(WorkflowRequest<IAddNewPatientRequest.AddClinicalInformation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<WorkflowResponse<AddNewPatientState.Committed>>(await _http.PostAsJsonAsync($"api/add-new-patient/add-clinical-information", body, ct));
    }

    public async Task<Guid> AddClinicalInformationDraftAsync(DraftRequest<IAddNewPatientRequest.AddClinicalInformation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<Guid>(await _http.PostAsJsonAsync($"api/add-new-patient/add-clinical-information/draft", body, ct));
    }

    public async Task<ValidationResult> AddClinicalInformationValidateAsync(WorkflowRequest<IAddNewPatientRequest.AddClinicalInformation> body, CancellationToken ct = default)
    {
        return await ReadOrThrowAsync<ValidationResult>(await _http.PostAsJsonAsync($"api/add-new-patient/add-clinical-information/validate", body, ct));
    }

}
